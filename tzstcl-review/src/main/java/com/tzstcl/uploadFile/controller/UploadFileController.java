package com.tzstcl.uploadFile.controller;

import com.google.common.collect.Lists;
import com.tzstcl.common.annotation.Log;
import com.tzstcl.common.config.RuoYiConfig;
import com.tzstcl.common.core.controller.BaseController;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.core.domain.model.LoginUser;
import com.tzstcl.common.enums.BusinessType;
import com.tzstcl.common.utils.StringUtils;
import com.tzstcl.common.utils.file.FileUploadUtils;
import com.tzstcl.common.utils.file.MimeTypeUtils;
import com.tzstcl.department.controller.MyException;
import com.tzstcl.person.domain.PersonData;
import com.tzstcl.person.service.IPersonDataService;
import com.tzstcl.uploadFile.util.ImageWatermarkUtils;
import com.tzstcl.uploadFile.util.MinioUtils;
import io.minio.errors.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Array;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;

import static com.tzstcl.common.core.domain.AjaxResult.error;
import static com.tzstcl.common.utils.SecurityUtils.getLoginUser;

/**
 * 公司：天筑科技股份有限公司
 * 作者：wzh
 * 日期：XX年XX月XX日
 * 说明：
 */
@RestController
@RequestMapping("/uploadFile")
@Slf4j
public class UploadFileController {
    @Autowired
    private MinioUtils minioUtils;
    @Autowired
    private IPersonDataService personDataService;

    /**
     * 上传考生相关资料
     */
    @PostMapping("/up")
    @Log(title = "上传文件", businessType = BusinessType.INSERT)
    public AjaxResult up(MultipartFile file)throws Exception{
        //允许上传的文件格式
        String [] extensions = { "jpg", "jpeg", "png","pdf"};

        int flag = 0;
        for (String extension : extensions) {
            if(extension.equals(MinioUtils.getExtension(file))){
                flag=1;
            }
        }
        if(flag ==0){
            return AjaxResult.error("只允许上传图片");
        }
        if(file.getSize()>33554432L){
            throw new MyException("文件过大，请上传32Mb一下的文件");
        }




        if (file!=null)
        {
            return AjaxResult.success(minioUtils.uploadFile(file));
        }
        return error("上传异常，请联系管理员");
    }
    /**
     * 上传文件删除
     */
    @GetMapping("/delUp")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "上传文件删除", businessType = BusinessType.DELETE)
    public  AjaxResult delUp(String url){
        //删除考生资料表中数据文件
        int i = 0;
        if (StringUtils.isNotEmpty(url)){
             i = personDataService.deletePersonDataByPath(url);
        }
        if (i>0){
//            minioUtils.deleteFile(url);
            return AjaxResult.success("删除成功",url);
        }
        if (i==0){
            //没保存到数据库的文件才进行删除
            minioUtils.deleteFile(url);
            return AjaxResult.success("删除成功",url);
        }

        return AjaxResult.success("删除失败",url);
    }
    /**
     * 下载
     */
    @GetMapping("/download")
    public void download(String path, HttpServletResponse response, HttpServletRequest request){
        try {
            minioUtils.download(path,response,request);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        //return minioUtils.getPreviewFileUrl(path);
    }


      /**
     * 下载
     */
    @PostMapping("/importTemplate")
    public void downloadTemplate(@RequestParam("path") String path, HttpServletResponse response, HttpServletRequest request){
        try {
            minioUtils.download(path,response,request);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        //return minioUtils.getPreviewFileUrl(path);
    }



}
