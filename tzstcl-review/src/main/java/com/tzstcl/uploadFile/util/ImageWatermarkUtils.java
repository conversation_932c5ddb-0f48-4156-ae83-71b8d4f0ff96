package com.tzstcl.uploadFile.util;


import javax.imageio.ImageIO;
import java.awt.AlphaComposite;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

/**
 * 定义文字加水印
 */
public class ImageWatermarkUtils {

    // 水印字体
    private static final Font FONT = new Font("微软雅黑", Font.PLAIN, 14);

    private static final String WATERMARK_CONTENT = "仅用于河南省建设类职业资格考试审核";

    // 透明度
    private static final AlphaComposite COMPOSITE = AlphaComposite
            .getInstance(AlphaComposite.SRC_OVER, 0.6f);

    // 水印之间的间隔
    private static final int X_MOVE = 150;

    // 水印之间的间隔
    private static final int Y_MOVE = 200;

    /**
     * 打水印(文字)
     *
     * @param inputStream       输入流
     * @param suffix            后缀
     */
    public static File markWithContent(InputStream inputStream,String suffix) {
        FileOutputStream fos = null;
        try {
            // 读取原图片信息
            File outFile = new File("tmp"+suffix);
            BufferedImage srcImg = ImageIO.read(inputStream);
            // 图片宽、高
            int imgWidth = srcImg.getWidth();
            int imgHeight = srcImg.getHeight();
            // 图片缓存
            BufferedImage bufImg = new BufferedImage(imgWidth, imgHeight, BufferedImage.TYPE_INT_RGB);
            // 创建绘图工具
            Graphics2D graphics = bufImg.createGraphics();
            // 画入原始图像
            graphics.drawImage(srcImg, 0, 0, imgWidth, imgHeight, null);
            // 设置水印颜色
            graphics.setColor(Color.darkGray);
            // 设置水印透明度
            graphics.setComposite(COMPOSITE);
            // 设置倾斜角度
            graphics.rotate(Math.toRadians(-35), (double) bufImg.getWidth() / 2, (double) bufImg.getHeight() / 2);
            // 设置水印字体
            graphics.setFont(FONT);
            // 消除java.awt.Font字体的锯齿
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            int xCoordinate = -imgWidth / 2, yCoordinate;
            // 字体长度
            int markWidth = FONT.getSize() * getTextLength(WATERMARK_CONTENT);
            // 字体高度
            int markHeight = FONT.getSize();
            // 循环添加水印
            while (xCoordinate < imgWidth * 1.5) {
                yCoordinate = -imgHeight / 2;
                while (yCoordinate < imgHeight * 1.5) {
                    graphics.drawString(WATERMARK_CONTENT, xCoordinate, yCoordinate);
                    yCoordinate += markHeight + Y_MOVE;
                }
                xCoordinate += markWidth + X_MOVE;
            }
            // 释放画图工具
            graphics.dispose();
            // 输出图片
            fos = new FileOutputStream(outFile);
            ImageIO.write(bufImg, suffix, fos);
            return outFile;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fos != null) {
                try {
                    fos.flush();
                    fos.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }


    //计算水印文本长度
    //1、中文长度即文本长度 2、英文长度为文本长度二分之一
    public static int getTextLength(String text) {
        //水印文字长度
        int length = text.length();

        for (int i = 0; i < text.length(); i++) {
            String s = String.valueOf(text.charAt(i));
            if (s.getBytes().length > 1) {
                length++;
            }
        }
        length = length % 2 == 0 ? length / 2 : length / 2 + 1;
        return length;
    }


}
