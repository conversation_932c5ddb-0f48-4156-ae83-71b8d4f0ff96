package com.tzstcl.uploadFile.util;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.tzstcl.common.utils.DateUtils;
import com.tzstcl.common.utils.SecurityUtils;
import com.tzstcl.common.utils.SnowflakeIdWorker;
import com.tzstcl.common.utils.StringUtils;
import com.tzstcl.common.utils.file.MimeTypeUtils;
import com.tzstcl.common.utils.uuid.IdUtils;
import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.poi.ss.usermodel.DateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Encoder;

import javax.annotation.PostConstruct;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.awt.Font;

/**
 * 公司：天筑科技股份有限公司
 * 作者：wzh
 * 日期：XX年XX月XX日
 * 说明：
 */
@Component
@Slf4j
public class MinioUtils {
    private static MinioClient minioClient;

    @Value("${minio.url}")
    private String url; //= "http://127.0.0.1:9000";
    @Value("${minio.accessKey}")
    private String accessKey;// = "minioadmin";
    @Value("${minio.secretKey}")
    private String secretKey;//= "minioadmin";
    @Value("${minio.bucket}")
    private String bucketName;//= "review";

    /**
     * 创建桶
     */
    @SneakyThrows(Exception.class)
    public static void createBucket(String bucketName) {
        boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        if (!isExist) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
        }
    }

    /**
     * 判断桶是否存在
     */
    @SneakyThrows(Exception.class)
    public static boolean bucketExists(String bucketName) {
        return minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
    }

    /**
     * 获取所有的桶
     */
    @SneakyThrows
    public List<Bucket> listBuckets() {
        return minioClient.listBuckets();
    }

    /**
     * 上传文件
     * 返回可以直接预览文件的URL
     */
    public String uploadFile(MultipartFile file) {
        try {
            //如果存储桶不存在则创建
            if (!bucketExists(bucketName)) {
                createBucket(bucketName);
            }
            PutObjectOptions putObjectOptions = new PutObjectOptions(file.getInputStream().available(), -1);
            putObjectOptions.setContentType(file.getContentType());
            String originalFilename = file.getOriginalFilename();
            //得到文件流
            InputStream inputStream = file.getInputStream();
            //判断文件名称是否包括后缀
            if (!originalFilename.substring(originalFilename.lastIndexOf(".") + 1).equals(getExtension(file))) {
                originalFilename = originalFilename + "." + getExtension(file);
            }
            //保证文件不重名(并且没有特殊字符)
            String fileName = SnowflakeIdWorker.getInstance().nextId() + "." + getExtension(file);
            //获取filename小数点的位置
            int i = fileName.indexOf(".");
            //如果上传的文件是图片加水印
            String[] typeArr = {"jpg", "jpeg", "png"};
            List<String> types = Arrays.asList(typeArr);
            if (types.contains(MinioUtils.getExtension(file))) {
                File waterPic = ImageWatermarkUtils.markWithContent(file.getInputStream(), MinioUtils.getExtension(file));
                FileInputStream stream = new FileInputStream(waterPic);
                minioClient.putObject(PutObjectArgs.builder().bucket(bucketName).object(fileName).stream(stream, stream.available(), -1).build());

            } else {
                minioClient.putObject(PutObjectArgs.builder().bucket(bucketName).object(fileName).stream(inputStream, inputStream.available(), -1).build());
            }

            return fileName;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取文件名的后缀
     *
     * @param file 表单文件
     * @return 后缀名
     */
    public static final String getExtension(MultipartFile file) {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (StringUtils.isEmpty(extension)) {
            extension = MimeTypeUtils.getExtension(Objects.requireNonNull(file.getContentType()));
        }
        return extension;
    }

    /**
     * 删除文件
     *
     * @param fileName: 文件名
     */
    @SneakyThrows(Exception.class)
    public void deleteFile(String fileName) {
        minioClient.removeObject(RemoveObjectArgs.builder().bucket(bucketName).object(fileName).build());
    }

    /**
     * 获取minio文件的下载或者预览地址
     * 取决于调用本方法的方法中的PutObjectOptions对象有没有设置contentType
     *
     * @param fileName: 文件名
     */
    @SneakyThrows(Exception.class)
    public String getPreviewFileUrl(String fileName) {
        return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder().bucket(bucketName).object(fileName).method(Method.GET).build());
    }

    /**
     * 初始化minio配置
     */
    @PostConstruct
    public void init() {
        try {
            log.info("Minio Initialize........................");
            minioClient = MinioClient.builder().endpoint(url).credentials(accessKey, secretKey).build();
            createBucket(bucketName);
            log.info("Minio Initialize........................successful");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("初始化minio配置异常: 【{}】", e.fillInStackTrace());
        }
    }


    /**
     * 通过文件名称下载文件
     */
    public void download(String filename, HttpServletResponse res, HttpServletRequest req) throws IOException, InternalException,
            ServerException, InsufficientDataException, ErrorResponseException, NoSuchAlgorithmException,
            InvalidKeyException, InvalidResponseException, XmlParserException, InvalidBucketNameException {
        BucketExistsArgs bucketArgs = BucketExistsArgs.builder().bucket(bucketName).build();
        minioClient.bucketExists(bucketArgs);
        //获取filename小数点的位置
        int i = filename.indexOf(".");
        GetObjectArgs objectArgs = GetObjectArgs.builder().bucket(bucketName)
                .object(filename).build();
        try (InputStream response = minioClient.getObject(objectArgs)) {
            byte[] buf = new byte[1024];
            int len;
            try (FastByteArrayOutputStream os = new FastByteArrayOutputStream()) {
                while ((len = response.read(buf)) != -1) {
                    os.write(buf, 0, len);
                }
                os.flush();
                byte[] bytes = os.toByteArray();
                res.setCharacterEncoding("utf-8");
                res.setContentType("application/octet-stream;charset=UTF-8");
                res.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
//                filenameEncoding 方法兼容不同浏览器编码
                res.addHeader("Content-Disposition", "attachment;fileName=" + filenameEncoding(filename, req));
                try (ServletOutputStream stream = res.getOutputStream()) {
                    stream.write(bytes);
                    stream.flush();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置不同浏览器编码
     *
     * @param filename 文件名称
     * @param request  请求对象
     */
    public static String filenameEncoding(String filename, HttpServletRequest request) throws UnsupportedEncodingException {
        // 获得请求头中的User-Agent
        String agent = request.getHeader("User-Agent");
        // 根据不同的客户端进行不同的编码

        if (agent.contains("MSIE")) {
            // IE浏览器
            filename = URLEncoder.encode(filename, "utf-8");
        } else if (agent.contains("Firefox")) {
            // 火狐浏览器
            BASE64Encoder base64Encoder = new BASE64Encoder();
            filename = "=?utf-8?B?" + base64Encoder.encode(filename.getBytes("utf-8")) + "?=";
        } else {
            // 其它浏览器
            filename = URLEncoder.encode(filename, "utf-8");
        }
        return filename;
    }

    public static void main(String[] args) throws InterruptedException {
        for (int i = 0; i < 10; i++) {

            String x = String.valueOf(SnowflakeIdWorker.getInstance().nextId());

            System.out.println(x);
            Thread.sleep(1000);
        }
    }

}
