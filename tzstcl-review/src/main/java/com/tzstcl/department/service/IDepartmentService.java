package com.tzstcl.department.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.tzstcl.audit.domain.Audit;
import com.tzstcl.common.core.domain.entity.SysDept;
import com.tzstcl.common.core.page.TableDataInfo;
import com.tzstcl.department.controller.MyException;
import com.tzstcl.person.domain.PersonInfo;
import com.tzstcl.person.domain.PersonInfoExport;
import net.sf.jsqlparser.schema.Table;
import org.springframework.security.core.parameters.P;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：wzh
 * 日期：XX年XX月XX日
 * 说明：
 */
public interface IDepartmentService {
    /**
     * 首页数据回显
     */
    JSONObject getData(String yearInfo,Integer candidateType) throws MyException;

    /**
     * 获取该市的,考生总数，未审核，已审核，未提交审核
     */
    JSONObject getDataByCity(List<PersonInfo> personInfoList)throws MyException;

    /**
     * 考生信息查看
     */
    List<PersonInfo> getPersonInfo(PersonInfo personInfo)throws MyException;

    /**
     * 考生审核页面
     * @param  // 0全部,1未审核,2已审核,3未参加审核
     */
    List<PersonInfo> auditList(PersonInfo personInfo)throws MyException;

    /**
     * 审核
     * @param audit
     * @return
     */
    String audit(Audit audit)throws MyException;
    String deptAudit(Audit audit)throws MyException;

    /**
     * 考生审核信息导出
     */
   List<PersonInfoExport> exportData(PersonInfo personInfo)throws MyException;

    String reduction(Audit audit);

    String adminaudit(Audit audit) throws MyException;

    List<PersonInfo> selectauditList(PersonInfo personInfo) throws MyException;
    List<PersonInfo> selectauditListExport(PersonInfo personInfo, SysDept dept) throws MyException;

    JSONObject getDataInfo(String yearInfo, Integer candidateType);
    JSONObject getDataInfoIndex(String yearInfo, Integer candidateType);

    List<PersonInfoExport> exportData1(PersonInfo personInfo) throws MyException;

    String banRegister(PersonInfo personInfo);


    String banRegisterStatus(String id, Integer registerStatus);

    String sendSms(PersonInfo personInfo) throws MyException;

    /**
     * 主管部门修改考生审核资料
     * @param array
     * @param candidateType
     * @return
     */
    String update(JSONArray array, Integer candidateType,String personId) throws MyException;
}
