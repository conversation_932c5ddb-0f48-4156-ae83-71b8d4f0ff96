package com.tzstcl.department.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import com.tzstcl.audit.domain.Audit;
import com.tzstcl.audit.domain.AuditRecord;
import com.tzstcl.audit.mapper.AuditMapper;
import com.tzstcl.audit.mapper.AuditRecordMapper;
import com.tzstcl.audit.service.IAuditRecordService;
import com.tzstcl.audit.service.IAuditService;
import com.tzstcl.common.annotation.DataScope;
import com.tzstcl.common.core.domain.entity.SysDept;
import com.tzstcl.common.core.domain.entity.SysRole;
import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.common.core.page.TableDataInfo;
import com.tzstcl.common.enums.AuditStatusEnum;
import com.tzstcl.common.enums.DeptTypeEnum;
import com.tzstcl.common.enums.UserTypeEnum;
import com.tzstcl.common.utils.*;
import com.tzstcl.common.utils.poi.ExcelUtil;
import com.tzstcl.company.domain.Company;
import com.tzstcl.company.mapper.CompanyMapper;
import com.tzstcl.department.controller.MyException;
import com.tzstcl.department.service.IDepartmentService;
import com.tzstcl.person.domain.ExamInfo;
import com.tzstcl.person.domain.PersonData;
import com.tzstcl.person.domain.PersonInfo;
import com.tzstcl.person.domain.PersonInfoExport;
import com.tzstcl.person.mapper.PersonDataMapper;
import com.tzstcl.person.mapper.PersonInfoMapper;
import com.tzstcl.person.service.IExamInfoService;
import com.tzstcl.person.service.IPersonDataService;
import com.tzstcl.person.service.IPersonInfoService;
import com.tzstcl.system.mapper.SysUserMapper;
import com.tzstcl.system.service.ISysDeptService;
import com.tzstcl.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.xmlbeans.UserType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.tzstcl.common.utils.PageUtils.startPage;

/**
 * 公司：天筑科技股份有限公司
 * 作者：wzh
 * 日期：XX年XX月XX日
 * 说明：
 */
@Slf4j
@Service
public class DepartmentServiceImpl implements IDepartmentService {

    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private IPersonInfoService personInfoService;
    @Autowired
    private IAuditService auditService;
    @Autowired
    private IAuditRecordService auditRecordService;

    @Autowired
    private AuditRecordMapper auditRecordMapper;
    @Autowired
    private IExamInfoService examInfoService;
    @Autowired
    private PersonInfoMapper personInfoMapper;
    @Autowired
    private PersonDataMapper personDataMapper;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private AuditMapper auditMapper;
    @Autowired
    private CompanyMapper companyMapper;

    private static final long codeStr = 410000;
    //设置特殊处理值
    private static final long userId = 1;

    private static final long deptId = 410005; //如果是厅建筑监管处查看全部的

    /**
     * 首页数据回显
     */
    @Override
    public JSONObject getData(String yearInfo, Integer candidateType) throws MyException {
        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
        if (userType == null || UserTypeEnum.COMPANY.getCode().equals(userType)
                || UserTypeEnum.EXAMINEE.getCode().equals(userType)) {
            throw new MyException("没有权限");
        }

        List<PersonInfo> personInfoList = new ArrayList<>();
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        SysDept dept = new SysDept();
        dept.setParentId(codeStr);
        dept.setDeptType(DeptTypeEnum.CITY.getCode());
        JSONObject jsonObject = new JSONObject();
        //省内的所有主管部门
        List<SysDept> sysDepts = sysDeptService.selectDeptList(dept);
        if (SecurityUtils.getUserId().equals(userId) || sysUser.getDeptId() != null) {
            SysDept sysDept = sysDeptService.selectDeptById(sysUser.getDeptId());
            if (SecurityUtils.getUserId().equals(userId) || sysUser.getDeptId().equals(deptId)) {//如果是厅建筑监管处查看全部的
                PersonInfo personInfo = new PersonInfo();
                personInfo.setYearInfo(yearInfo);
                personInfo.setCandidateType(candidateType);
                personInfoList = personInfoService.selectPersonInfoList(personInfo);
                jsonObject = getDataByCity(personInfoList);
                JSONArray cityData = new JSONArray();
                for (SysDept sysDept1 : sysDepts) {
                    sysDept1.setYearInfo(yearInfo);
                    sysDept1.setCandidateType(candidateType);
                    personInfoList = personInfoService.selectByDept(sysDept1);
                    JSONObject object = getDataByCity(personInfoList);
                    object.put("cityName", sysDept1.getRemark());
                    cityData.add(object);
                }
                jsonObject.put("cityData", cityData);
            } else {//地市查看所属下的
                personInfoList = personInfoService.selectByDept(sysDept);
                jsonObject = getDataByCity(personInfoList);
                jsonObject.put("cityName", sysDept.getRemark());
            }
        }
        return jsonObject;
    }


    @Override
    public JSONObject getDataInfo(String yearInfo, Integer candidateType) {
        PersonInfo personInfo = new PersonInfo();
        personInfo.setYearInfo(yearInfo);
        personInfo.setCandidateType(candidateType);
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        SysDept sysDept = sysDeptService.selectDeptById(sysUser.getDeptId());
        SysDept dept = new SysDept();
        dept.setParentId(codeStr);
        dept.setDeptType(DeptTypeEnum.CITY.getCode());
        List<SysDept> sysDepts = sysDeptService.selectDeptList(dept);
        JSONObject jsonObject = new JSONObject();
        Long userId1 = SecurityUtils.getUserId();
        if (userId1.equals(userId) || sysUser.getDeptId().equals(deptId)) {
            List<PersonInfo> personInfos = personInfoService.selectPersonInfoListsIndex(personInfo);
            jsonObject = generateAuditStatusSummary(personInfos);
            JSONArray cityData = new JSONArray();
            for (SysDept sysDept1 : sysDepts) {
                List<PersonInfo> collect = personInfos.stream().filter(item -> item != null && item.getCityName() != null && item.getCityName().equals(sysDept1.getRemark())).collect(Collectors.toList());
                JSONObject object = generateAuditStatusSummary(collect);
                object.put("cityName", sysDept1.getRemark());
                cityData.add(object);
            }
            jsonObject.put("cityData", cityData);
        } else if ("2".equals(dept.getDeptType())) {
            personInfo.setCityName(sysDept.getRemark());
            personInfo.setInputType(1);
            List<PersonInfo> personInfos = personInfoService.selectPersonInfoListsIndex(personInfo);
            jsonObject = generateAuditStatusSummary(personInfos);
            jsonObject.put("cityName", sysDept.getRemark());
        }

        return jsonObject;
    }


    @Override
    public JSONObject getDataInfoIndex(String yearInfo, Integer candidateType) {
        PersonInfo personInfo = new PersonInfo();
        personInfo.setYearInfo(yearInfo);
        personInfo.setCandidateType(candidateType);

        SysDept dept = new SysDept();
        dept.setParentId(codeStr);
        dept.setDeptType(DeptTypeEnum.CITY.getCode());
        List<SysDept> sysDepts = sysDeptService.selectDeptList(dept);


        JSONObject jsonObject = new JSONObject();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long deptId1 = user.getDeptId();
        String userType = user.getUserType();
        SysDept sysDept = sysDeptService.selectDeptById(deptId1);
        if ("00".equals(userType) || "01".equals(userType)) {
            jsonObject = personInfoService.generateAuditStatusIndex(personInfo);
            JSONArray cityData = new JSONArray();
            for (SysDept sysDept1 : sysDepts) {
                personInfo.setCityName(sysDept1.getRemark());
                JSONObject object = personInfoService.generateAuditStatusIndex(personInfo);
                object.put("cityName", sysDept1.getRemark());
                cityData.add(object);
            }
            jsonObject.put("cityData", cityData);
        } else if ("04".equals(userType)) {
            // 创建审核账号需修改 市级账号展示的数据根据客户需要 统计固定资质的考生数据，(service中已判断)


            jsonObject = personInfoService.generateAuditStatusIndex(personInfo);
            jsonObject.put("cityName", sysDept.getRemark());
        }
        return jsonObject;
    }


//    @Override
//    public JSONObject getDataInfoIndex(String yearInfo, Integer candidateType) {
//        PersonInfo personInfo = new PersonInfo();
//        personInfo.setYearInfo(yearInfo);
//        personInfo.setCandidateType(candidateType);
//
//        SysDept dept = new SysDept();
//        dept.setParentId(codeStr);
//        dept.setDeptType(DeptTypeEnum.CITY.getCode());
//        List<SysDept>  sysDepts = sysDeptService.selectDeptList(dept);
//
//
//        JSONObject jsonObject = new JSONObject();
//        Long userId1 = SecurityUtils.getUserId();
//        Long deptId1 = SecurityUtils.getLoginUser().getUser().getDeptId();
//        SysDept sysDept = sysDeptService.selectDeptById(deptId1);
//        if(userId1.equals(userId) || deptId1.equals(deptId)) {
//            jsonObject = generateAuditStatusIndex(personInfo);
//            JSONArray cityData = new JSONArray();
//            for (SysDept sysDept1 : sysDepts) {
//                personInfo.setCityName(sysDept1.getRemark());
//                JSONObject object = generateAuditStatusIndex(personInfo);
//                object.put("cityName",sysDept1.getRemark());
//                cityData.add(object);
//            }
//            jsonObject.put("cityData",cityData);
//        }else if ("2".equals(dept.getDeptType())){
//            personInfo.setCityName(sysDept.getRemark());
//            personInfo.setInputType(1);
//            personInfo.setCandidateType(16);
//            jsonObject = generateAuditStatusIndex(personInfo);
//            jsonObject.put("cityName",sysDept.getRemark());
//        }
//        return  jsonObject;
//    }


    public JSONObject generateAuditStatusIndex(PersonInfo personInfo) {
        JSONObject jsonObject = new JSONObject();
        Integer sum = personInfoMapper.selectPersonInfoListsCount(personInfo);
        personInfo.setAuditStatus(2);
        Integer noAudit = personInfoMapper.selectPersonInfoListsCount(personInfo);
        personInfo.setAuditStatus(3);
        Integer yesAudit = personInfoMapper.selectPersonInfoListsCount(personInfo);
        personInfo.setAuditStatus(4);
        Integer auditnotpassed = personInfoMapper.selectPersonInfoListsCount(personInfo);
        personInfo.setAuditStatus(null);
        personInfo.setAuditResult(0);
        Integer noAdd = personInfoMapper.selectPersonInfoListsCount(personInfo);

        jsonObject.put("sum", sum);//考生人数
        jsonObject.put("noAudit", noAudit);//未审核
        jsonObject.put("yesAudit", yesAudit);//已审核
        jsonObject.put("noAdd", noAdd);//未参加审核
        jsonObject.put("auditnotpassed", auditnotpassed);//主管部门审核不通过

        return jsonObject;
    }


    public JSONObject generateAuditStatusSummary(List<PersonInfo> personInfos) {
        JSONObject jsonObject = new JSONObject();

        if (personInfos != null && CollectionUtils.isNotEmpty(personInfos)) {
            int noAudit = personInfos.stream().filter(item -> item != null && item.getAuditListStatus() != null &&
                    (item.getAuditListStatus() == 2 || item.getAuditListStatus() == 5)).collect(Collectors.toList()).size();
            int yesAudit = personInfos.stream().filter(item -> item != null && item.getAuditListStatus() != null &&
                    item.getAuditListStatus() == 3).collect(Collectors.toList()).size();
            int auditnotpassed = personInfos.stream().filter(item -> item != null && item.getAuditListStatus() != null &&
                    item.getAuditListStatus() == 4).collect(Collectors.toList()).size();
            int noAdd = personInfos.stream().filter(item -> item != null && item.getAuditListStatus() == null).collect(Collectors.toList()).size();


            jsonObject.put("sum", personInfos.size());//考生人数
            jsonObject.put("noAudit", noAudit);//未审核
            jsonObject.put("yesAudit", yesAudit);//已审核
            jsonObject.put("noAdd", noAdd);//未参加审核
            jsonObject.put("auditnotpassed", auditnotpassed);//主管部门审核不通过
        }

        return jsonObject;
    }


    /**
     * 获取该市的,考生总数，未审核，已审核，未提交审核
     */
    @Override
    public JSONObject getDataByCity(List<PersonInfo> personInfoList) {
        JSONObject jsonObject = new JSONObject();
        long noAudit = 0, yesAudit = 0, noAdd = 0, other = 0, auditnotpassed = 0, waitcorpaudit = 0, otaddaudither = 0;
        if (personInfoList != null && personInfoList.size() > 0) {
            Map<Integer, Long> personInfoMap = personInfoList.stream().collect(Collectors.groupingBy(PersonInfo::getAuditStatus, Collectors.counting()));
            for (Map.Entry<Integer, Long> entryMap : personInfoMap.entrySet()) {
                Integer auditStatus = entryMap.getKey();
                Long auditNum = entryMap.getValue();
                if (auditStatus.equals(AuditStatusEnum.NO_ADD_AUDIT.getCode())) {
                    noAdd = auditNum;
                }
                if (auditStatus.equals(AuditStatusEnum.NO_AUDIT.getCode())) {
                    noAudit = auditNum;
                }
                if (auditStatus.equals(AuditStatusEnum.AUDITED.getCode())) {
                    yesAudit = auditNum;
                }
                if (auditStatus.equals(AuditStatusEnum.OTHER.getCode())) {
                    other = auditNum;
                }
                if (auditStatus.equals(AuditStatusEnum.AUDIT_NOT_PASSED.getCode())) {
                    auditnotpassed = auditNum;
                }
                if (auditStatus.equals(AuditStatusEnum.WAIT_CORP_AUDIT.getCode())) {
                    waitcorpaudit = auditNum;
                }
                if (!auditStatus.equals(AuditStatusEnum.NO_ADD_AUDIT.getCode())) {
                    otaddaudither += auditNum;
                }
            }
//            for (PersonInfo personInfo : personInfoList) {
//                personInfo = personInfoService.getInfoById(personInfo.getId());
//                if (personInfo.getAuditList().size()==0) {//筛选出提交过审核的
//                    noAdd += 1;
//                } else {
//                    if (personInfo.getAuditList().get(0).getAuditStatus() == 2) {//筛选出待审核
//                        noAudit += 1;
//                    } else {
//                        if (personInfo.getAuditList().get(0).getAuditType()!=null && personInfo.getAuditList().get(0).getAuditType() == 1) {
//                            yesAudit += 1;
//                        }
//                    }
//                }
//            }
        }
        jsonObject.put("sum", personInfoList.size());//考生人数
        jsonObject.put("noAudit", noAudit);//未审核
        jsonObject.put("yesAudit", yesAudit);//已审核
        jsonObject.put("noAdd", noAdd);//未参加审核
        jsonObject.put("other", other);
        jsonObject.put("auditnotpassed", auditnotpassed);//主管部门审核不通过
        jsonObject.put("waitcorpaudit", waitcorpaudit);//待企业审核
        jsonObject.put("otaddaudither", otaddaudither);//已参加审核
        return jsonObject;
    }

    /**
     * 考生信息列表
     */
    @Override
    public List<PersonInfo> getPersonInfo(PersonInfo person) throws MyException {
        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
        if (userType == null || "02".equals(userType) || "03".equals(userType)) {
            throw new MyException("没有权限");
        }
        //获取所有考生的身份证号
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();

        SysDept dept = sysDeptService.selectDeptById(sysUser.getDeptId());
        if (!Objects.isNull(dept)) {
            String cityName = dept.getRemark();
            if (cityName != null) {
                if (cityName.contains("市")) {
                    person.setCityName(cityName);
                }
            }
        }
//        if(SecurityUtils.getUserId()==1||sysUser.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList()).contains("dept")){
        if (SecurityUtils.getUserId() == 14141313 || userType.equals("01") || userType.equals("04")) {
            //只有厅建筑监管处和管理员可看到此列表
            startPage();
            List<PersonInfo> results = personInfoService.selectPersonInfoList(person);
            results.forEach(result -> {
                result.setAddressExam("41".equals(result.getAddressExam()) ? "河南省" : result.getAddressExam());
                //考生的考试项目信息
                ExamInfo examInfo = new ExamInfo();
                examInfo.setIdCard(result.getIdCard());
                List<ExamInfo> examInfoList = examInfoService.selectExamInfoList(examInfo);
                result.setExamInfoList(examInfoList);
                //考生审批记录
                List<Audit> auditList = auditService.getByIdCard(result.getIdCard());
                result.setAuditList(auditList);
            });
            return results;

        }
        return new ArrayList<>();
    }

    /**
     * 考生审核页面
     *
     * @param // 0全部,1未审核,2已审核,3未参加审核
     */
    @Override
    public List<PersonInfo> auditList(PersonInfo personInfo) throws MyException {
        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
        if (userType == null || "02".equals(userType) || "03".equals(userType)) {
            throw new MyException("没有权限");
        }

        Long deptId = SecurityUtils.getDeptId();
        //考生的身份证号List
        List<PersonInfo> personInfoList = new ArrayList<>();
        //
        List<PersonInfo> tempList = new ArrayList<>();
        //最后返回结果
        List<PersonInfo> resultList = new ArrayList<>();


        SysDept dept = sysDeptService.selectDeptById(deptId);
        if (!Objects.isNull(dept)) {
            String cityName = dept.getRemark();
            if (cityName.contains("市")) {
                personInfo.setCityName(cityName);
            }
        }

        if (deptId != null || SecurityUtils.getUserId() == 14141313) {
            if (SecurityUtils.getUserId() == 14141313 || deptId == 410005L) {//厅建筑市场处
                personInfoList = personInfoService.selectPersonInfoList(personInfo);
            } else {
                if ("2".equals(dept.getDeptType())) {
                    personInfo.setDeptId(dept.getDeptId().toString());
                    personInfo.setDeptName(dept.getRemark());
                    personInfoList = personInfoService.getByDept(personInfo);
                }
            }
            if (personInfoList.size() > 0) {
//                for (PersonInfo info : personInfoList) {
//                    tempList.add(personInfoService.getInfoById1(info.getId()));
//                }
                //流处理personInfoList查询数据添加到tempList
                personInfoList.stream().forEach(personInfo1 -> {
                    tempList.add(personInfoService.getInfoById1(personInfo1.getId()));
                });


            }
            if (tempList.size() > 0) {
                if (personInfo.getSelect() != null) {
                    if (personInfo.getSelect() == 0) {
                        resultList.addAll(tempList);
                    }
                    for (PersonInfo info : tempList) {

                        if (info.getAuditList().size() != 0) {
                            if (personInfo.getSelect() == 1) {//到达主管部门端
                                if (info.getAuditList().get(0).getAuditStatus() == 2 || info.getAuditList().get(0).getAuditStatus() == 5) {
                                    resultList.add(info);
                                }
                            }
                            if (personInfo.getSelect() == 2) {//审核通过
                                if (info.getAuditList().get(0).getAuditStatus() == 3) {
                                    resultList.add(info);
                                }
                            }

                            if (personInfo.getSelect() == 3) {//说明审核不通过
                                if (info.getAuditList().get(0).getAuditStatus() == 4) {
                                    resultList.add(info);
                                }
                            }
                            if (personInfo.getSelect() == 4) { //说明已提交
                                if (info.getAuditList().get(0).getAuditStatus() == 0) {
                                    resultList.add(info);
                                }
                            }
                            if (personInfo.getSelect() == 5) {//有审核列表说明已注册 算参加
                                resultList.add(info);
                            }


                        }
                        if (personInfo.getSelect() == 6) {//没审核列表说明没注册 算没参加
                            if (null == info.getAuditList() || info.getAuditList().size() == 0) {
                                resultList.add(info);
                            }
                        }

                    }
                }
            }
        }
        return resultList;
    }


    public List<PersonInfo> selectByBatchAndType(SysDept sysDept, PersonInfo personInfo) throws MyException {

        if (sysDept != null && "一级注册造价师审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(17);
            personInfo.setCandidateType(19);
        }

        if (sysDept != null && "二级注册造价师审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(18);
            personInfo.setCandidateType(20);
        }

        //新增注册土木工程师（岩土）审核部门
        if (sysDept != null && "注册土木工程师（岩土）审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(19);
            personInfo.setCandidateType(6);
        }

        //新增注册土木工程师（岩土）审核部门
        if (sysDept != null && "注册土木工程师(水利水电工程)审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(19);
            personInfo.getCandidateTypeList().add(22);
            personInfo.getCandidateTypeList().add(23);
            personInfo.getCandidateTypeList().add(24);
            personInfo.getCandidateTypeList().add(25);
            personInfo.getCandidateTypeList().add(26);
        }
        //注册土木工程师（港口与航道工程）
        if (sysDept != null && "注册土木工程师（港口与航道工程）审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(19);
            personInfo.setCandidateType(7);
        }
        //注册土木工程师(道路工程)
        if (sysDept != null && "注册土木工程师(道路工程)审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(19);
            personInfo.setCandidateType(27);
        }
        //一级注册结构工程师
        if (sysDept != null && "一级注册结构工程师审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(19);
            personInfo.setCandidateType(4);
        }
        //二级注册结构工程师
        if (sysDept != null && "二级注册结构工程师审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(19);
            personInfo.setCandidateType(5);
        }
        //注册环保工程师
        if (sysDept != null && "注册环保工程师审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(19);
            personInfo.setCandidateType(28);
        }
        //注册公用设备工程师
        if (sysDept != null && "注册公用设备工程师审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(19);
            personInfo.getCandidateTypeList().add(8);
            personInfo.getCandidateTypeList().add(9);
            personInfo.getCandidateTypeList().add(10);
        }
        //注册化工工程师
        if (sysDept != null && "注册化工工程师审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(19);
            personInfo.setCandidateType(13);
        }
        //注册电气工程师
        if (sysDept != null && "注册电气工程师审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(19);
            personInfo.getCandidateTypeList().add(11);
            personInfo.getCandidateTypeList().add(12);
        }
        if (sysDept != null && "勘察设计注册工程师审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(19);
//            personInfo.getCandidateTypeList().add(11);
//            personInfo.getCandidateTypeList().add(12);
        }

        String username = SecurityUtils.getUsername();
        SysUser sysUser = new SysUser();
        SysUser user = sysUserService.selectUserByUserName(username);
        sysUser.setDeptId(user.getDeptId());
        String nickName = user.getNickName();
        String newName = nickName.substring(0, nickName.length() - 3);
        sysUser.setNickName(newName);
        List<String> qkUserList = sysUserService.selectUserListByNickName(sysUser);
        personInfo.setAuditTotal(qkUserList.size());
        //
        personInfo.setIndexTotal(qkUserList.indexOf(user.getNickName()));
        if (!newName.contains("全部")) {
            personInfo.setType(newName);
        }

        return allotPersonByDept(personInfo);
    }

    @Override
    public List<PersonInfo> selectauditList(PersonInfo personInfo) throws MyException {
        SysDept sysDept = sysDeptService.selectDeptById(SecurityUtils.getDeptId());
        String username = SecurityUtils.getUsername();
        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
        if (userType == null || "02".equals(userType) || "03".equals(userType)) {
            throw new MyException("没有权限");
        }
        //考生的身份证号List
        List<PersonInfo> personInfoList = new ArrayList<>();
        //省抽查账号
        if (sysDept != null && sysDept.getParentId() == 500124L) {
            if (personInfo.getSelect() == 1) {//待审核
                personInfo.setAuditStatus(2);
                personInfo.setAuditListStatus(5);
            } else if (personInfo.getSelect() == 2) {//审核通过
                personInfo.setAuditStatus(3);
            } else if (personInfo.getSelect() == 3) {//审核不通过
                personInfo.setAuditStatus(4);
            } else if (personInfo.getSelect() == 4) {//带企业审核
                personInfo.setAuditStatus(0);
            } else if (personInfo.getSelect() == 5) {
                personInfo.setAuditResult(1);
            } else if (personInfo.getSelect() == 6) {//未参加审核
                personInfo.setAuditResult(0);
            }
            //！！！！！！！！！！！！！！！！！
            personInfo.setInputType(31);
            personInfo.setCandidateType(1);
            //！！！！！！！！！！！！！！！！！
            if (sysDept.getDeptId() == 500125L) {
                personInfo.setType("考全科");
//                if (null == personInfo.getCityName() || personInfo.getCityName().contains("郑") || personInfo.getCityName().contains("州")) {
//                    personInfo.setCityName("市");
//                }
                if ("hnskqk001".equals(username)) {
                    personInfo.setAuditTotal(4);
                    personInfo.setIndexTotal(0);
                } else if ("hnskqk002".equals(username)) {
                    personInfo.setAuditTotal(4);
                    personInfo.setIndexTotal(1);
                } else if ("hnskqk003".equals(username)) {
                    personInfo.setAuditTotal(4);
                    personInfo.setIndexTotal(2);
                } else if ("hnskqk004".equals(username)) {
                    personInfo.setAuditTotal(4);
                    personInfo.setIndexTotal(3);
                }


                // 考生人员分配
                //分配考生
                return allotPersonByDept(personInfo);
               /* PageUtils.startPage();
                personInfoList = personInfoMapper.selectPersonInfoLists1(personInfo);*/
            } else if (sysDept.getDeptId() == 500126L) {
                personInfo.setType("增报");
                if ("hnskzx001".equals(username)) {
                    //设置查看全部
                    personInfo.setAuditTotal(1);
                    personInfo.setIndexTotal(0);
//                } else if ("hnskzx002".equals(username)) {
//                    personInfo.setAuditTotal(3);
//                    personInfo.setIndexTotal(1);
//                } else if ("hnskzx003".equals(username)) {
//                    personInfo.setAuditTotal(3);
//                    personInfo.setIndexTotal(2);
                }
                return allotPersonByDept(personInfo);
              /*  PageUtils.startPage();
                personInfoList = personInfoMapper.selectPersonInfoLists1(personInfo);*/
            } else if (sysDept.getDeptId() == 500127L) {
                personInfo.setType("免");
                PageUtils.startPage();
                personInfoList = personInfoMapper.selectPersonInfoLists1(personInfo);
            } else {
                PageUtils.startPage();
                personInfoList = personInfoMapper.selectPersonInfoLists1(personInfo);
            }
            return personInfoList;
        }
        if ("00".equals(userType) || "01".equals(userType)) {//厅建筑市场处
            if (personInfo.getSelect() == 1) {//待审核
                personInfo.setAuditStatus(2);
                personInfo.setAuditListStatus(5);
            } else if (personInfo.getSelect() == 2) {//审核通过
                personInfo.setAuditStatus(3);
            } else if (personInfo.getSelect() == 3) {//审核不通过
                personInfo.setAuditStatus(4);
            } else if (personInfo.getSelect() == 4) {//带企业审核
                personInfo.setAuditStatus(0);
            } else if (personInfo.getSelect() == 5) {
                personInfo.setAuditResult(1);
            } else if (personInfo.getSelect() == 6) {//未参加审核
                personInfo.setAuditResult(0);
            }
            //todo 创建审核账号需修改  此处可优化为根据部门名称获取字典值，后续在进行搜索条件设置  批次？
            if (sysDept != null && sysDept.getDeptName().contains("省级抽查部门")) {
                personInfo.setInputType(30);
                personInfo.setCandidateType(14);
            }

//            if(sysDept != null && sysDept.getDeptName().equals("河南省住房和城乡建设厅")) {
//                personInfo.setInputType(30);
//                personInfo.setCandidateType(14);
//            }

//            personInfoList = selectByBatchAndType(sysDept, personInfo);
//            if (sysDept != null && "一级注册造价师审核部门".equals(sysDept.getDeptName())) {
//                personInfo.setInputType(17);
//                personInfo.setCandidateType(19);
//                SysUser sysUser = new SysUser();
//                SysUser user = sysUserService.selectUserByUserName(username);
//                sysUser.setDeptId(user.getDeptId());
//                String nickName = sysUser.getNickName();
//                String newName = nickName.substring(0, nickName.length() - 3);
//                sysUser.setNickName(newName);
//                List<String> qkUserList = sysUserService.selectUserListByNickName(sysUser);
//                personInfo.setAuditTotal(qkUserList.size());
//                //
//                personInfo.setIndexTotal(qkUserList.indexOf(user.getNickName()));
//                if (!newName.contains("全部")) {
//                    personInfo.setType(newName);
//                }
//
//                return allotPersonByDept(personInfo);
//
//            }
//
//            if (sysDept != null && "二级注册造价师审核部门".equals(sysDept.getDeptName())) {
//                personInfo.setInputType(18);
//                personInfo.setCandidateType(20);
//                SysUser sysUser = new SysUser();
//                SysUser user = sysUserService.selectUserByUserName(username);
//                sysUser.setDeptId(user.getDeptId());
//                String nickName = user.getNickName();
//                String newName = nickName.substring(0, nickName.length() - 3);
//                sysUser.setNickName(newName);
//                List<String> qkUserList = sysUserService.selectUserListByNickName(sysUser);
//                personInfo.setAuditTotal(qkUserList.size());
//                //
//                personInfo.setIndexTotal(qkUserList.indexOf(user.getNickName()));
//                if (!newName.contains("全部")) {
//                    personInfo.setType(newName);
//                }
//
//                return allotPersonByDept(personInfo);
//
//            }


//            if (sysDept != null && "一级注册建筑师审核部门".equals(sysDept.getDeptName())) {
//                personInfo.setInputType(13);
//                personInfo.setCandidateType(1);
//                if ("一级注册建筑审核03".equals(username)) {
//                    personInfo.setAuditTotal(1);
//                    personInfo.setIndexTotal(0);
//                } else if ("一级注册建筑审核02".equals(username)) {
//                    personInfo.setAuditTotal(2);
//                    personInfo.setIndexTotal(1);
//                } else if ("一级注册建筑审核01".equals(username)) {
//                    personInfo.setAuditTotal(2);
//                    personInfo.setIndexTotal(0);
//                }
//                // 考生人员分配
//                //分配考生
//                return allotPersonByDept(personInfo);
//            }
//            if (sysDept != null && "二级注册建筑师审核部门".equals(sysDept.getDeptName())) {
//                personInfo.setInputType(14);
//                personInfo.setCandidateType(2);
//                if ("二级注册建筑审核03".equals(username)) {
//                    personInfo.setAuditTotal(1);
//                    personInfo.setIndexTotal(0);
//                } else if ("二级注册建筑审核02".equals(username)) {
//                    personInfo.setAuditTotal(2);
//                    personInfo.setIndexTotal(1);
//                } else if ("二级注册建筑审核01".equals(username)) {
//                    personInfo.setAuditTotal(2);
//                    personInfo.setIndexTotal(0);
//                }
//                // 考生人员分配
//                //分配考生
//                return allotPersonByDept(personInfo);
//            }
//
            PageUtils.startPage();
            personInfoList = personInfoMapper.selectPersonInfoLists1(personInfo);
        }
        if ("04".equals(userType)) {
            //todo 创建审核账号需修改  市级账号展示具体的目前审核中的考试  可以考虑进行优化
            if (sysDept != null && "一级注册造价师审核部门".equals(sysDept.getDeptName())) {
                personInfo.setInputType(17);
                personInfo.setCandidateType(19);
                SysUser sysUser = new SysUser();
                SysUser user = sysUserService.selectUserByUserName(username);
                sysUser.setDeptId(user.getDeptId());
                String nickName = user.getNickName();
                String newName = nickName.substring(0, nickName.length() - 3);
                sysUser.setNickName(newName);
                List<String> qkUserList = sysUserService.selectUserListByNickName(sysUser);
                personInfo.setAuditTotal(qkUserList.size());
                personInfo.setIndexTotal(qkUserList.indexOf(user.getNickName()));
                if (!newName.contains("全部")) {
                    personInfo.setType(newName);
                }
            }
            if (sysDept != null && "二级注册造价师审核部门".equals(sysDept.getDeptName())) {
                personInfo.setInputType(18);
                personInfo.setCandidateType(20);
                SysUser sysUser = new SysUser();
                SysUser user = sysUserService.selectUserByUserName(username);
                sysUser.setDeptId(user.getDeptId());
                String nickName = user.getNickName();
                String newName = nickName.substring(0, nickName.length() - 3);
                sysUser.setNickName(newName);
                List<String> qkUserList = sysUserService.selectUserListByNickName(sysUser);
                personInfo.setAuditTotal(qkUserList.size());
                personInfo.setIndexTotal(qkUserList.indexOf(user.getNickName()));
                if (!newName.contains("全部")) {
                    personInfo.setType(newName);
                }
            }
            if (sysDept != null && "一级注册建筑师审核部门".equals(sysDept.getDeptName())) {
                personInfo.setInputType(13);
                personInfo.setCandidateType(1);
                if ("一级注册建筑审核03".equals(username)) {
                    personInfo.setAuditTotal(1);
                    personInfo.setIndexTotal(0);
                } else if ("一级注册建筑审核02".equals(username)) {
                    personInfo.setAuditTotal(2);
                    personInfo.setIndexTotal(1);
                } else if ("一级注册建筑审核01".equals(username)) {
                    personInfo.setAuditTotal(2);
                    personInfo.setIndexTotal(0);
                }
//                personInfo.setDeptId(sysDept.getDeptId().toString());
            }
            if (sysDept != null && "二级注册建筑师审核部门".equals(sysDept.getDeptName())) {
                personInfo.setInputType(14);
                personInfo.setCandidateType(2);
                if ("二级注册建筑审核03".equals(username)) {
                    personInfo.setAuditTotal(1);
                    personInfo.setIndexTotal(0);
                } else if ("二级注册建筑审核02".equals(username)) {
                    personInfo.setAuditTotal(2);
                    personInfo.setIndexTotal(1);
                } else if ("二级注册建筑审核01".equals(username)) {
                    personInfo.setAuditTotal(2);
                    personInfo.setIndexTotal(0);
                }
//                personInfo.setDeptId(sysDept.getDeptId().toString());
            }
            if (sysDept != null && "省直".equals(sysDept.getDeptName())) {
                if ("省直01".equals(SecurityUtils.getUsername())) {
                    personInfo.setAuditTotal(1);
                    personInfo.setIndexTotal(0);
                    personInfo.setInputType(15);
                } else if ("省直02".equals(SecurityUtils.getUsername())) {
                    personInfo.setInputType(15);
                    personInfo.setAuditTotal(1);
                    personInfo.setIndexTotal(0);
                    personInfo.setType("考1科（即免试2科）");
                } else if ("省直03".equals(SecurityUtils.getUsername())) {
                    personInfo.setInputType(15);
                    personInfo.setAuditTotal(1);
                    personInfo.setIndexTotal(0);
                    personInfo.setType("增项");
                } else if ("省直04".equals(SecurityUtils.getUsername())) {
                    personInfo.setInputType(15);
                    personInfo.setAuditTotal(2);
                    personInfo.setIndexTotal(0);
                    personInfo.setType("考全科");
                } else if ("省直05".equals(SecurityUtils.getUsername())) {
                    personInfo.setInputType(15);
                    personInfo.setAuditTotal(2);
                    personInfo.setIndexTotal(1);
                    personInfo.setType("考全科");
                } else {
                    personInfo.setInputType(15);
                    personInfo.setCandidateType(17);
                    personInfo.setAuditTotal(1);
                    personInfo.setIndexTotal(0);
                }

            } else {
                // TODO: 2024/12/13 ____只让地市部门查看15批
                //todo 主要审核批次
                personInfo.setInputType(15);
                personInfo.setCandidateType(17);
                if (sysDept != null && "总账号".equals(sysDept.getDeptName())) {
                    personInfo.setAuditTotal(1);
                    personInfo.setIndexTotal(0);
                } else if (sysDept != null && "考1科".equals(sysDept.getDeptName())) {
                    personInfo.setType("考1科（即免试2科）");

                } else if (sysDept != null && "考2科".equals(sysDept.getDeptName())) {
                    personInfo.setType("考2科（免试《建设工程法规及相关知识》）");

                } else if (sysDept != null && "增项".equals(sysDept.getDeptName())) {
                    personInfo.setType("增项");

                } else if (sysDept != null && "考全科".equals(sysDept.getDeptName())) {
                    personInfo.setType("考全科");
                }
            }

            //todo 2024-3-26 勘察设计审核账号创建
            if ("kanchasheji01".equals(SecurityUtils.getUsername())) {
                personInfo.setInputType(11);
            }

            //注册监理工程师审核部门
            if (sysDept != null && "注册监理工程师审核部门".equals(sysDept.getDeptName())) {
//                personInfo.setDeptId(sysDept.getDeptId().toString());
                if ("注册监理审核01".equals(username) || "注册监理审核10".equals(username) || "注册监理审核11".equals(username) || "注册监理审核12".equals(username)) {
                    //不分配考生
                    personInfo.setAuditTotal(1);
                    personInfo.setIndexTotal(0);
                    personInfo.setInputType(12);
                } else if ("注册监理审核02".equals(username)) {
                    personInfo.setAuditTotal(1);
                    personInfo.setIndexTotal(0);
                    personInfo.setInputType(12);
                    personInfo.setType("免二科");
                } else if ("注册监理审核03".equals(username)) {
                    personInfo.setAuditTotal(1);
                    personInfo.setIndexTotal(0);
                    personInfo.setInputType(12);
                    personInfo.setType("增报专业");
                } else if ("注册监理审核04".equals(username)) {
                    personInfo.setAuditTotal(5);
                    personInfo.setIndexTotal(0);
                    personInfo.setInputType(12);
                    personInfo.setType("考全科");
                } else if ("注册监理审核05".equals(username)) {
                    personInfo.setAuditTotal(5);
                    personInfo.setIndexTotal(1);
                    personInfo.setInputType(12);
                    personInfo.setType("考全科");
                } else if ("注册监理审核06".equals(username)) {
                    personInfo.setAuditTotal(5);
                    personInfo.setIndexTotal(2);
                    personInfo.setInputType(12);
                    personInfo.setType("考全科");
                } else if ("注册监理审核07".equals(username)) {
                    personInfo.setAuditTotal(5);
                    personInfo.setIndexTotal(3);
                    personInfo.setInputType(12);
                    personInfo.setType("考全科");
                } else if ("注册监理审核08".equals(username)) {
                    personInfo.setAuditTotal(5);
                    personInfo.setIndexTotal(4);
                    personInfo.setInputType(12);
                    personInfo.setType("考全科");
                }
            }


            if (personInfo.getSelect() == 1) {
                personInfo.setAuditStatus(2);
                personInfo.setAuditListStatus(5);

            } else if (personInfo.getSelect() == 2) {
                personInfo.setAuditStatus(3);

            } else if (personInfo.getSelect() == 3) {
                personInfo.setAuditStatus(4);

            } else if (personInfo.getSelect() == 4) {
                personInfo.setAuditStatus(0);
            } else if (personInfo.getSelect() == 5) {
                personInfo.setAuditResult(1);
            } else if (personInfo.getSelect() == 6) {//未参加审核
                personInfo.setAuditResult(0);
            }
            // 考生人员分配

            //分配考生
            return allotPersonByDept(personInfo);


/*
            //分配前原始逻辑
            PageUtils.startPage();
            personInfoList = personInfoService.selectPersonInfoLists(personInfo);*/

        }
        return personInfoList;
    }

    private List<PersonInfo> allotPersonByDept(PersonInfo personInfo) throws MyException {
        //根据部门人数分配考生
        //根据考生id分配给不同的用户   先查询出所有的用户id，然后根据id%num添加到返回集合中
        //获取该用户在集合中位置
        if (personInfo.getAuditTotal() == null || personInfo.getIndexTotal() == null) {
            List<Long> sysUserIdList = sysUserService.selectUserIdByDeptId(SecurityUtils.getDeptId());
            List<Long> personInfoList = personInfoMapper.selectUserListByAuditStatus(personInfo);
            if (sysUserIdList.isEmpty()) {
                throw new MyException("该部门无可用用户");
            }
            int index = sysUserIdList.indexOf(SecurityUtils.getUserId());
            int userIndex = personInfoList.indexOf(SecurityUtils.getUserId());

            if(personInfo.getAuditStatus() == 2) {
                personInfo.setAuditTotal(personInfoList.size());
                personInfo.setIndexTotal(userIndex);
            }
            else if (sysUserIdList.size() % 2 == 0) {
                //设置总人数和审核账号所处位置
                personInfo.setAuditTotal(sysUserIdList.size());
                personInfo.setIndexTotal(index);
            } else {
                //设置总人数和审核账号所处位置
                personInfo.setAuditTotal(sysUserIdList.size());
                personInfo.setIndexTotal(index);
            }
        }
        // todo 第十一批考生专属定制 每个账号不进行分配考生 不进行考生地市条件查询
        if (personInfo.getInputType() != null && personInfo.getInputType() == 11) {
            personInfo.setAuditTotal(1);
            personInfo.setIndexTotal(0);
            if ("省直".equals(personInfo.getCityName())) {
                personInfo.setCityName(null);
            }

        }


        startPage();
        return personInfo.getAuditStatus() != null &&personInfo.getAuditStatus() == 2 ?
                personInfoMapper.selectPersonInfoListsByAverageExam(personInfo) : personInfoMapper.selectPersonInfoListsByAverage(personInfo);
    }

    @Override
    public List<PersonInfo> selectauditListExport(PersonInfo personInfo, SysDept dept) throws MyException {
        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
        if (userType == null || "02".equals(userType) || "03".equals(userType)) {
            throw new MyException("没有权限");
        }
        Long deptId = SecurityUtils.getDeptId();
        //考生的身份证号List
        List<PersonInfo> personInfoList = new ArrayList<>();
        if (SecurityUtils.getUserId() == 14141313 || deptId == 410005L) {
            if (personInfo.getSelect() == 1) {//待审核
                personInfo.setAuditStatus(2);
                personInfo.setAuditListStatus(5);
            } else if (personInfo.getSelect() == 2) {//审核通过
                personInfo.setAuditStatus(3);
            } else if (personInfo.getSelect() == 3) {//审核不通过
                personInfo.setAuditStatus(4);
            } else if (personInfo.getSelect() == 4) {//带企业审核
                personInfo.setAuditStatus(0);
            }
            personInfoList = personInfoService.selectPersonInfoLists(personInfo);
            if (personInfo.getSelect() == 5) {

                personInfoList = personInfoService.selectPersonInfoaudirStatusIsNotNull(personInfo);
            }
            if (personInfo.getSelect() == 6) {//未参加审核

                personInfoList = personInfoService.selectPersonInfoAuditStatusIsNull(personInfo);
            }
        } else if ("2".equals(dept.getDeptType())) {
            personInfo.setInputType(1);
            if (personInfo.getSelect() == 1) {
                personInfo.setAuditStatus(2);
                personInfo.setAuditListStatus(5);

            } else if (personInfo.getSelect() == 2) {
                personInfo.setAuditStatus(3);

            } else if (personInfo.getSelect() == 3) {
                personInfo.setAuditStatus(4);

            } else if (personInfo.getSelect() == 4) {
                personInfo.setAuditStatus(0);
            }
            personInfoList = personInfoService.selectPersonInfoLists(personInfo);
            if (personInfo.getSelect() == 5) {
                personInfoList = personInfoService.selectPersonInfoaudirStatusIsNotNull(personInfo);
            }
            if (personInfo.getSelect() == 6) {
                personInfoList = personInfoService.selectPersonInfoAuditStatusIsNull(personInfo);
            }
        }
        return personInfoList;
    }


    /**
     * 审核
     *
     * @param audit
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String audit(Audit audit) throws MyException {

        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
        if (userType == null || UserTypeEnum.COMPANY.getCode().equals(userType)
                || UserTypeEnum.EXAMINEE.getCode().equals(userType)) {
            throw new MyException("没有权限");
        }
        //PersonInfo personInfo = personInfoService.getInfoByIdCard(SecurityUtils.getUsername());
        PersonInfo personInfo = personInfoService.selectPersonInfoById(audit.getExamineeId());
        if (Objects.isNull(personInfo)) {
            log.warn("主管部门审核,个人信息为空,audit:{}", JSON.toJSONString(audit));
            throw new MyException("未查询到考生信息");
        }

        /*  // 防止同一个人被审核两次
        //查询该考生最新的一条审核记录    判断是否是主管部门审核
       AuditRecord record = auditRecordMapper.selectAuditRecordByAuditId(audit.getId());
        SysUser sysUser = sysUserMapper.selectUserById(Long.valueOf(record.getAuditPerson()));
        if (sysUser.getUserType() != "02" || sysUser.getUserType() != "03"){
            //最新的一条审核是主管部门审核
            //判断当前登录的主管部门用户是否是审核记录中用户
        }
        */

        audit.setSponsor(auditService.selectAuditById(audit.getId()).getSponsor());
        //审核
        if (audit.getAuditResult() != null) {
            if (audit.getAuditResult() == 0) {//合格
                audit.setAuditStatus(3);
                //只有通过操作下,修改personInfo审核状态为已审核
                personInfo.setAuditStatus(2);
                personInfo.setAuditPerson(SecurityUtils.getUsername());
                personInfoService.updatePersonInfo(personInfo);
            } else if (audit.getAuditResult() == 1) { //不通过
                audit.setAuditStatus(4);
                //审核不通过审核状态为审核不通过
                personInfo.setAuditStatus(4);
                personInfo.setAuditPerson(SecurityUtils.getUsername());
                personInfoService.updatePersonInfo(personInfo);
            } else if (audit.getAuditResult() == 2) {
                audit.setAuditStatus(5);

            } else if (audit.getAuditResult() == 4) {
                audit.setAuditStatus(6);
                //修改人员状态为需要补交材料
                personInfo.setAuditStatus(1);
                //personInfo.setFlag(0);
                personInfo.setAuditPerson(SecurityUtils.getUsername());
                personInfoService.updatePersonInfo(personInfo);
            }

            if (auditService.updateAudit(audit) == 1) {
                //添加审核记录
                AuditRecord auditRecord = new AuditRecord();
                auditRecord.setAuditId(audit.getId());
                auditRecord.setAuditResult(audit.getAuditResult());
                auditRecord.setOpinion(audit.getOpinion());
                auditRecord.setCandidateType(audit.getCandidateType());
                auditRecord.setCorpName(personInfo.getWork());
                auditRecordService.insertAuditRecord(auditRecord);
            } else {
                return "审核失败";
            }
        }
        return "审核成功";
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deptAudit(Audit audit) throws MyException {
        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
        if (userType == null || UserTypeEnum.COMPANY.getCode().equals(userType)
                || UserTypeEnum.EXAMINEE.getCode().equals(userType)) {
            throw new MyException("没有权限");
        }
        SysDept sysDept = sysDeptService.selectDeptById(SecurityUtils.getDeptId());
        if (sysDept != null && sysDept.getDeptName().contains("审查部门")) {
            throw new MyException("审查部门没有权限");
        }
        PersonInfo personInfo = personInfoService.selectPersonInfoById(audit.getExamineeId());
        if (Objects.isNull(personInfo)) {
            log.warn("主管部门审核,个人信息为空,audit:{}", JSON.toJSONString(audit));
            throw new MyException("未查询到考生信息");
        }
        Audit auditById = auditService.selectAuditById(audit.getId());

        audit.setSponsor(auditById.getSponsor());


        // 防止同一个考生被二次审核
        AuditRecord record = auditRecordMapper.selectAuditRecordByAuditId(audit.getId());
        if (record != null && auditById.getAuditStatus() == 4) {
            throw new MyException("当前考生已被其他账号驳回");
        }


        //审核
        if (audit.getAuditResult() != null) {
            if (audit.getAuditResult() == 0) {//合格
                audit.setAuditStatus(3);
            } else if (audit.getAuditResult() == 1) { //不通过
                audit.setAuditStatus(4);
            } else if (audit.getAuditResult() == 2) {//现场审核
                audit.setAuditStatus(5);
            } else if (audit.getAuditResult() == 4) {//需补交资料
                audit.setAuditStatus(6);
            }
            int i = auditService.updateAuditByCorp(audit);
            if (i > 0) {
                //添加审核记录
                AuditRecord auditRecord = new AuditRecord();
                auditRecord.setAuditId(audit.getId());
                auditRecord.setAuditResult(audit.getAuditResult());
                auditRecord.setOpinion(audit.getOpinion());
                auditRecord.setCandidateType(audit.getCandidateType());
                auditRecord.setCorpName(personInfo.getWork());
                auditRecord.setCityFile(audit.getCityFile());
                auditRecord.setIsSee(audit.getIsSee());
                auditRecordService.insertAuditRecord(auditRecord);
            }
            return "审核成功";
        } else {
            return "审核失败";
        }
    }


    /**
     * 考生审核信息导出
     */
    @Override
    public List<PersonInfoExport> exportData(PersonInfo personInfo) throws MyException {
        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
        if (userType == null || "02".equals(userType) || "03".equals(userType)) {
            throw new MyException("没有权限");
        }
        List<PersonInfo> personInfoList = auditList(personInfo);
        List<PersonInfoExport> list = new ArrayList<PersonInfoExport>();
        for (PersonInfo info : personInfoList) {
            PersonInfoExport export = new PersonInfoExport();
            export.setName(info.getName());
            export.setIdCard(info.getIdCard());
            export.setCityName(info.getCityName());
            export.setWork(info.getWork());
            if (info.getAuditList().size() > 0) {
                export.setApplications(info.getAuditList().get(0).getNumber());
                //处理考生的状态
                Integer status = info.getAuditList().get(0).getAuditStatus();
                export.setStatus(status == 2 ? "待审核" : "已审核");
                if (status == 0) {
                    export.setStatus("已提交审核");
                }
                setAuditColumn(export, info.getAuditList().get(0));
            } else {
                export.setApplications("无");
                export.setStatus("未提交审核");
            }
            String examInfo = null;
            //考试科目处理
            List<ExamInfo> examInfoList = info.getExamInfoList();
            if (examInfoList.size() > 0) {
                for (ExamInfo examInfo1 : examInfoList) {
                    examInfo = examInfo1.getExamSubject() + "(" + examInfo1.getExamCode() + ")";
                }
            }
            export.setExam(examInfo);
            list.add(export);
        }
        return list;
    }


    @Override
    public List<PersonInfoExport> exportData1(PersonInfo personInfo) throws MyException {
        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
        if (userType == null || "02".equals(userType) || "03".equals(userType)) {
            throw new MyException("没有权限");
        }
        Long deptId = SecurityUtils.getDeptId();
        SysDept dept = sysDeptService.selectDeptById(deptId);
        if (!Objects.isNull(dept)) {
            String cityName = dept.getRemark();
            if (cityName != null) {
                if (cityName.contains("市") || cityName.contains("省直")) {
                    personInfo.setCityName(cityName);
                }
            }
        }
        List<PersonInfoExport> list = new ArrayList<PersonInfoExport>();
        if ("00".equals(userType) || "01".equals(userType)) {
            //省级数据
            if (personInfo.getSelect() == 1) {//待审核
                personInfo.setAuditStatus(2);
                personInfo.setAuditListStatus(5);
            } else if (personInfo.getSelect() == 2) {//审核通过
                personInfo.setAuditStatus(3);
            } else if (personInfo.getSelect() == 3) {//审核不通过
                personInfo.setAuditStatus(4);
            } else if (personInfo.getSelect() == 4) {//带企业审核
                personInfo.setAuditStatus(0);
            } else if (personInfo.getSelect() == 5) {
                personInfo.setAuditResult(1);
            } else if (personInfo.getSelect() == 6) {//未参加审核
                personInfo.setAuditResult(0);
            }
            list = personInfoService.selectPersonInfoListExport(personInfo);
        } else if ("04".equals(userType)) {
            //todo 创建审核账号需修改 导出各地市数据
            personInfo.setInputType(16);
            personInfo.setCandidateType(16);
            if (personInfo.getSelect() == 1) {
                personInfo.setAuditStatus(2);
                personInfo.setAuditListStatus(5);

            } else if (personInfo.getSelect() == 2) {
                personInfo.setAuditStatus(3);

            } else if (personInfo.getSelect() == 3) {
                personInfo.setAuditStatus(4);

            } else if (personInfo.getSelect() == 4) {
                personInfo.setAuditStatus(0);
            } else if (personInfo.getSelect() == 5) {
                personInfo.setAuditResult(1);
            } else if (personInfo.getSelect() == 6) {//未参加审核
                personInfo.setAuditResult(0);
            }
            list = personInfoService.selectPersonInfoListExport(personInfo);
        }
        return list;
    }

    @Override
    public String banRegister(PersonInfo personInfo) {
        Integer candidateType = personInfo.getCandidateType();
        Integer registerStatus = personInfo.getRegisterStatus();
        if (candidateType == null && registerStatus == null) {
            return "请选择考生类型";
        }
        personInfoMapper.updatePersonInfoByCandidateType(registerStatus, candidateType);

        return "禁止注册成功";
    }

    @Override
    public String banRegisterStatus(String id, Integer registerStatus) {

        return personInfoMapper.updatePersonInfoById(id, registerStatus) > 0 ? "修改成功" : "修改失败";
    }

    @Override
    public String sendSms(PersonInfo personInfo) throws MyException {

        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
        if (userType == null || "02".equals(userType) || "03".equals(userType)) {
            throw new MyException("没有权限");
        }
        Long deptId = SecurityUtils.getDeptId();
        //考生的身份证号List

        if ("00".equals(userType) || "01".equals(userType)) {//厅建筑市场处
            if (personInfo.getSelect() == 1) {//待审核
                personInfo.setAuditStatus(2);
                personInfo.setAuditListStatus(5);
            } else if (personInfo.getSelect() == 2) {//审核通过
                personInfo.setAuditStatus(3);
            } else if (personInfo.getSelect() == 3) {//审核不通过
                personInfo.setAuditStatus(4);
            } else if (personInfo.getSelect() == 4) {//带企业审核
                personInfo.setAuditStatus(0);
            } else if (personInfo.getSelect() == 5) {
                personInfo.setAuditResult(1);
            } else if (personInfo.getSelect() == 6) {//未参加审核
                personInfo.setAuditResult(0);
            }
            List<PersonInfo> personInfoList = personInfoMapper.getPersonInfoByPhone(personInfo);
            int applyIdSelectSize = 500;
            int limit = (personInfoList.size() + applyIdSelectSize - 1) / applyIdSelectSize;
            //分成limit次发请求到数据库，in（）操作时 可以把多条数据分割成多组请求
            Stream.iterate(0, n -> n + 1).limit(limit).forEach(a -> {
                //获取后面1000条中的前500条
                // 拿到这个参数的流的 （a * applyIdSelectSize）后面的数据 .limit（applyIdSelectSize）->后面数据的500条 .collect(Collectors.toList()->组成一个toList
                List<PersonInfo> iphone = personInfoList.stream().skip(a * applyIdSelectSize).limit(applyIdSelectSize).collect(Collectors.toList());

                String[] phoneNum = new String[iphone.size()];
                for (int i = 0; i < iphone.size(); i++) {
                    phoneNum[i] = iphone.get(i).getPhoneNumber().toString();
                    System.out.println(phoneNum[i]);
                }
                System.out.println("-----------------------");
//          smsService.sendSms(phoneNum,"2023年度河南省监理工程师职业资格考试（土木建筑工程专业）已开始考后资格审核，请各位考生及时登录http://*************:9022/，注册后按提示要求上传相关资料参与审核。详情查看https://hnjs.henan.gov.cn/2023/07-31/2788511.html");

            });
        }
        return "发送成功";
    }

    /**
     * 主管部门修改考生审核资料
     *
     * @param array
     * @param candidateType
     * @return
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String update(JSONArray array, Integer candidateType, String personId) throws MyException {
        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
        if ("03".equals(userType)) {
            throw new MyException("没有权限");
        }

        //判断附件是否已存在，存在就覆盖
        List<PersonData> personDataList = personDataMapper.getByIdCard(SecurityUtils.getUsername());
        personDataList.forEach(data -> personDataMapper.deletePersonDataById(data.getId()));
        for (int i = 0; i < array.size(); i++) {
            PersonData data = new PersonData();
            data.setIdCard(SecurityUtils.getUsername());
            data.setType(String.valueOf(i));
            data.setPath(array.getString(i));
            data.setCandidateType(candidateType);

            if (insertPersonData(data) < 1) {
                return "修改异常，操作失败";
            }
        }
        //新增audit表
        if (insertAuditByDept(candidateType, personId) < 1) {
            return "修改异常，操作失败";
        }
        //修改person_info表
        PersonInfo personInfo = new PersonInfo();
        personInfo.setUpdateBy(SecurityUtils.getUsername());
        personInfo.setUpdateTime(new Date());
        personInfo.setAuditResult(3);
        if (personInfoMapper.updatePersonInfo(personInfo) < 1) {
            return "修改异常，操作失败";
        }
        return "修改审核成功";
    }

    private int insertAuditByDept(Integer candidateType, String personId) {
        Audit audit = new Audit();
        audit.setCandidateType(candidateType);
        //审核表基本信息
        audit.setSponsor(SecurityUtils.getUsername());//发起人
        Integer number = auditMapper.selectAuditListByIdCard(audit);
        if (number == null) {
            number = 0;
        }
        audit.setAuditNumber(number + 1);
        audit.setId(String.valueOf(SnowflakeIdWorker.getInstance().nextId()));
        audit.setTime(DateUtils.getNowDate());
        audit.setAuditStatus(3);
        audit.setIdCard(personInfoMapper.selectPersonInfoById(personId).getIdCard());
        audit.setHanlder(SecurityUtils.getDeptId().toString());
        return auditMapper.insertAudit(audit);
    }

    private int insertPersonData(PersonData personData) {
        personData.setId(String.valueOf(SnowflakeIdWorker.getInstance().nextId()));
        personData.setCreateBy(SecurityUtils.getUserId().toString());
        personData.setCreateTime(DateUtils.getNowDate());
        personData.setUpdateTime(DateUtils.getNowDate());
        personData.setUpdateBy(SecurityUtils.getUserId().toString());
        return personDataMapper.insertPersonData(personData);
    }


//    @Override
//    public List<PersonInfoExport>  exportData1(PersonInfo personInfo) throws MyException{
//        Long deptId = SecurityUtils.getDeptId();
//        SysDept dept = sysDeptService.selectDeptById(deptId);
//        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
//        if(userType==null||"02".equals(userType)||"03".equals(userType)) {
//            throw new MyException("没有权限");
//        }
//        List<PersonInfo> personInfos = selectauditList(personInfo, dept);
//        List<PersonInfo> personInfos = selectauditListExport(personInfo, dept);
//
//        List<PersonInfoExport> list = new ArrayList<PersonInfoExport>();
//        for (PersonInfo info : personInfos) {
//            PersonInfoExport export = new PersonInfoExport();
//            export.setName(info.getName());
//            export.setIdCard(info.getIdCard());
//            export.setCityName(info.getCityName());
//            export.setWork(info.getWork());
//
//            export.setApplications(info.getNumber());
//            Integer status = info.getAuditListStatus();
//                //处理考生的状态
//            if(status!=null){
//
//                export.setStatus(status ==2?"待审核":"已审核");
//                    if(status==0){
//                        export.setStatus("已提交审核");
//                    }
//                    setAuditResult(export,info);
//            }else {
//                export.setApplications("无");
//                export.setStatus("未提交审核");
//            }
//            list.add(export);
//        }
//        return list;
//    }

    private void setAuditResult(PersonInfoExport export, PersonInfo info) {
        String auditId = info.getAuditId();
        Integer hand = info.getHand();
        if (hand == null) {
            hand = 0;
        }
        List<AuditRecord> auditRecords = auditRecordMapper.selectAuditRecordListByauditId(auditId);
        int size = auditRecords.size();
        if (size > 1) {
            if (hand == 0) {
                AuditRecord auditRecord = auditRecords.get(0);
                export.setAudit_result_cp(auditRecord.getAuditResult() == 1 ? "不同意" : "同意");
                export.setAudit_opinion_cp(auditRecord.getOpinion());
                AuditRecord auditRecord1 = auditRecords.get(size - 1);
                export.setAudit_result_zg(auditRecord1.getAuditResult() == 1 ? "不同意" : "同意");
                export.setAudit_opinion_zg(auditRecord1.getOpinion());
            } else {
                AuditRecord auditRecord1 = auditRecords.get(size - 1);
                export.setAudit_result_zg(auditRecord1.getAuditResult() == 1 ? "不同意" : "同意");
                export.setAudit_opinion_zg(auditRecord1.getOpinion());
            }
        } else if (size == 1) {
            if (hand == 0) {
                AuditRecord auditRecord = auditRecords.get(0);
                export.setAudit_result_cp(auditRecord.getAuditResult() == 1 ? "不同意" : "同意");
                export.setAudit_opinion_cp(auditRecord.getOpinion());
            } else {
                AuditRecord auditRecord = auditRecords.get(0);
                export.setAudit_result_zg(auditRecord.getAuditResult() == 1 ? "不同意" : "同意");
                export.setAudit_opinion_zg(auditRecord.getOpinion());
            }
        }
    }


    /**
     * 设置审核结果列
     *
     * @param export
     * @param audit
     */
    private void setAuditColumn(PersonInfoExport export, Audit audit) {
        List<AuditRecord> auditRecords = audit.getAuditRecord();
        Map<Integer, AuditRecord> rolesOpinion = auditRecords.stream().collect(Collectors.toMap(AuditRecord::getAuditType, Function.identity(), (k1, k2) -> k1));
        Optional<AuditRecord> auditRecordCP = Optional.ofNullable(rolesOpinion.get(0));
        export.setAudit_result_cp(auditRecordCP.isPresent() ? auditRecordCP.get().getAuditResult() == 1 ? "不同意" : "同意" : "");
        export.setAudit_opinion_cp(auditRecordCP.isPresent() ? auditRecordCP.get().getOpinion() : "");
        Optional<AuditRecord> auditRecordZG = Optional.ofNullable(rolesOpinion.get(1));
        export.setAudit_result_zg(auditRecordZG.isPresent() ? auditRecordZG.get().getAuditResult() == 1 ? "不同意" : "同意" : "");
        export.setAudit_opinion_zg(auditRecordZG.isPresent() ? auditRecordZG.get().getOpinion() : "");
    }

    @Override
    public String reduction(Audit audit) {
        audit.setAuditType(1);
        audit.setAuditResult(1);
        audit.setOpinion("主管部门还原");
        PersonInfo personInfo = personInfoService.selectPersonInfoById(audit.getExamineeId());
        //审核状态为待主管部门审核
        audit.setAuditStatus(2);
        personInfo.setAuditStatus(2);
        personInfoService.updatePersonInfo(personInfo);
        int i = auditService.updateAuditByCorp(audit);
        if (i == 1) {
            //添加审核记录
            AuditRecord auditRecord = new AuditRecord();
            auditRecord.setAuditId(audit.getId());
            auditRecord.setAuditResult(audit.getAuditResult());
            auditRecord.setOpinion(audit.getOpinion());
            auditRecord.setCandidateType(audit.getCandidateType());
            auditRecord.setCorpName(personInfo.getWork());
            auditRecordService.insertAuditRecord(auditRecord);
        } else {
            return "还原失败";
        }
        return "还原成功";
    }

    @Override
    public String adminaudit(Audit audit) throws MyException {
        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
        if (userType == null
                || UserTypeEnum.COMPANY.getCode().equals(userType)
                || UserTypeEnum.EXAMINEE.getCode().equals(userType)
                || UserTypeEnum.COMPETENT_DEPARTMENT.getCode().equals(userType)) {
            throw new MyException("没有权限");
        }
        PersonInfo personInfo = personInfoService.selectPersonInfoById(audit.getExamineeId());
        if (Objects.isNull(personInfo)) {
            log.warn("主管部门审核,个人信息为空,audit:{}", JSON.toJSONString(audit));
            throw new MyException("未查询到考生信息");
        }
        //审核
        if (audit.getAuditResult() != null) {
            if (audit.getAuditResult() == 0) {//合格
                audit.setOpinion("管理员修改为合格");
                audit.setAuditStatus(3);
                //只有通过操作下,修改personInfo审核状态为已审核
                personInfo.setAuditStatus(3);
            } else if (audit.getAuditResult() == 1) { //不通过
                audit.setOpinion("管理员修改为不合格");
                audit.setAuditStatus(4);
                //审核不通过审核状态为审核不通过
                personInfo.setAuditStatus(4);
            } else if (audit.getAuditResult() == 3) {//待审核
                audit.setOpinion("管理员修改为待主管部门审核");
                audit.setAuditStatus(2);
                personInfo.setAuditStatus(2);
            }
            personInfoService.updatePersonInfo(personInfo);
            int i = auditService.updateAuditByCorp(audit);
            if (i == 1) {
                //添加审核记录
                AuditRecord auditRecord = new AuditRecord();
                auditRecord.setAuditId(audit.getId());
                auditRecord.setAuditResult(audit.getAuditResult());
                auditRecord.setOpinion(audit.getOpinion());
                auditRecord.setCandidateType(audit.getCandidateType());
                auditRecord.setCorpName(personInfo.getWork());
                auditRecordService.insertAuditRecord(auditRecord);
            } else {
                return "审核失败";
            }
        }
        return "审核成功";
    }


}
