package com.tzstcl.department.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.tzstcl.common.annotation.Log;
import com.tzstcl.common.core.controller.BaseController;
import com.tzstcl.common.core.domain.entity.SysDept;
import com.tzstcl.common.core.domain.entity.SysDictData;
import com.tzstcl.common.core.page.TableDataInfo;
import com.tzstcl.audit.domain.Audit;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.core.page.TableSupport;
import com.tzstcl.common.enums.BusinessType;
import com.tzstcl.common.utils.PageUtils;
import com.tzstcl.common.utils.SecurityUtils;
import com.tzstcl.common.utils.poi.ExcelUtil;
import com.tzstcl.department.service.IDepartmentService;
import com.tzstcl.person.domain.PersonInfo;
import com.tzstcl.person.domain.PersonInfoExport;
import com.tzstcl.person.service.IPersonDataService;
import com.tzstcl.system.service.ISysDeptService;
import com.tzstcl.system.service.ISysUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.tzstcl.common.core.page.PageDomain;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

import static com.tzstcl.common.utils.PageUtils.startPage;

/**
 * 公司：天筑科技股份有限公司
 * 作者：wzh
 * 日期：XX年XX月XX日
 * 说明：
 */
@RestController
@RequestMapping("/department")
public class DepartmentController extends BaseController {

    @Autowired
    private IDepartmentService departmentService;
    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private IPersonDataService personDataService;
    /**
     * 旧逻辑主管部门首页数据
     */
//    @GetMapping("/getData")
//    public AjaxResult getData(String yearInfo,Integer candidateType)throws MyException{
//
//        return AjaxResult.success(departmentService.getData(yearInfo,candidateType));
//    }

    /**
     * 新逻辑主管部门首页数据
     */
    @GetMapping("/getData")
    public AjaxResult getData(String yearInfo, Integer candidateType) throws MyException {

        return AjaxResult.success(departmentService.getDataInfoIndex(yearInfo, candidateType));
    }

    /**
     * 考生信息列表
     */
    @GetMapping("/getPersonInfo")
    public TableDataInfo getPersonInfo(PersonInfo personInfo) throws MyException {
        List<PersonInfo> personInfoList = departmentService.getPersonInfo(personInfo);
        return getDataTable(personInfoList);

    }
    /**
     * 旧逻辑考生审核页面
     * @param  // 0全部,1未审核,2已审核,3未参加审核 4已提交审核
     */
//    @GetMapping("/auditList")
//    public TableDataInfo auditList(PersonInfo personInfo)throws MyException{
//        List<PersonInfo> l = departmentService.auditList(personInfo);
//        //System.out.println("====================="+l.size());
//        return PageUtils.projectPage(l);
//    }

    /**
     * 新逻辑考生审核页面
     */
    @GetMapping("/auditList")
    public TableDataInfo auditList(PersonInfo personInfo) throws MyException {
        Long deptId = SecurityUtils.getDeptId();
        SysDept dept = sysDeptService.selectDeptById(deptId);
        if (!Objects.isNull(dept)) {
            String cityName = dept.getRemark();
            if (cityName != null) {
                if (cityName.contains("市") || cityName.contains("省直")) {
                    personInfo.setCityName(cityName);
                }
            }
        }
        startPage();
        List<PersonInfo> l = departmentService.selectauditList(personInfo);
        return getDataTable(l);
    }


    @GetMapping("/sendSms")
    public AjaxResult sendSms(PersonInfo personInfo) throws MyException {
        Long deptId = SecurityUtils.getDeptId();
        SysDept dept = sysDeptService.selectDeptById(deptId);
        if (!Objects.isNull(dept)) {
            String cityName = dept.getRemark();
            if (cityName != null) {
                if (cityName.contains("市") || cityName.contains("省直")) {
                    personInfo.setCityName(cityName);
                }
            }
        }
        departmentService.sendSms(personInfo);
        return AjaxResult.success();
    }


//    /**
//     * 新逻辑考生审核页面
//     * */
//    @GetMapping("/auditList")
//    public TableDataInfo auditList(PersonInfo personInfo)throws MyException{
//        Long deptId = SecurityUtils.getDeptId();
//        SysDept dept = sysDeptService.selectDeptById(deptId);
//        if (!Objects.isNull(dept)) {
//            String cityName = dept.getRemark();
//            if (cityName!=null){
//                if (cityName.contains("市")||cityName.contains("省直")) {
//                    personInfo.setCityName(cityName);
//                }
//            }
//        }
//        startPage();
//        List<PersonInfo> l= departmentService.selectauditList(personInfo,dept);
//        return getDataTable(l);
//    }


    /**
     * 审核
     */
    @PostMapping("/audit")
    @Log(title = "审核", businessType = BusinessType.UPDATE)
    public AjaxResult audit(@RequestBody Audit audit) throws MyException {
//        String audit1 = departmentService.audit(audit);
        String audit1 = departmentService.deptAudit(audit);
        return AjaxResult.success(audit1);
    }


    /**
     * 管理员审核
     */
    @PostMapping("/adminaudit")
    @Log(title = "管理员审核", businessType = BusinessType.UPDATE)
    public AjaxResult adminaudit(@RequestBody Audit audit) throws MyException {

        return AjaxResult.success(departmentService.adminaudit(audit));
    }

    /**
     * 考生审核信息导出
     */
//    @PostMapping("/exportData")
//    public void exportData(HttpServletResponse response,PersonInfo personInfo)throws MyException{
//        List<PersonInfoExport> list =departmentService.exportData(personInfo);
//        ExcelUtil<PersonInfoExport> util = new ExcelUtil<PersonInfoExport>(PersonInfoExport.class);
//        util.exportExcel(response, list, "考生审核数据");
//    }

    /**
     * 考生审核信息导出新逻辑
     */
    @PostMapping("/exportData")
    public void exportData1(HttpServletResponse response, PersonInfo personInfo) throws MyException {
        Integer select = personInfo.getSelect();
        if (select == null) {
            personInfo.setSelect(0);
        }
        List<PersonInfoExport> list = departmentService.exportData1(personInfo);
        ExcelUtil<PersonInfoExport> util = new ExcelUtil<PersonInfoExport>(PersonInfoExport.class);
        util.exportExcel(response, list, "考生审核数据");
    }


    /**
     * 主管部门还原考生信息
     */
    @PostMapping("/reduction")
    @Log(title = "主管部门还原考生信息", businessType = BusinessType.UPDATE)
    public AjaxResult reduction(@RequestBody Audit audit) throws MyException {
        return AjaxResult.success(departmentService.reduction(audit));
    }


    /**
     * 审批结束，禁用考生注册
     */
    @PreAuthorize("@ss.hasPermi('department:banRegister')")
    @PostMapping("/banRegister")

    @Log(title = "审批结束，禁用考生注册", businessType = BusinessType.UPDATE)

    public AjaxResult banRegister(@RequestBody PersonInfo personInfo) {
        return AjaxResult.success(departmentService.banRegister(personInfo));
    }

    /**
     * 审批结束，是否禁用考生注册
     */
    @PreAuthorize("@ss.hasPermi('department:banRegisterStatus')")
    @GetMapping("/banRegisterStatus")
    public AjaxResult banRegisterStatus(@RequestParam("id") String id, @RequestParam("registerStatus") Integer registerStatus) {
        return AjaxResult.success(departmentService.banRegisterStatus(id, registerStatus));
    }


    /**
     * 主管部门修改考生资料并并通过
     *
     * @param object
     * @return
     */
    @PutMapping("/update/audit")
    @Log(title = "主管部门修改考生资料并并通过", businessType = BusinessType.UPDATE)

    public AjaxResult updateAudit(@RequestBody JSONObject object) throws MyException {
        JSONArray array = JSONArray.parseArray(object.getString("array"));
        Integer candidateType = object.getInteger("candidateType");
        String personId = object.getString("personId");
        return AjaxResult.success(departmentService.update(array, candidateType, personId));
    }

}
