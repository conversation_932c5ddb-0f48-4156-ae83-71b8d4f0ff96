//package com.tzstcl.sms.serviceImpl;
//import com.mascloud.sdkclient.Client;
//import com.tzstcl.sms.entity.Sms;
//import com.tzstcl.sms.mapper.SmsInfoMapper;
//import com.tzstcl.sms.service.SmsService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//import javax.annotation.PostConstruct;
//import java.util.List;
//import java.util.UUID;
//
//
///**
// * @ClassName SmsServiceImpl
// * @Description
// * <AUTHOR>
// * @Date 2019/2/18 15:45
// * @Version 1.0
// * @Company 天筑科技股份有限公司
// **/
//
//@Slf4j
//@Service
//public class SmsServiceImpl implements SmsService {
//    @Value("${mas.url}")
//    String url;
//    @Value("${mas.userAccount}")
//    String  userAccount;
//    @Value("${mas.password}")
//    String password;
//    @Value("${mas.ecname}")
//    String ecname;
//    @Value("${mas.sign}")
//    String sign;
//    @Value("${mas.serviceNo}")
//    String serviceNo;
//    @Value("${mas.orderNo}")
//    String orderNo;
//    Client client;
//
//    @Autowired
//    SmsInfoMapper smsInfoMapper;
//
//    @PostConstruct
//    public void clientInit(){
//        client = Client.getInstance();
//        client.login(url,userAccount,password,ecname);
//    }
//
//
///**
//     * <AUTHOR>
//     * @Company tzstcl
//     * @Description //
//     * @Date 18:14 2019/2/18
//     * @Param  phoneNos : 手机号，数组形式，同时向多个手机号发送短信  content:短信内容,短信内容不可超过70个字符
//     * @return
//     **/
//
//    @Override
//    public int sendSms(String [] phoneNos, String content) {
//        return 0;//测试时，屏蔽发送短信
////        try {
////            int result = client.sendDSMS(phoneNos,content,"",5, sign, UUID.randomUUID().toString().replaceAll("-", ""),true);
////            return result;
////        } catch (Exception e) {
////            //log.error("短信发送异常",e);
////            return 0;
////        }
//    }
//
//    @Override
//    public int insertSmsInfo(Sms sms) {
//        return smsInfoMapper.insertSmsInfo(sms);
//    }
//
//    @Override
//    public List<Sms> selectSmsList(Sms sms)
//    {
//        return smsInfoMapper.selectSmsList(sms);
//    }
//
//    @Override
//    public int updateSms(Sms sms)
//    {
//        return smsInfoMapper.updateSms(sms);
//    }
//}
//
