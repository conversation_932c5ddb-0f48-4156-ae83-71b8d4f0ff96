//package com.tzstcl.sms.ctrl;
//
//
//import cn.hutool.poi.excel.ExcelReader;
//import com.tzstcl.common.core.domain.AjaxResult;
////import com.tzstcl.framework.web.service.TokenService;
//import com.tzstcl.common.utils.StringUtils;
//import com.tzstcl.sms.domain.SmsRecord;
//import com.tzstcl.sms.entity.Sms;
//import com.tzstcl.sms.service.SmsService;
//import org.apache.commons.lang3.RandomStringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.util.*;
//import java.util.stream.Collectors;
//import java.util.stream.Stream;
//
//@RestController
//@RequestMapping("/sms")
//public class SmsCtrl {
//    @Autowired
//    SmsService smsService;
////    @Autowired
////    TokenService tokenService;
//
//    @PostMapping("/sendSms")
//    public AjaxResult sendSms(@RequestBody Sms sms){
//        String checkCode= RandomStringUtils.randomAlphanumeric(6).toUpperCase();
//        sms.setCheckcode(checkCode);
//        smsService.insertSmsInfo(sms);
//        if(sms.getPhonenumber()==null){
//            return AjaxResult.error("手机号不能为空!");
//        }
//        //发送验证码  ,五分钟之内有效。本验证码将用于主管部门审核操作,请勿泄露
//        String[] phoneNum={sms.getPhonenumber()};
//        smsService.sendSms(phoneNum,"您的验证码为:"+checkCode+",五分钟之内有效。本验证码将用于主管部门审核操作,请勿泄露!");
//        //五分钟之后验证码失效
//        final Timer timer=new Timer();
//        timer.schedule(new TimerTask() {
//            @Override
//            public void run() {
//                Sms sms1 = new Sms();
//                sms1.setPhonenumber(sms.getPhonenumber());
//                smsService.updateSms(sms1);
//                timer.cancel();
//            }
//        },5*60*1000);
//        return AjaxResult.success();
//    }
//
//    @PostMapping("/checkSms")
//    public AjaxResult checkSms(@RequestBody Sms sms){
//        List<Sms> list = smsService.selectSmsList(sms);
//        if(list==null||list.size()==0){
//            return AjaxResult.error("验证码已失效!");
//        }
//        if(list.get(0).getCheckcode().equals(sms.getCheckcode())){
//            return AjaxResult.success("success");
//        }else {
//            return AjaxResult.error("验证码错误!");
//        }
//    }
//
//    @PostMapping("/testSms")
//    public AjaxResult testSms(String phoneNumber){
//        String[] phoneNum={phoneNumber};
//        smsService.sendSms(phoneNum,"2023年度河南省监理工程师职业资格考试（土木建筑工程专业）已开始考后资格审核，请各位考生及时登录http://*************:9022/，注册后按提示要求上传相关资料参与审核。详情查看https://hnjs.henan.gov.cn/2023/07-31/2788511.html");
//        return AjaxResult.success();
//    }
//
//
//
//
//
//    @PostMapping("/importTest")
//    public String importTest(@RequestParam("file") MultipartFile file, SmsRecord smsRecord) {
//        //判断上传文件为空返回
//        if (file.isEmpty()) {
//            return "上传文件不能为空";
//        }
//        //判断文件是否是excel文件
//        String filename = file.getOriginalFilename();
//        if (!StringUtils.isEmpty(filename) && (!filename.matches("^.+\\.(?i)(xls)$")
//                && !filename.matches("^.+\\.(?i)(xlsx)$"))) {
//            return "上传文件格式错误，请上传后缀为.xls或.xlsx的文件";
//        }
//        //读取文件
//        ExcelReader readerExcel = null;
//        try {
////            readerExcel = ExcelUtil.getReader(file.getInputStream());
//            readerExcel = cn.hutool.poi.excel.ExcelUtil.getReader(file.getInputStream());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        //读取表头 （可以用来判断是否是自己系统想要接收的excel）
//        List<Object> firstRow = readerExcel.readRow(0);
//        if (firstRow.size()>1) {
//            return "长度不匹配";
//        }
//        //为了防止文件过大读取时内存溢出我们可以分批次进行读取
//        //获取总条数
//        int total = readerExcel.getRowCount();
//        //开始索引 一般第一行是表头 所以我们从1开始
//        int startIndex = 0;
//        //结束索引
//        int endIndex = 500;
//        //每次读取多少行
//        int batchSize = 500;
//        //记录批次
//        int batch = 1;
//        //存读取到的内容
//        List<List<Object>> readAll;
//        //开始读取 当结束索引大于总条数时结束
//        while (endIndex < total) {
//            readAll = readerExcel.read(startIndex, endIndex);
//            //定义一个方法做逻辑处理
//            readResult(readAll);
//            //读取规则刷新赋值
//            startIndex = endIndex + 1;
//            batch++;
//            endIndex = batch * batchSize;
//            if (endIndex > total) {
//                endIndex = total;
//            }
//        }
//        //处理最后一页
//        readAll = readerExcel.read(startIndex, endIndex);
//        readResult(readAll);
//        return "读取成功";
//    }
//
//    private void readResult(List<List<Object>> readAll) {
//        int applyIdSelectSize = 500;
//        int limit = (readAll.size() + applyIdSelectSize - 1) / applyIdSelectSize;
//        //分成limit次发请求到数据库，in（）操作时 可以把多条数据分割成多组请求
//        Stream.iterate(0, n -> n + 1).limit(limit).forEach(a -> {
//            //获取后面1000条中的前500条
//            // 拿到这个参数的流的 （a * applyIdSelectSize）后面的数据 .limit（applyIdSelectSize）->后面数据的500条 .collect(Collectors.toList()->组成一个toList
//            List<List<Object>> iphone = readAll.stream().skip(a * applyIdSelectSize).limit(applyIdSelectSize).collect(Collectors.toList());
//
//            String[] phoneNum = new String[iphone.size()];
//            System.out.println("------------------------------------------" + iphone.size());
//            for (int i = 0; i < iphone.size(); i++) {
//                phoneNum[i] = iphone.get(i).get(0).toString();
//                System.out.println(phoneNum[i]);
//
//            }
//            System.out.println("------------------------------------------");
//            smsService.sendSms(phoneNum,"系统显示你仍未参加监理工程师考后资格核查。请各位考生尽快登录http://*************:9022/，注册后按提示要求上传相关资料参与审核。详情查看https://hnjs.henan.gov.cn/2023/07-31/2788511.html");
////            smsService.sendSms(phoneNum,"2022年度河南省勘察设计注册工程师资格考试（补考）已开始考后资格审核，请各位考生及时登录http://*************:9022/，注册后参照报考条件（https://www.pqrc.org.cn/Details/Publish_Details_Form.aspx?RowGuid=16382846-2281-4c15-8d4a-c2d8aa4efd89），按提示要求上传相关资料参与审核。详情查看https://hnjs.henan.gov.cn/2023/08-17/2798606.html");
//
//        });
//
//
//    }
//
//    }
//
