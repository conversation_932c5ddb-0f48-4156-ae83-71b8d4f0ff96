//package com.tzstcl.sms.entity;
//
//import java.util.Date;
//import com.fasterxml.jackson.annotation.JsonFormat;
//import com.tzstcl.common.annotation.Excel;
//import com.tzstcl.common.core.domain.BaseEntity;
//import lombok.Data;
//
///**
// * 接收手机号记录对象 sms
// *
// * <AUTHOR>
// * @date 2021-11-29
// */
//@Data
//public class Sms extends BaseEntity
//{
//    private static final long serialVersionUID = 1L;
//
//    /** $column.columnComment */
//    private Long id;
//
//    /** 登录企业名称 */
//    @Excel(name = "登录企业名称")
//    private String username;
//
//    /** 统一社会信用代码 */
//    @Excel(name = "统一社会信用代码")
//    private String corpcode;
//
//    /** 手机号 */
//    @Excel(name = "手机号")
//    private String phonenumber;
//
//    /** 验证码 */
//    @Excel(name = "验证码")
//    private String checkcode;
//
//    /** 发送时间 */
//    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd")
//    private Date createdate;
//
//    /** 删除标志 */
//    @Excel(name = "删除标志")
//    private String delflag;
//
//
//
//
//
//    @Excel(name = "模板标题")
//    private String title;
//
//    @Excel(name = "模板内容")
//    private String content;
//
//    @Excel(name = "创建时间")
//    private Date creatTime;
//
//
//
//
//
//
//
//}
