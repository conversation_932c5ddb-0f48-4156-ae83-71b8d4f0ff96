//package com.tzstcl.sms.domain;
//
//import lombok.Data;
//
//import java.util.Date;
//
///**
// * <Description> <br>
// * 发送记录
// * <AUTHOR>
// * @version 1.0<br>
// * @date 2023/08/23 <br>
// */
//@Data
//public class SmsRecord {
//
//
//    /** id */
//    private String id;
//
//    /** 短信标题id */
//    private String titleId;
//
//    /** 内容 */
//    private String content;
//
//    /** 发送数量 */
//    private String number;
//
//    /** 发送方式(0实时；1定时) */
//    private Integer style;
//
//    /** 发送状态(0待发送；1发送成功；2发送失败) */
//    private Integer status;
//
//
//    /** 发送时间 */
//    private Date sendTime;
//
//    /** 发送人 */
//    private String userId;
//}
