package com.tzstcl.company.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.tzstcl.audit.domain.Audit;
import com.tzstcl.company.domain.Company;
import com.tzstcl.person.domain.PersonInfo;

/**
 * 企业信息Service接口
 * 
 * <AUTHOR>
 * @date 2022-11-24
 */
public interface ICompanyService 
{
    /**
     * 查询企业信息
     * 
     * @param id 企业信息主键
     * @return 企业信息
     */
    public Company selectCompanyById(String id);

    /**
     * 查询企业信息列表
     * 
     * @param company 企业信息
     * @return 企业信息集合
     */
    public List<Company> selectCompanyList(Company company);

    /**
     * 新增企业信息
     * 
     * @param company 企业信息
     * @return 结果
     */
    public int insertCompany(Company company);

    /**
     * 修改企业信息
     * 
     * @param company 企业信息
     * @return 结果
     */
    public int updateCompany(Company company);

    /**
     * 批量删除企业信息
     * 
     * @param ids 需要删除的企业信息主键集合
     * @return 结果
     */
    public int deleteCompanyByIds(String[] ids);

    /**
     * 删除企业信息信息
     * 
     * @param id 企业信息主键
     * @return 结果
     */
    public int deleteCompanyById(String id);

    /**
     * 根据社会信用代码更改
     * @param company
     * @return
     */
    public int updateByCorpCode(Company company);

    /**
     * 根据社会信用代码查询
     * @param corpCode
     * @return
     */
    public Company getByCorpCode(String corpCode);

    /**
     * 企业审核列表
     * @param  type(0待审核,1已审核,不传则为全部)
     */
    List<PersonInfo> getAuditList(String type,String name,String phoneNumber);

    /**
     * 企业审核操作
     */
    String audit(Audit audit);
    String corpAudit(Audit audit);
    /**
     * 根据企业名称查询corpCode
     */
    String getByName(String corpName);

    /**
     * 企业审核数据统计
     */
    JSONObject getData();

    /**
     * 获取已通过考生名单
     */
    List <PersonInfo> getPassData();
}

