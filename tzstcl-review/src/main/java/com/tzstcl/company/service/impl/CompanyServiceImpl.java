package com.tzstcl.company.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.alibaba.fastjson2.JSONObject;
import com.tzstcl.audit.domain.Audit;
import com.tzstcl.audit.domain.AuditRecord;
import com.tzstcl.audit.service.IAuditRecordService;
import com.tzstcl.audit.service.IAuditService;
import com.tzstcl.common.core.domain.entity.SysDept;
import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.common.utils.*;
import com.tzstcl.person.domain.PersonInfo;
import com.tzstcl.person.mapper.PersonInfoMapper;
import com.tzstcl.person.service.IPersonDataService;
import com.tzstcl.person.service.IPersonInfoService;
import com.tzstcl.person.service.impl.PersonInfoServiceImpl;
import com.tzstcl.system.service.ISysDeptService;
import com.tzstcl.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;
import com.tzstcl.company.mapper.CompanyMapper;
import com.tzstcl.company.domain.Company;
import com.tzstcl.company.service.ICompanyService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 企业信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
@Service
public class CompanyServiceImpl implements ICompanyService
{
    @Autowired
    private CompanyMapper companyMapper;
    @Autowired
    private IAuditService auditService;
    @Autowired
    private IAuditRecordService auditRecordService;
    @Autowired
    private IPersonInfoService personInfoService;
    @Autowired
    private PersonInfoMapper personInfoMapper;
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private ISysUserService sysUserService;

    /**
     * 查询企业信息
     *
     * @param id 企业信息主键
     * @return 企业信息
     */
    @Override
    public Company selectCompanyById(String id)
    {
        return companyMapper.selectCompanyById(id);
    }

    /**
     * 查询企业信息列表
     *
     * @param company 企业信息
     * @return 企业信息
     */
    @Override
    public List<Company> selectCompanyList(Company company)
    {
        return companyMapper.selectCompanyList(company);
    }

    /**
     * 新增企业信息
     *
     * @param company 企业信息
     * @return 结果
     */
    @Override
    public int insertCompany(Company company)
    {
        company.setId(String.valueOf(SnowflakeIdWorker.getInstance().nextId()));
        company.setCreateBy("1");
        company.setCreateTime(DateUtils.getNowDate());
        return companyMapper.insertCompany(company);
    }

    /**
     * 修改企业信息
     *
     * @param company 企业信息
     * @return 结果
     */
    @Override
    public int updateCompany(Company company)
    {
        company.setUpdateBy("1");
        company.setUpdateTime(DateUtils.getNowDate());
        return companyMapper.updateCompany(company);
    }

    /**
     * 批量删除企业信息
     *
     * @param ids 需要删除的企业信息主键
     * @return 结果
     */
    @Override
    public int deleteCompanyByIds(String[] ids)
    {
        return companyMapper.deleteCompanyByIds(ids);
    }

    /**
     * 删除企业信息信息
     *
     * @param id 企业信息主键
     * @return 结果
     */
    @Override
    public int deleteCompanyById(String id)
    {
        return companyMapper.deleteCompanyById(id);
    }
    /**
     * 根据社会信用代码更改
     * @param company
     * @return
     */
    @Override
    public int updateByCorpCode(Company company){
        return companyMapper.updateByCorpCode(company);
    }

    /**
     * 根据社会信用代码查询
     * @param corpCode
     * @return
     */
    @Override
    public Company getByCorpCode(String corpCode){
        return companyMapper.getByCorpCode(corpCode);
    }

    @Override
    public List<PersonInfo> getAuditList(String type,String name,String phoneNmuber) {// type(0待审核,1已审核,不传则为全部)
        PersonInfo personInfo = new PersonInfo();
        //todo 只允许查询出未被禁止注册的用户的审核信息
        personInfo.setRegisterStatus(0);
        personInfo.setName(name);
        personInfo.setPhoneNumber(phoneNmuber);
        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
        if(userType==null||userType.equals("01")||"03".equals(userType)){
            return new ArrayList<>();
        }

        if(!"00".equals(SecurityUtils.getLoginUser().getUser().getUserType())) {
            personInfo.setCorpCode(SecurityUtils.getUsername());
        }
        if ("0".equals(type)){
            personInfo.setAuditStatus(0);
        }
        //获得该企业下的所有考生
        PageUtils.startPage();
        List<PersonInfo> personInfoList = personInfoService.selectPersonInfoList(personInfo);
        personInfoList.stream().forEach(info -> {
            Integer auditStatus = info.getAuditStatus();
            if (auditStatus==2 || auditStatus==3 || auditStatus==5 || auditStatus==6){
                info.setAuditResult(0);
            }else {
                info.setAuditResult(1);
            }
            PersonInfo infoById = personInfoService.getInfoById(info.getId());
            info.setAuditList(infoById.getAuditList());
            info.setPersonDataList(infoById.getPersonDataList());
            info.setExamInfoList(infoById.getExamInfoList());
        });

        if ("1".equals(type)){
            PageUtils.startPage();
            List<PersonInfo> corpAuditPersonList = personInfoService.selectCorpAuditPersonList(personInfo);
            corpAuditPersonList.stream().forEach(info -> {
                Integer auditStatus = info.getAuditStatus();
                if (auditStatus==2 || auditStatus==3 || auditStatus==5 || auditStatus==6){
                    info.setAuditResult(0);
                }else {
                    info.setAuditResult(1);
                }
                PersonInfo infoById = personInfoService.getInfoById(info.getId());
                info.setAuditList(infoById.getAuditList());
                info.setPersonDataList(infoById.getPersonDataList());
                info.setExamInfoList(infoById.getExamInfoList());
            });
            return corpAuditPersonList;
        }

        return personInfoList;
    }

    /**
     * 企业审核操作
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String audit(Audit audit) {
        Audit auditById = auditService.selectAuditById(audit.getId());
        audit.setSponsor(auditById.getSponsor());
        AuditRecord auditRecord = new AuditRecord();
        List<SysDept> sysDeptList = new ArrayList<>();
        //审核
        if(audit.getAuditResult()!=null){
            if(audit.getAuditResult()==0){//合格
                audit.setAuditStatus(2);
                //设置下一步的审核人
                Company company = companyMapper.getByCorpCode(SecurityUtils.getUsername());
                if(company!=null&&StringUtils.isNotEmpty(company.getCityNum())){
                    audit.setHanlder(company.getCityNum());
                    auditRecord.setCorpName(company.getCorpName());
                }else {
                    SysDept dept = new SysDept();
                    //获取考生信息
                    SysUser  sysUser = sysUserService.selectUserById(Long.valueOf(audit.getSponsor()));
                    //根据报考地址获取主管部门的行政区域代码
                    //PersonInfo personInfo = personInfoService.getInfoById(personInfoService.getInfoByIdCard(sysUser.getUserName()).getId());
                    //查询该考生
                    PersonInfo personInfo1 = new PersonInfo();
                    personInfo1.setIdCard(auditById.getIdCard());
                    personInfo1.setCandidateType(auditById.getCandidateType());
                    PersonInfo personInfo = personInfoMapper.selectPersonInfoByIdCardAndCandType(personInfo1);

                    if(StringUtils.isNotEmpty(personInfo.getCityName())){
                        dept.setRemark(personInfo.getCityName());
                        sysDeptList= sysDeptService.selectDeptList(dept);
                        if(sysDeptList.size()==0){
//                            return "考生的报考地市不在河南省中";
                            audit.setHanlder(null);
                        }else {
                            audit.setHanlder(sysDeptList.get(0).getDeptId().toString());
                        }
                    }
                    auditRecord.setCorpName(personInfo.getWork());
                    audit.setAuditStatus(2);
                }
            }else {
                audit.setAuditStatus(1);//不合格
            }
        }
        if(auditService.updateAudit(audit)==1){
            //添加审核记录
            auditRecord.setAuditId(audit.getId());
            auditRecord.setAuditResult(audit.getAuditResult());
            auditRecord.setOpinion(audit.getOpinion());
            auditRecord.setCandidateType(audit.getCandidateType());
            auditRecordService.insertAuditRecord(auditRecord);
        }else {
            return "审核失败";
        }
        return "审核成功";
    }

    /**
     * 企业审核操作
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String corpAudit(Audit audit) {
        AuditRecord auditRecord = new AuditRecord();
        Audit auditById = auditService.selectAuditById(audit.getId());
        Company company = companyMapper.getByCorpCode(SecurityUtils.getUsername());
        //查询考生是否是待企业审核
        //查询该考生
        PersonInfo personInfo1 = new PersonInfo();
        personInfo1.setIdCard(auditById.getIdCard());
        personInfo1.setCandidateType(auditById.getCandidateType());
        PersonInfo personInfo = personInfoMapper.selectPersonInfoByIdCardAndCandType(personInfo1);

        //审核
        if(audit.getAuditResult()!=null && personInfo.getAuditStatus() == 0){
            if(audit.getAuditResult()==0){//合格
                audit.setAuditStatus(2);
                //设置下一步的审核人

                if(company!=null&&StringUtils.isNotEmpty(company.getCityNum())){
                    audit.setHanlder(company.getCityNum());
                    auditRecord.setCorpName(company.getCorpName());

                }
            }else if (audit.getAuditResult()==1&&(audit.getAuditStatus()!=null && audit.getAuditStatus()==7)){
                audit.setAuditStatus(7);//撤回
            }else {
                audit.setAuditStatus(1);//不合格
            }
        }

        int i = auditService.updateAuditByCorp(audit);
        if(i ==1){
            //添加审核记录
            auditRecord.setAuditId(audit.getId());
            auditRecord.setAuditResult(audit.getAuditResult());
            auditRecord.setOpinion(audit.getOpinion());
            auditRecord.setCandidateType(audit.getCandidateType());
            auditRecord.setCorpName(personInfo.getWork());
            auditRecordService.insertAuditRecord(auditRecord);
        }else {
            return "审核失败";
        }
        return "审核成功";
    }

    /**
     * 根据企业名称查询corpCode
     */
    @Override
    public String getByName(String corpName) {
        return companyMapper.getByName(corpName);
    }
    /**
     * 企业审核数据统计
     */
    @Override
    public JSONObject getData() {
        //获得该企业下的所有考生
        PersonInfo personInfo = new PersonInfo();
        personInfo.setCorpCode(SecurityUtils.getUsername());
        JSONObject jsonObject = new JSONObject();
        int sum = 0;
        int yes =0;
        int no =0;
        List<PersonInfo> personInfoList = personInfoService.selectPersonInfoList(personInfo);
        personInfo.setAuditStatus(0);
        List<PersonInfo> noAuditPersonInfoList = personInfoService.selectPersonInfoList(personInfo);
        no= noAuditPersonInfoList.size();
        sum = personInfoList.size();
        yes = sum-no;
//        if(personInfoList.size()>0) {
//            for (PersonInfo info : personInfoList) {
//                //personInfo = personInfoService.getInfoByIdCard(info.getIdCard());
//                PersonInfo personInfo1 = personInfoService.getInfoByIdCardAndcandidateType(info.getIdCard(), info.getCandidateType());
//                //遍历personInfos
//                if (personInfo1.getAuditList().size() != 0) {//筛选出未参加审核的考生
//                    sum+=1;
//                    if (personInfo1.getAuditList().get(0).getAuditStatus() == 0) {
//                        no += 1;
//                    } else {
//                        yes += 1;
//                    }
//                }
////                if (personInfo.getAuditList().size() != 0) {//筛选出未参加审核的考生
////                    sum+=1;
////                    if (personInfo.getAuditList().get(0).getAuditStatus() == 0) {
////                        no += 1;
////                    } else {
////                        yes += 1;
////                    }
////                }
//            }
//        }
        jsonObject.put("sum",sum);//考生数
        jsonObject.put("yes",yes);//已审核
        jsonObject.put("no",no);//未审核
        return jsonObject;
    }
    /**
     * 获取已通过考生名单
     */
    @Override
    public List<PersonInfo> getPassData() {
        //获得该企业下的所有考生
        PersonInfo personInfo = new PersonInfo();
        personInfo.setCorpCode(SecurityUtils.getUsername());
        personInfo.setAuditStatus(3);
        List<PersonInfo> personInfoList = personInfoService.selectPersonInfoList(personInfo);
//        List<PersonInfo> resultList = new ArrayList<>();
//        if(personInfoList.size()>0){
//            for (PersonInfo info : personInfoList) {
//                info = personInfoService.getInfoByIdCardAndcandidateType(info.getIdCard(), info.getCandidateType());
//                if (info.getAuditList().size() != 0) {//筛选出未参加审核的考生
//                    if (info.getAuditList().get(0).getAuditStatus() == 3) {
//                        resultList.add(info);
//                    }
//                }
//            }
//        }
        return personInfoList;
    }
}

