package com.tzstcl.company.mapper;

import java.util.List;
import com.tzstcl.company.domain.Company;
import org.apache.ibatis.annotations.Mapper;

/**
 * 企业信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
@Mapper
public interface CompanyMapper
{
    /**
     * 查询企业信息
     *
     * @param id 企业信息主键
     * @return 企业信息
     */
    public Company selectCompanyById(String id);

    /**
     * 查询企业信息列表
     *
     * @param company 企业信息
     * @return 企业信息集合
     */
    public List<Company> selectCompanyList(Company company);

    /**
     * 新增企业信息
     *
     * @param company 企业信息
     * @return 结果
     */
    public int insertCompany(Company company);

    /**
     * 修改企业信息
     *
     * @param company 企业信息
     * @return 结果
     */
    public int updateCompany(Company company);

    /**
     * 删除企业信息
     *
     * @param id 企业信息主键
     * @return 结果
     */
    public int deleteCompanyById(String id);

    /**
     * 批量删除企业信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCompanyByIds(String[] ids);

    /**
     * 根据社会信用代码更改
     * @param company
     * @return
     */
    public int updateByCorpCode(Company company);

    /**
     * 根据社会信用代码查询
     * @param corpCode
     * @return
     */
    Company getByCorpCode(String corpCode);

    /**
     * 根据企业名称查询corpCode
     */
    String getByName(String corpName);

    /**
     * 根据企业信用代码查询企业信息
     * @param corpCode
     * @return
     */
    Company selectCompanyByCorpCode(String corpCode);
}
