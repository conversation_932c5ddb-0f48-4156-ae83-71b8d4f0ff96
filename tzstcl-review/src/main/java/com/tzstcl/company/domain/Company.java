package com.tzstcl.company.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzstcl.common.annotation.Excel;
import com.tzstcl.common.core.domain.BaseEntity;

/**
 * 企业信息对象 company
 * 
 * <AUTHOR>
 * @date 2022-11-24
 */
public class Company extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private String id;

    /** 社会统一代码 */
    @Excel(name = "社会统一代码")
    private String corpCode;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String corpName;

    /** 注册所在地（市、州、盟） */
    @Excel(name = "注册所在地", readConverterExp = "市=、州、盟")
    private String cityNum;

    /** 注册所在县（区、市、旗） */
    @Excel(name = "注册所在县", readConverterExp = "区=、市、旗")
    private String countyNum;

    /** 删除标记(0-未删除，1-已删除) */
    private Integer delFlag;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setCorpCode(String corpCode) 
    {
        this.corpCode = corpCode;
    }

    public String getCorpCode() 
    {
        return corpCode;
    }
    public void setCorpName(String corpName) 
    {
        this.corpName = corpName;
    }

    public String getCorpName() 
    {
        return corpName;
    }
    public void setCityNum(String cityNum) 
    {
        this.cityNum = cityNum;
    }

    public String getCityNum() 
    {
        return cityNum;
    }
    public void setCountyNum(String countyNum) 
    {
        this.countyNum = countyNum;
    }

    public String getCountyNum() 
    {
        return countyNum;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("corpCode", getCorpCode())
            .append("corpName", getCorpName())
            .append("cityNum", getCityNum())
            .append("countyNum", getCountyNum())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Company company = (Company) o;

        if (!corpCode.equals(company.corpCode)) return false;
        return corpName.equals(company.corpName);
    }

    @Override
    public int hashCode() {
        int result = corpCode.hashCode();
        result = 31 * result + corpName.hashCode();
        return result;
    }


}
