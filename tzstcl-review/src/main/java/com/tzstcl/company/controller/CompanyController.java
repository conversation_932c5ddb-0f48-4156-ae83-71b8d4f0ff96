package com.tzstcl.company.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tzstcl.audit.domain.Audit;
import com.tzstcl.person.domain.PersonInfo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzstcl.common.annotation.Log;
import com.tzstcl.common.core.controller.BaseController;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.enums.BusinessType;
import com.tzstcl.company.domain.Company;
import com.tzstcl.company.service.ICompanyService;
import com.tzstcl.common.utils.poi.ExcelUtil;
import com.tzstcl.common.core.page.TableDataInfo;

/**
 * 企业信息Controller
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
@RestController
@RequestMapping("/company/info")
public class CompanyController extends BaseController
{
    @Autowired
    private ICompanyService companyService;

    /**
     * 查询企业信息列表
     */
    @PreAuthorize("@ss.hasPermi('company:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(Company company)
    {
        startPage();
        List<Company> list = companyService.selectCompanyList(company);
        return getDataTable(list);
    }

    /**
     * 导出企业信息列表
     */
  /*  @PreAuthorize("@ss.hasPermi('company:info:export')")
    @Log(title = "企业信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Company company)
    {
        List<Company> list = companyService.selectCompanyList(company);
        ExcelUtil<Company> util = new ExcelUtil<Company>(Company.class);
        util.exportExcel(response, list, "企业信息数据");
    }*/

    /**
     * 获取企业信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('company:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(companyService.selectCompanyById(id));
    }

    /**
     * 新增企业信息
     */
    @PreAuthorize("@ss.hasPermi('company:info:add')")
    @Log(title = "企业信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Company company)
    {
        return toAjax(companyService.insertCompany(company));
    }

    /**
     * 修改企业信息
     */
    @PreAuthorize("@ss.hasPermi('company:info:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Company company)
    {
        return toAjax(companyService.updateCompany(company));
    }

    /**
     * 删除企业信息
     */
    @PreAuthorize("@ss.hasPermi('company:info:remove')")
    @Log(title = "企业信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(companyService.deleteCompanyByIds(ids));
    }

    /**
     * 企业审核列表
     * @param  type(0待审核,1已审核,不传则为全部)
     */
    @GetMapping("/getAuditList")
    public TableDataInfo getAuditList(String type,String name, String phoneNumber){
        startPage();
        List<PersonInfo> auditList = companyService.getAuditList(type, name, phoneNumber);
        return getDataTable(auditList);
    }

    /**
     * 企业审核操作
     */
    @PostMapping("/audit")
    @Log(title = "企业审核操作", businessType = BusinessType.INSERT)

    public AjaxResult audit(@RequestBody Audit audit){
//        String audit1 = companyService.audit(audit);
        String audit1 = companyService.corpAudit(audit);
        return AjaxResult.success(audit1);
    }
    /**
     * 企业审核数据统计
     */
    @GetMapping("/getData")
    public AjaxResult getData(){
        return AjaxResult.success(companyService.getData());
    }
    /**
     * 获取已通过考生名单
     */
    @GetMapping("/getPassData")
    public TableDataInfo getPassData(){
        startPage();
        List<PersonInfo> passData = companyService.getPassData();
        return getDataTable(passData);
    }

}
