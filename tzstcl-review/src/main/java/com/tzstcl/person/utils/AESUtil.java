package com.tzstcl.person.utils;

import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.*;

public class AESUtil {

	// key为16位
	private static String KEY = "tzkj20240514zghs";

	private static String IV = "2024051420240514";

	static {
		Security.addProvider(new BouncyCastleProvider());

	}

	public static String decrypt(String content, String key, String vi)
			throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, BadPaddingException,
			IllegalBlockSizeException, UnsupportedEncodingException, InvalidAlgorithmParameterException {
		Key k = toKey(key.getBytes());
		byte[] encoded = k.getEncoded();
		SecretKeySpec aes = new SecretKeySpec(encoded, "AES");
		Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
		IvParameterSpec iv = new IvParameterSpec(vi.getBytes());
		cipher.init(Cipher.DECRYPT_MODE, aes, iv);

		byte[] bytes = cipher.doFinal(Base64.decodeBase64(content));

		return new String(bytes, "UTF-8");
	}

	public static String encrypt(String data, String key, String vi)
			throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException,
			BadPaddingException, IllegalBlockSizeException, InvalidAlgorithmParameterException {
		// Key k = toKey(Base64.decodeBase64(key));
		Key k = toKey(key.getBytes());
		byte[] encoded = k.getEncoded();
		SecretKeySpec aes = new SecretKeySpec(encoded, "AES");
		Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
		IvParameterSpec iv = new IvParameterSpec(vi.getBytes());
		cipher.init(Cipher.ENCRYPT_MODE, aes, iv);
		byte[] bytes = cipher.doFinal(data.getBytes("UTF-8"));
		return Base64.encodeBase64String(bytes);
	}

	private static Key toKey(byte[] key) {

		SecretKeySpec aes = new SecretKeySpec(key, "AES");
		return aes;
	}

	/**
	 * 使用默认的key和iv加密
	 * @param data
	 * @return
	 * @throws Exception
	 */
	public static String encrypt(String data) throws Exception {
		return encrypt(data, KEY, IV);
	}

	/**
	 * 使用默认的key和iv解密
	 * @param data
	 * @return
	 * @throws Exception
	 */
	public static String desEncrypt(String data) throws Exception {
		return decrypt(data, KEY, IV);
	}

	public static void main(String[] args) throws NoSuchPaddingException, BadPaddingException,
			InvalidAlgorithmParameterException, NoSuchAlgorithmException, IllegalBlockSizeException,
			UnsupportedEncodingException, InvalidKeyException, Exception {

		String content = "uVfJ6nDvfTam16jY8Fc0uw==";
		String key = KEY;
		String vi = IV;
		String ckkkkk = AESUtil.encrypt("lsq0514");
		System.out.println("ssss :" + ckkkkk);

		String f = AESUtil.decrypt("2lwLTGrpzYfJ/13CT7M24g==  ", key, vi);
		System.out.println("解密 : " + f);

		String pwd = AESUtil.encrypt("lsq0514dsz");
		System.out.println("pwd :" + pwd);

		String p = AESUtil.decrypt("4yPRsDwUc4beF5y4XBhZhQ==", key, vi);
		System.out.println("解密 : " + p);
	}

}
