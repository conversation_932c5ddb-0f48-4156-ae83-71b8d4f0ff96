package com.tzstcl.person.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import afu.org.checkerframework.checker.igj.qual.I;
import com.alibaba.fastjson2.JSONObject;
import com.tzstcl.audit.domain.Audit;
import com.tzstcl.audit.domain.AuditRecord;
import com.tzstcl.audit.mapper.AuditMapper;
import com.tzstcl.audit.service.IAuditService;
import com.tzstcl.common.annotation.DataScope;
import com.tzstcl.common.constant.UserConstants;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.core.domain.entity.SysDept;
import com.tzstcl.common.core.domain.entity.SysRole;
import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.common.core.domain.model.ResetPwdBody;
import com.tzstcl.common.enums.AuditStatusEnum;
import com.tzstcl.common.exception.ServiceException;
import com.tzstcl.common.utils.*;
import com.tzstcl.common.utils.poi.ExcelUtil;
import com.tzstcl.common.utils.uuid.IdUtils;
import com.tzstcl.company.service.ICompanyService;
import com.tzstcl.department.controller.MyException;
import com.tzstcl.person.domain.*;
import com.tzstcl.person.service.IExamInfoService;
import com.tzstcl.person.service.IPersonDataService;
import com.tzstcl.system.service.ISysDeptService;
import com.tzstcl.system.service.ISysDictDataService;
import com.tzstcl.system.service.ISysUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzstcl.person.mapper.PersonInfoMapper;
import com.tzstcl.person.service.IPersonInfoService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import cn.hutool.core.util.IdUtil;
import static com.tzstcl.person.utils.IdCardNumberUtils.getSexFromIdCard;


/**
 * 考生信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-17
 */
@Service(value = "personInfoServiceImpl")
public class PersonInfoServiceImpl implements IPersonInfoService
{
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    private PersonInfoMapper personInfoMapper;
    @Autowired
    private IPersonDataService personDataService;
    @Autowired
    private IAuditService auditService;
    @Autowired
    private IExamInfoService examInfoService;
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private AuditMapper auditMapper;
    @Autowired
    private ISysDeptService sysDeptService;
    /**
     * 查询考生信息
     *
     * @param id 考生信息主键
     * @return 考生信息
     */
    @Override
    public PersonInfo selectPersonInfoById(String id)
    {
        return personInfoMapper.selectPersonInfoById(id);
    }

    /**
     * 查询考生信息列表
     *
     * @param personInfo 考生信息
     * @return 考生信息
     */
    @Override
    public List<PersonInfo> selectPersonInfoList(PersonInfo personInfo)
    {
        List<PersonInfo> personInfoList = personInfoMapper.selectPersonInfoList(personInfo);

//        personInfoList.forEach(pi -> {
//            if(pi.getAuditStatus()==0){//需要区分企业审核还是主管审核
//                PersonInfo infoById = getInfoById(pi.getId());
//                if(infoById.getAuditList().size()>0){
//                    if(infoById.getAuditList().get(0).getAuditStatus()==0){//是企业审核
//                        pi.setAuditStatus(AuditStatusEnum.WAIT_CORP_AUDIT.getCode());
//                    }
//                }
////                if(infoById.getAuditList().get(0).getAuditStatus()==0){//是企业审核
////                    pi.setAuditStatus(AuditStatusEnum.WAIT_CORP_AUDIT.getCode());
////                }
//            }
//        });
        return personInfoList;
    }

    /**
     * 新增考生信息
     *
     * @param personInfo 考生信息
     * @return 结果
     */
    @Override
    public int insertPersonInfo(PersonInfoImport personInfo)
    {
        personInfo.setId(String.valueOf(IdUtil.getSnowflake().nextId()));
        personInfo.setCreateBy(SecurityUtils.getUserId().toString());
        personInfo.setCreateTime(DateUtils.getNowDate());
        personInfo.setUpdateTime(DateUtils.getNowDate());
        personInfo.setUpdateBy(SecurityUtils.getUserId().toString());
        personInfo.setHand(0);
        personInfo.setFlag(0);
        return personInfoMapper.insertPersonInfo(personInfo);
    }

    /**
     * 修改考生信息
     *
     * @param personInfo 考生信息
     * @return 结果
     */
    @Override
    public int updatePersonInfo(PersonInfo personInfo)
    {
        if(personInfo.getFlag()==null){
            personInfo.setUpdateBy("1");
        }
        else {
            personInfo.setUpdateBy(SecurityUtils.getUserId().toString());
        }
        personInfo.setUpdateTime(DateUtils.getNowDate());
        return personInfoMapper.updatePersonInfo(personInfo);
    }

    /**
     * 批量删除考生信息
     *
     * @param ids 需要删除的考生信息主键
     * @return 结果
     */
    @Override
    public int deletePersonInfoByIds(String[] ids)
    {
        return personInfoMapper.deletePersonInfoByIds(ids);
    }

    /**
     * 删除考生信息信息
     *
     * @param id 考生信息主键
     * @return 结果
     */
    @Override
    public int deletePersonInfoById(String id)
    {
        return personInfoMapper.deletePersonInfoById(id);
    }

    /**
     * 根据身份证号获取考生信息
     * @param  idCard 身份证号
     */
    @Override
    public PersonInfo getInfoByIdCard(String idCard,Integer candidateType) {
        //考生的信息
        PersonInfo personInfo =personInfoMapper.getInfoByIdCardAndcandidateType(idCard,candidateType);
        //List<PersonInfo> personInfo =personInfoMapper.getPersonInfoByIdCard(IdCard);
        if(personInfo==null){
            return null;
        }
        personInfo.setAddressExam("41".equals(personInfo.getAddressExam())?"河南省":personInfo.getAddressExam());
        //考生的考试项目信息
        ExamInfo examInfo = new ExamInfo();
        examInfo.setIdCard(idCard);
        List<ExamInfo> examInfoList = new ArrayList<>();
        examInfoList= examInfoService.selectExamInfoList(examInfo);
        //考生的资料
        List<PersonData> personDataList = new ArrayList<>();
        //只查询比本批次录入数据之后上传的文件
        personDataList = personDataService.selectPersonDataByIdCard(idCard);
        personDataList = personDataList.stream().filter(p->p.getCreateTime().getTime()>personInfo.getCreateTime().getTime()).collect(Collectors.toList());
        //考生审批记录
        List<Audit> auditList = new ArrayList<>();
        auditList= auditService.getByIdCard(idCard);
        personInfo.setExamInfoList(examInfoList);
        personInfo.setPersonDataList(personDataList);
        personInfo.setAuditList(auditList);
        personInfo.setBirthday(personInfo.getIdCard().substring(6,10)+"年"+personInfo.getIdCard().substring(10,12)+"月"+personInfo.getIdCard().substring(12,14)+"日");
        if(!"03".equals(SecurityUtils.getLoginUser().getUser().getUserType())){
            personInfo.setIdCard(StringUtils.Mdtm( personInfo.getIdCard()));
            personInfo.setPhoneNumber(StringUtils.Mdtm(personInfo.getPhoneNumber()));
        }
        return personInfo;
    }
    /**
     * 根据身份证号获取考生信息
     * @param  archives_no 档案号
     */
    @Override
    public PersonInfo getInfoByArchives_no(String archives_no) {
        //考生的信息
        PersonInfo personInfo =personInfoMapper.getInfoByArchives_no(archives_no);
        if(personInfo==null){
            return personInfo;
        }
        return null;

    }
    /**
     * 获取首页数据反馈
     */
    @Override
    public JSONObject getData() {
        PersonInfo personInfo = new PersonInfo();
        personInfo.setIdCard(SecurityUtils.getUsername());
        //获取考生信息
        List<PersonInfo> personInfoList = selectPersonInfoList(personInfo);
        //获取考生资料
        List<PersonData> personDataList = personDataService.selectPersonDataByIdCard(SecurityUtils.getUsername());
        //获取考生审核
        List<Audit> auditList = auditService.getByIdCard(SecurityUtils.getUsername());

        String auditStatus = null;
        if(auditList.size()==0){
            auditStatus = "未提交审核";
        }else {
            auditStatus = dictDataService.selectDictLabel("audit_status",auditList.get(0).getAuditStatus().toString());
        }

        JSONObject object = new JSONObject();
        object.put("isFinish",personInfoList.get(0).getFlag());//是否完善信息 0 否，1是
        object.put("dataNum",personDataList.size());//资料的个数
        object.put("auditStatus",auditStatus);//审核状态
        return object;
    }

    /**
     * 根据部门查看考生
     * @param sysDept
     * @return
     */
    @Override
    public List<PersonInfo> selectByDept(SysDept sysDept) {
        return personInfoMapper.selectByDept(sysDept);
    }
    /**
     * 根据部门及条件查看考生
     * @param personInfo
     * @return
     */
    @Override
    public List<PersonInfo> getByDept(PersonInfo personInfo) {
        return personInfoMapper.getByDept(personInfo);
    }

    /**
     * 考生信息导入
     * @param file
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importData(Integer candidateType, MultipartFile file) throws Exception {
        List<PersonInfoImport> list = new ArrayList<PersonInfoImport>();
        if(candidateType.equals(14)){
            ExcelUtil<PersonInfoImportJl> util = new ExcelUtil<PersonInfoImportJl>(PersonInfoImportJl.class);
            List<PersonInfoImportJl> listJl = util.importExcel(file.getInputStream());
            list = convert2Normal(listJl,null);
        } else if (candidateType.equals(1)||candidateType.equals(2)) {
            ExcelUtil<PersonInfoImportJl> util = new ExcelUtil<PersonInfoImportJl>(PersonInfoImportJl.class);
            List<PersonInfoImportJl> listJl = util.importExcel(file.getInputStream());
            list = convert2Normal(listJl,null);
        }else if (candidateType.equals(19)) {
            ExcelUtil<PersonInfoImport> util = new ExcelUtil<PersonInfoImport>(PersonInfoImport.class);
            list = util.importExcel(file.getInputStream());
            return convertPersonInfoImport(list, candidateType);
        } else{
            ExcelUtil<PersonInfoImport> util = new ExcelUtil<PersonInfoImport>(PersonInfoImport.class);
            list = util.importExcel(file.getInputStream());

        }

        if(!list.isEmpty()){
            for (PersonInfoImport personInfoImport : list) {
                if(null==personInfoImport.getCandidateType1()){
                personInfoImport.setCandidateType(candidateType);}
                else {
                    String candidateType1 = personInfoImport.getCandidateType1();
                    if ("一级注册建筑师".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(1);
                    } else if ("二级注册建筑师".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(2);
                    } else if ("勘察设计注册工程师".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(3);
                    } else if ("一级注册结构工程师".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(4);
                    } else if ("二级注册结构工程师".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(5);
                    } else if ("注册土木工程师（岩土）".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(6);
                    } else if ("注册土木工程师（港口与航道工程）".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(7);
                    } else if ("注册公用设备工程师（暖通空调）".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(8);
                    } else if ("注册公用设备工程师（给水排水）".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(9);
                    } else if ("注册公用设备工程师（动力）".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(10);
                    } else if ("注册电气工程师（发输变电）".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(11);
                    } else if ("注册电气工程师（供配电）".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(12);
                    } else if ("注册化工工程师".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(13);
                    } else if ("注册监理工程师".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(14);
                    } else if ("注册建造师".equals(candidateType1.trim())) {
                        // 注意：这里你有一个“注册建造师”但没有指定是一级还是二级，可能需要进一步细化
                        personInfoImport.setCandidateType(15); // 假设这是通用的注册建造师类型
                    } else if ("一级注册建造师".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(16);
                    } else if ("二级注册建造师".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(17);
                    } else if ("注册造价工程师".equals(candidateType1.trim())) {
                        // 同样，这里你有一个“注册造价工程师”但没有指定是一级还是二级
                        // 根据你的业务逻辑，你可能需要处理这种情况
                         personInfoImport.setCandidateType(18); // 需要一个默认值或者进一步细化
                    } else if ("一级注册造价工程师".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(19);
                    } else if ("二级注册造价工程师".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(20);
                    } else if ("注册建筑师".equals(candidateType1.trim())) {
                        // 注意：这里你有一个泛指的“注册建筑师”，它可能与“一级注册建筑师”或“二级注册建筑师”不同
                        // 根据你的业务逻辑，你可能需要处理这种情况
                         personInfoImport.setCandidateType(21); // 需要一个默认值或者进一步细化，或者与上面的某个类型合并
                    } else if ("注册土木工程师(水利水电工程(水利水电工程规划))".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(22);
                    } else if ("注册土木工程师(水利水电工程(水工结构))".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(23);
                    } else if ("注册土木工程师(水利水电工程(水利水电工程地质))".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(24);
                    } else if ("注册土木工程师(水利水电工程(水利水电工程移民))".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(25);
                    } else if ("注册土木工程师(水利水电工程(水利水电工程水土保持))".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(26);
                    } else if ("注册土木工程师（道路工程）".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(27);
                    } else if ("注册环保工程师".equals(candidateType1.trim())) {
                        personInfoImport.setCandidateType(28);
                    } else {
                        // 处理未知类型
                        System.out.println("未知的候选人类型: " + candidateType);
                        // 可以选择设置一个默认类型或者抛出异常等
                        // personInfoImport.setCandidateType(someDefaultValue);
                    }
                }
                Map<String, Object> sexFromIdCard = getSexFromIdCard(personInfoImport.getIdCard());
                Object sexByInt = sexFromIdCard.get("sex_by_int");
                Long aLong = Long.valueOf(sexByInt.toString());
                personInfoImport.setSex(aLong.intValue());
                int result =0;

                if(personInfoImport.getAddressExam() == null) {
                    personInfoImport.setAddressExam(personInfoImport.getCityName().substring(0, personInfoImport.getCityName().indexOf("省") + 1));
                }
                if ("省直".equals(personInfoImport.getCityName())){
                    personInfoImport.setCityName("省直");
                }else {
                    personInfoImport.setCityName(personInfoImport.getCityName().substring(personInfoImport.getCityName().indexOf("省")+1));
                }

                String zzmm = personInfoImport.getZzmm();
                if ("中国共产党党员".equals(zzmm)){
                    personInfoImport.setPoliticalOutlook(1);
                }else if ("中国共产主义青年团团员".equals(zzmm)){
                    personInfoImport.setPoliticalOutlook(2);
                }else if ("九三学社社员".equals(zzmm)){
                    personInfoImport.setPoliticalOutlook(5);
                }else if ("群众".equals(zzmm)){
                    personInfoImport.setPoliticalOutlook(4);
                }else if ("无党派民主人士".equals(zzmm)){
                    personInfoImport.setPoliticalOutlook(6);
                }else if ("中国共产党预备党员".equals(zzmm)){
                    personInfoImport.setPoliticalOutlook(7);
                }else if ("中国民主建国会会员".equals(zzmm)) {
                    personInfoImport.setPoliticalOutlook(8);
                }
                String xl = personInfoImport.getXl();
                if ("博士研究生毕业".equals(xl)){
                    personInfoImport.setEducation(0);
                }else if ("硕士研究生毕业".equals(xl)){
                    personInfoImport.setEducation(1);
                }else if ("硕研以上".equals(xl)){
                    personInfoImport.setEducation(1);
                }else if ("大学本科毕业".equals(xl)){
                    personInfoImport.setEducation(2);
                }else if ("大学专科毕业".equals(xl)){
                    personInfoImport.setEducation(3);
                }else if ("中专".equals(xl)){
                    personInfoImport.setEducation(4);
                }else if ("高中".equals(xl)){
                    personInfoImport.setEducation(5);
                }else if ("初中".equals(xl)){
                    personInfoImport.setEducation(6);
                }else if ("职高".equals(xl)){
                    personInfoImport.setEducation(7);
                } else if ("研究生班毕业".equals(xl)){
                    personInfoImport.setEducation(9);
                }else {
                    personInfoImport.setEducation(8);
                }
                //todo 分批次进行数据导入
                if (personInfoImport.getInputType() == null) {
                        personInfoImport.setInputType(31);
                }
                //毕业时间处理
//                String year = StringUtils.substring(personInfoImport.getGraduationTime(),0,4);
//                String month = personInfoImport.getGraduationTime().substring(4,6);
//                personInfoImport.setGraduationTime(year+"年"+month+"月");
                if(StringUtils.isNotEmpty(personInfoImport.getWork())){
                    personInfoImport.setCorpCode(companyService.getByName(personInfoImport.getWork()));
                }
                if("0".equals(personInfoImport.getNationality())){
                    personInfoImport.setNationality("中国");
                }
                //如果该考生信息已经存在则替换之前的所有信息
                if(personInfoMapper.getInfoByIdCardAndcandidateType(personInfoImport.getIdCard(),candidateType)!=null){
                    result = updatePersonInfoByIdCard(personInfoImport);
                    //删除考试信息
                    ExamInfo examInfo = new ExamInfo();
                    examInfo.setIdCard(personInfoImport.getIdCard());
                    examInfo.setCandidateType(candidateType);
                    List<ExamInfo> examInfoList = examInfoService.selectExamInfoList(examInfo);
                    for (ExamInfo info : examInfoList) {
                        examInfoService.deleteExamInfoById(info.getId());
                    }
                }else if (sysUserService.selectAllUserByUserName(personInfoImport.getIdCard())!=null) {
                    SysUser sysUser = new SysUser();
                    sysUser.setUserName(personInfoImport.getIdCard());
                    sysUser.setPhonenumber(personInfoImport.getPhoneNumber());
                    sysUser.setStatus("0");
                    sysUser.setDelFlag("0");
                    sysUserService.updateUserStatusByUserName(sysUser);
                    result = insertPersonInfo(personInfoImport);
                }else {
                    //首次添加
                    result = insertPersonInfo(personInfoImport);
                }
                if(result==1){
                    ExamInfo examInfo = new ExamInfo();
                    examInfo.setIdCard(personInfoImport.getIdCard());
                    examInfo.setCandidateType(candidateType);
                    if(StringUtils.isNotEmpty(personInfoImport.getKh_one())){
                        examInfo.setExamSubject("科目1");
                        examInfo.setExamCode(personInfoImport.getKh_one());
                        examInfoService.insertExamInfo(examInfo);
                    }
                    if(StringUtils.isNotEmpty(personInfoImport.getKh_two())) {
                        examInfo.setExamSubject("科目2");
                        examInfo.setExamCode(personInfoImport.getKh_two());
                        examInfoService.insertExamInfo(examInfo);
                    }
                    if(StringUtils.isNotEmpty(personInfoImport.getKh_three())) {
                        examInfo.setExamSubject("科目3");
                        examInfo.setExamCode(personInfoImport.getKh_three());
                        examInfoService.insertExamInfo(examInfo);

                    }
                    if(StringUtils.isNotEmpty(personInfoImport.getKh_four())) {
                        examInfo.setExamSubject("科目4");
                        examInfo.setExamCode(personInfoImport.getKh_four());
                        examInfoService.insertExamInfo(examInfo);

                    }
                    if(StringUtils.isNotEmpty(personInfoImport.getKh_five())) {
                        examInfo.setExamSubject("科目5");
                        examInfo.setExamCode(personInfoImport.getKh_five());
                        examInfoService.insertExamInfo(examInfo);

                    }
                    if(StringUtils.isNotEmpty(personInfoImport.getKh_six())) {
                        examInfo.setExamSubject("科目6");
                        examInfo.setExamCode(personInfoImport.getKh_six());
                        examInfoService.insertExamInfo(examInfo);

                    }
                    if(StringUtils.isNotEmpty(personInfoImport.getKh_seven())) {
                        examInfo.setExamSubject("科目7");
                        examInfo.setExamCode(personInfoImport.getKh_seven());
                        examInfoService.insertExamInfo(examInfo);

                    }
                    if(StringUtils.isNotEmpty(personInfoImport.getKh_eight())) {
                        examInfo.setExamSubject("科目8");
                        examInfo.setExamCode(personInfoImport.getKh_eight());
                        examInfoService.insertExamInfo(examInfo);

                    }
                    if(StringUtils.isNotEmpty(personInfoImport.getKh_nine())) {
                        examInfo.setExamSubject("科目9");
                        examInfo.setExamCode(personInfoImport.getKh_nine());
                        examInfoService.insertExamInfo(examInfo);

                    }
                }else {
                    return personInfoImport.getName()+"的数据有问题，请处理后再添加";
                }
            }
        }
        return "导入成功";
    }

    private String convertPersonInfoImport(List<PersonInfoImport> list,Integer candidateType) {
        for (PersonInfoImport personInfoImport : list) {
            personInfoImport.setCandidateType(candidateType);
            personInfoImport.setInputType(19);
            Map<String, Object> sexFromIdCard = getSexFromIdCard(personInfoImport.getIdCard());
            Object sexByInt = sexFromIdCard.get("sex_by_int");
            Long aLong = Long.valueOf(sexByInt.toString());
            personInfoImport.setSex(aLong.intValue());
            int result =0;
            personInfoImport.setAddressExam(personInfoImport.getCityName().substring(0,personInfoImport.getCityName().indexOf("省")+1));
            if(StringUtils.isNotEmpty(personInfoImport.getWork())){
                personInfoImport.setCorpCode(companyService.getByName(personInfoImport.getWork()));
            }
            if(StringUtils.isEmpty(personInfoImport.getAddress())){
                personInfoImport.setAddress("无");
            }
            if (personInfoImport.getCityName().equals("省直")){
                personInfoImport.setCityName("省直");
            }else {
                personInfoImport.setCityName(personInfoImport.getCityName().substring(personInfoImport.getCityName().indexOf("省")+1));
            }
            String xl = personInfoImport.getXl();
            if(xl != null) {
                if ("博士毕业".equals(xl)) {
                    personInfoImport.setEducation(0);
                } else if ("硕士研究生毕业".equals(xl)) {
                    personInfoImport.setEducation(1);
                } else if ("大学本科毕业".equals(xl)) {
                    personInfoImport.setEducation(2);
                } else if ("大学专科毕业".equals(xl)) {
                    personInfoImport.setEducation(3);
                } else if ("中专毕业".equals(xl)) {
                    personInfoImport.setEducation(4);
                } else if ("高中毕业".equals(xl)) {
                    personInfoImport.setEducation(5);
                } else if ("初中毕业".equals(xl)) {
                    personInfoImport.setEducation(6);
                } else if ("职高毕业".equals(xl)) {
                    personInfoImport.setEducation(7);
                } else {
                    personInfoImport.setEducation(8);
                }
            }

            String zzmm = personInfoImport.getZzmm();
            if ("中国共产党党员".equals(zzmm)){
                personInfoImport.setPoliticalOutlook(1);
            }else if ("中国共产主义青年团团员".equals(zzmm)){
                personInfoImport.setPoliticalOutlook(2);
            }else if ("九三学社社员".equals(zzmm)){
                personInfoImport.setPoliticalOutlook(5);
            }else if ("群众".equals(zzmm)){
                personInfoImport.setPoliticalOutlook(4);
            }else if ("无党派民主人士".equals(zzmm)){
                personInfoImport.setPoliticalOutlook(6);
            }else if ("中国共产党预备党员".equals(zzmm)){
                personInfoImport.setPoliticalOutlook(7);
            }else if ("中国民主建国会会员".equals(zzmm)) {
                personInfoImport.setPoliticalOutlook(8);
            }
            //如果该考生信息已经存在则替换之前的所有信息
            if(personInfoMapper.getInfoByIdCardAndcandidateType(personInfoImport.getIdCard(),candidateType)!=null){
                result = updatePersonInfoByIdCard(personInfoImport);
                //删除考试信息
                ExamInfo examInfo = new ExamInfo();
                examInfo.setIdCard(personInfoImport.getIdCard());
                examInfo.setCandidateType(candidateType);
                List<ExamInfo> examInfoList = examInfoService.selectExamInfoList(examInfo);
                for (ExamInfo info : examInfoList) {
                    examInfoService.deleteExamInfoById(info.getId());
                }
            }else {//首次添加
                result = insertPersonInfo(personInfoImport);
            }
            if(result==1){
                ExamInfo examInfo = new ExamInfo();
                examInfo.setIdCard(personInfoImport.getIdCard());
                examInfo.setCandidateType(candidateType);
                if(StringUtils.isNotEmpty(personInfoImport.getKh_one())){
                    examInfo.setExamSubject("科目1");
                    examInfo.setExamCode(personInfoImport.getKh_one());
                    examInfoService.insertExamInfo(examInfo);
                }
                if(StringUtils.isNotEmpty(personInfoImport.getKh_two())) {
                    examInfo.setExamSubject("科目2");
                    examInfo.setExamCode(personInfoImport.getKh_two());
                    examInfoService.insertExamInfo(examInfo);
                }
                if(StringUtils.isNotEmpty(personInfoImport.getKh_three())) {
                    examInfo.setExamSubject("科目3");
                    examInfo.setExamCode(personInfoImport.getKh_three());
                    examInfoService.insertExamInfo(examInfo);

                }
                if(StringUtils.isNotEmpty(personInfoImport.getKh_four())) {
                    examInfo.setExamSubject("科目4");
                    examInfo.setExamCode(personInfoImport.getKh_four());
                    examInfoService.insertExamInfo(examInfo);

                }
                if(StringUtils.isNotEmpty(personInfoImport.getKh_five())) {
                    examInfo.setExamSubject("科目5");
                    examInfo.setExamCode(personInfoImport.getKh_five());
                    examInfoService.insertExamInfo(examInfo);

                }
                if(StringUtils.isNotEmpty(personInfoImport.getKh_six())) {
                    examInfo.setExamSubject("科目6");
                    examInfo.setExamCode(personInfoImport.getKh_six());
                    examInfoService.insertExamInfo(examInfo);

                }
                if(StringUtils.isNotEmpty(personInfoImport.getKh_seven())) {
                    examInfo.setExamSubject("科目7");
                    examInfo.setExamCode(personInfoImport.getKh_seven());
                    examInfoService.insertExamInfo(examInfo);

                }
                if(StringUtils.isNotEmpty(personInfoImport.getKh_eight())) {
                    examInfo.setExamSubject("科目8");
                    examInfo.setExamCode(personInfoImport.getKh_eight());
                    examInfoService.insertExamInfo(examInfo);

                }
                if(StringUtils.isNotEmpty(personInfoImport.getKh_nine())) {
                    examInfo.setExamSubject("科目9");
                    examInfo.setExamCode(personInfoImport.getKh_nine());
                    examInfoService.insertExamInfo(examInfo);

                }
            }else {
                return personInfoImport.getName()+"的数据有问题，请处理后再添加";
            }
        }
        return "导入成功";
    }


    /**
     * 根据身份证号修改
     * @param personInfoImport
     */
 /*   private int updatePersonInfoByIdCard(PersonInfoImport personInfoImport) {
       return personInfoMapper.updatePersonInfoByIdCard(personInfoImport);
    }*/

    private int updatePersonInfoByIdCard(PersonInfoImport personInfoImport) {
        PersonInfo personInfo = new PersonInfo();
        personInfo.setIdCard(personInfoImport.getIdCard());
        personInfo.setCandidateType(personInfoImport.getCandidateType());
        //查询出该考生之前的老数据
        PersonInfo info = personInfoMapper.selectPersonInfoByIdCardAndCandType(personInfo);

        if(info!=null){
            //将老数据删除
            personInfoMapper.deletePersonInfoById(info.getId());
            //将老数据账号启用
            SysUser user = sysUserService.selectAllUserByUserName(info.getIdCard());
            if (user != null){
                List<SysRole> roles = user.getRoles();
                if(roles != null && roles.size() > 0 && roles.get(0) != null) {
                    user.setRoleIds(new Long[]{roles.get(0).getRoleId()});
                }
//                if(user.getRoleId() != null) {
//                    user.setRoleIds(new Long[]{user.getRoleId()});
//                }
                user.setStatus("0");
                user.setDelFlag("0");
                sysUserService.updateUser(user);
            }

        }
        //新增新数据
        return insertPersonInfo(personInfoImport);
    }

    /**
     * 根据Id获取考生信息
     * @param  id
     */
    @Override
    public PersonInfo getInfoById(String id) {
        //考生的信息
        PersonInfo personInfo =personInfoMapper.selectPersonInfoById(id);
        if(personInfo==null){
            return personInfo;
        }

        personInfo.setAddressExam("41".equals(personInfo.getAddressExam())?"河南省":personInfo.getAddressExam());
        //考生的考试项目信息
        ExamInfo examInfo = new ExamInfo();
        examInfo.setIdCard(personInfo.getIdCard());
        examInfo.setCandidateType(personInfo.getCandidateType());
        List<ExamInfo> examInfoList = new ArrayList<>();
        examInfoList= examInfoService.selectExamInfoList(examInfo);
        //考生的资料
        List<PersonData> personDataList = new ArrayList<>();
        personDataList = personDataService.selectPersonDataByIdCard(personInfo.getIdCard());
        //考生审批记录
        List<Audit> auditList = new ArrayList<>();
        auditList= auditService.getByIdCard1(personInfo.getIdCard(),personInfo.getCandidateType());
        personInfo.setExamInfoList(examInfoList);
        personInfo.setPersonDataList(personDataList);
        personInfo.setAuditList(auditList);
        String idCard = personInfo.getIdCard();
        personInfo.setBirthday(idCard.substring(6,10)+"年"+ idCard.substring(10,12)+"月"+ idCard.substring(12,14)+"日");
//        if(!"03".equals(SecurityUtils.getLoginUser().getUser().getUserType())){
//            personInfo.setIdCard(StringUtils.Mdtm(idCard));
//            personInfo.setPhoneNumber(StringUtils.Mdtm(personInfo.getPhoneNumber()));
//        }
        return personInfo;
    }
    /**
     *检查注册根据身份证号
     * @param IdCard
     * @return
     */
    @Override
    public PersonInfo checkRegister(String IdCard) {
        PersonInfo personInfo = new PersonInfo();
        PersonInfo info =  personInfoMapper.getInfoByIdCard1(IdCard);
        if(info != null){
            personInfo.setId(info.getId());
            personInfo.setSex(info.getSex());
            return personInfo;
        }
        return null;
    }

    @Override
    public SysUser checkResetInfo(ResetPwdBody resetPwdBody) {
        Objects.requireNonNull(resetPwdBody.getUserName(),"身份证号不能为空！");
        Objects.requireNonNull(resetPwdBody.getNickName(),"姓名不能为空！");
        Objects.requireNonNull(resetPwdBody.getPhoneNumber(),"手机号码不能为空！");
        Objects.requireNonNull(resetPwdBody.getPassword(),"密码不能为空！");
        if(resetPwdBody.getPassword().length() < UserConstants.PASSWORD_MIN_LENGTH
                || resetPwdBody.getPassword().length() > UserConstants.PASSWORD_MAX_LENGTH){
            throw new ServiceException("密码长度必须在5到20个字符之间");
        }
        SysUser sysUser = sysUserService.selectUserByUserName(resetPwdBody.getUserName());
        Optional.ofNullable(sysUser).orElseThrow(()->new ServiceException("未查询到用户"));
        if(!StringUtils.equals(sysUser.getNickName(),resetPwdBody.getNickName())){
            throw new ServiceException("姓名与注册时填写不一致！");
        }
        if(!StringUtils.equals(sysUser.getPhonenumber(),resetPwdBody.getPhoneNumber())){
            throw new ServiceException("电话号码与注册预留号码不一致！");
        }
        return sysUser;
    }

    @Override
    public List<PersonInfo> getPersonInfoByIdCard(String IdCard) {
        List<PersonInfo> personInfoByIdCard = personInfoMapper.getPersonInfoByIdCard(IdCard);
        for (PersonInfo info : personInfoByIdCard) {
            PersonInfo infoById = getInfoById(info.getId());
            if (infoById.getAuditList().size()>0) {
                info.setAuditList(infoById.getAuditList());
            }else {
                Audit audit = new Audit();
                ArrayList<Audit> auditList = new ArrayList<>();
                auditList.add(audit);
                info.setAuditList(auditList);
            }
//            info.setExamInfoList(infoById.getExamInfoList());
//            info.setPersonDataList(infoById.getPersonDataList());
        }
        return personInfoByIdCard;
    }

    @Override
    public JSONObject getDataIndex(Integer candidateType) {
        JSONObject object = new JSONObject();
        PersonInfo personInfo = new PersonInfo();
        personInfo.setCandidateType(candidateType);
        personInfo.setIdCard(SecurityUtils.getUsername());
        //获取考生信息
//        List<PersonInfo> personInfoList = selectPersonInfoList(personInfo);
        PersonInfo personInfoOne=personInfoMapper.selectPersonInfoByIdCardAndCandType(personInfo);
        object.put("isFinish",personInfoOne.getFlag());//资料的个数
        if (personInfoOne.getRegisterStatus()!=null && personInfoOne.getRegisterStatus()==1){
            object.put("registerStatus",1);
            return object;
        }

        //获取考生资料
        List<PersonData> personDataList = personDataService.selectPersonDataByIdCard(SecurityUtils.getUsername());
        //获取考生审核
        List<Audit> auditList = auditService.getByIdCard1(SecurityUtils.getUsername(),candidateType);

        String auditStatus = null;
        if(auditList.size()==0){
            auditStatus = "未提交审核";
        }else {
//            auditStatus = dictDataService.selectDictLabel("audit_status",auditList.get(0).getAuditStatus().toString());
            auditStatus = dictDataService.selectDictLabel("audit_status",personInfoOne.getAuditStatus().toString());
        }



        object.put("dataNum",personDataList.size());//资料的个数
        object.put("auditStatus",auditStatus);//审核状态
        return object;
    }

    @Override
    public PersonInfo getInfoByIdCardAndcandidateType(String idCard, Integer candidateType) {
        PersonInfo personInfo = personInfoMapper.getInfoByIdCardAndcandidateType(idCard, candidateType);

        //考生的信息
        //PersonInfo personInfo =personInfoMapper.selectPersonInfoById(id);
        if(personInfo==null){
            return personInfo;
        }

        personInfo.setAddressExam("41".equals(personInfo.getAddressExam())?"河南省":personInfo.getAddressExam());
        //考生的考试项目信息
        ExamInfo examInfo = new ExamInfo();
        examInfo.setIdCard(personInfo.getIdCard());
        examInfo.setCandidateType(personInfo.getCandidateType());
        List<ExamInfo> examInfoList = new ArrayList<>();
        examInfoList= examInfoService.selectExamInfoList(examInfo);
        //考生的资料
        List<PersonData> personDataList = new ArrayList<>();
        personDataList = personDataService.selectPersonDataByIdCard(personInfo.getIdCard());
        //todo   区别新老数据。将新数据中新增的字段inputType作为查询条件，替换掉查询出来的考生资料老数据
        List<PersonData> collect = personDataList.stream().filter(p -> p.getCandidateType() != null).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            PersonData personData = new PersonData();
            personData.setIdCard(personInfo.getIdCard());
            personData.setCandidateType(personInfo.getCandidateType());
            personDataList = personDataService.selectPersonDataList(personData);

        }
        //只查询比本批次录入数据之后上传的文件
        personDataList = personDataList.stream().filter(p->p.getCreateTime().getTime()>personInfo.getCreateTime().getTime()).collect(Collectors.toList());
        //考生审批记录
        List<Audit> auditList = new ArrayList<>();
        auditList= auditService.getByIdCard1(personInfo.getIdCard(),personInfo.getCandidateType());
        personInfo.setExamInfoList(examInfoList);
        personInfo.setPersonDataList(personDataList);
        personInfo.setAuditList(auditList);
        String idCard1 = personInfo.getIdCard();
        personInfo.setBirthday(idCard1.substring(6,10)+"年"+ idCard1.substring(10,12)+"月"+ idCard1.substring(12,14)+"日");
        if(!"03".equals(SecurityUtils.getLoginUser().getUser().getUserType())){
            personInfo.setIdCard(StringUtils.Mdtm(idCard1));
            personInfo.setPhoneNumber(StringUtils.Mdtm(personInfo.getPhoneNumber()));
        }
        return personInfo;



        //return infoByIdCardAndcandidateType;
    }

    @Override
    public List<PersonInfo> getInfoByIdCard1(String id) {
        //考生的信息
        List<PersonInfo> personInfos =personInfoMapper.selectPersonInfoById1(id);
        if(personInfos==null){
            return new ArrayList<PersonInfo>();
        }
        personInfos.stream().map(item->{
            item.setAddressExam("41".equals(item.getAddressExam())?"河南省":item.getAddressExam());
            //考生的考试项目信息
            ExamInfo examInfo = new ExamInfo();
            examInfo.setIdCard(item.getIdCard());
            List<ExamInfo> examInfoList = new ArrayList<>();
            examInfoList= examInfoService.selectExamInfoList(examInfo);
            //考生的资料
            List<PersonData> personDataList = new ArrayList<>();
            personDataList = personDataService.selectPersonDataByIdCard(item.getIdCard());
            //考生审批记录
            List<Audit> auditList = new ArrayList<>();
            auditList= auditService.getByIdCard(item.getIdCard());
            item.setExamInfoList(examInfoList);
            item.setPersonDataList(personDataList);
            item.setAuditList(auditList);
            String idCard1 = item.getIdCard();
            item.setBirthday(idCard1.substring(6,10)+"年"+ idCard1.substring(10,12)+"月"+ idCard1.substring(12,14)+"日");
            if(!"03".equals(SecurityUtils.getLoginUser().getUser().getUserType())){
                item.setIdCard(StringUtils.Mdtm(idCard1));
                item.setPhoneNumber(StringUtils.Mdtm(item.getPhoneNumber()));
            }
            return item;
        }).collect(Collectors.toList());

        return personInfos;
    }

    @Override
    public PersonInfo getInfoById1(String id) {

        PersonInfo personInfo = personInfoMapper.getOneInfoexamInfo(id);
        // todo 区别新老数据。将新数据中新增的字段inputType作为查询条件，替换掉查询出来的考生资料老数据
        List<PersonData> collect = personInfo.getPersonDataList().stream().filter(p -> p.getCandidateType() != null).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            PersonData personData = new PersonData();
            personData.setIdCard(personInfo.getIdCard());
            personData.setCandidateType(personInfo.getCandidateType());
            List<PersonData> personDataList = personDataService.selectPersonDataList(personData);
            personInfo.setPersonDataList(personDataList);
        }

        //考生的信息
        //PersonInfo personInfo =personInfoMapper.selectPersonInfoById(id);
        if(personInfo==null){
            return personInfo;
        }

        personInfo.setAddressExam("41".equals(personInfo.getAddressExam())?"河南省":personInfo.getAddressExam());
        //考生的考试项目信息
//        ExamInfo examInfo = new ExamInfo();
//        examInfo.setIdCard(personInfo.getIdCard());
//        examInfo.setCandidateType(personInfo.getCandidateType());
//        List<ExamInfo> examInfoList = new ArrayList<>();
//        examInfoList= examInfoService.selectExamInfoList(examInfo);
        //考生的资料
//        List<PersonData> personDataList = new ArrayList<>();
//        personDataList = personDataService.selectPersonDataByIdCard(personInfo.getIdCard());
        //考生审批记录
        List<Audit> auditList = new ArrayList<>();
        auditList= auditService.getByIdCard1(personInfo.getIdCard(),personInfo.getCandidateType());
//        personInfo.setExamInfoList(examInfoList);
//        personInfo.setPersonDataList(personDataList);
        personInfo.setAuditList(auditList);
        String idCard = personInfo.getIdCard();
        personInfo.setBirthday(idCard.substring(6,10)+"年"+ idCard.substring(10,12)+"月"+ idCard.substring(12,14)+"日");
//        if(!"03".equals(SecurityUtils.getLoginUser().getUser().getUserType())){
//            personInfo.setIdCard(StringUtils.Mdtm(idCard));
//            personInfo.setPhoneNumber(StringUtils.Mdtm(personInfo.getPhoneNumber()));
//        }
        return personInfo;
    }

    @DataScope(deptAlias = "p")
    @Override
    public List<PersonInfo> selectPersonInfoLists(PersonInfo personInfo) {


//
     /*   String username = SecurityUtils.getUsername();
        //账号jianli的四个账号展示不同考生，根据工作年限分配给不同的审核人员
        if (username.contains("jianli")){
            String s = username.substring(6, 7);
            if ("1".equals(s)){
                personInfo.setWorkTimeMin(0);
                personInfo.setWorkTimeMax(8);
            }else if ("2".equals(s)){
                personInfo.setWorkTimeMin(8);
                personInfo.setWorkTimeMax(10);
            }else if ("3".equals(s)){
                personInfo.setWorkTimeMin(10);
                personInfo.setWorkTimeMax(15);
            }else if ("4".equals(s)) {
                personInfo.setWorkTimeMin(15);
                personInfo.setWorkTimeMax(100);
            }
        }*/
        PageUtils.startPage();

        return personInfoMapper.selectPersonInfoLists1(personInfo);
    }



    @DataScope(deptAlias = "p")
    @Override
    public List<PersonInfo> selectPersonInfoaudirStatusIsNotNull(PersonInfo personInfo) {
        List<PersonInfo> personInfos = personInfoMapper.selectPersonInfoaudirStatusIsNotNull(personInfo);
        if (CollectionUtils.isNotEmpty(personInfos)){
            personInfos.stream().map(p -> {
                String idCard = p.getIdCard();
                Integer candidateType = p.getCandidateType();
                Audit oneAudit = auditMapper.getOneAudit(idCard, candidateType);
                if (oneAudit != null) {
                    p.setAuditListStatus(oneAudit.getAuditStatus());
                    p.setNumber(oneAudit.getNumber());
                    p.setAuditId(oneAudit.getId());
                }
                return p;
            }).collect(Collectors.toList());
        }
        return personInfos;
    }

    @DataScope(deptAlias = "p")
    @Override
    public List<PersonInfo> selectPersonInfoAuditStatusIsNull(PersonInfo personInfo) {
        List<PersonInfo> personInfos = personInfoMapper.selectPersonInfoAuditStatusIsNull(personInfo);
        return personInfos;
    }

    @Override
    public Integer selectPersonInfoListsCount(PersonInfo personInfo) {
        return personInfoMapper.selectPersonInfoListsCount(personInfo);
    }

    @Override
    public List<PersonInfo> selectPersonInfoListsIndex(PersonInfo personInfo) {
        List<PersonInfo> personInfoList = personInfoMapper.selectPersonInfoLists1(personInfo);
        if (CollectionUtils.isNotEmpty(personInfoList)){
            personInfoList.stream().map(p -> {
                String idCard = p.getIdCard();
                Integer candidateType = p.getCandidateType();
                Audit oneAudit = auditMapper.getOneAudit(idCard, candidateType);
                if (oneAudit != null) {
                    p.setAuditListStatus(oneAudit.getAuditStatus());
                    p.setNumber(oneAudit.getNumber());
                    p.setAuditId(oneAudit.getId());
                }
                return p;
            }).collect(Collectors.toList());
        }
        return personInfoList;
    }

    @DataScope(deptAlias = "p")
    @Override
    public JSONObject generateAuditStatusIndex(PersonInfo personInfo) {
        SysDept sysDept = sysDeptService.selectDeptById(SecurityUtils.getDeptId());
        String username = SecurityUtils.getUsername();
        //todo 创建审核账号需修改  一级注册造价师审查首页显示初始条件
        if (sysDept!=null && "一级注册造价师审核部门".equals(sysDept.getDeptName())){
            personInfo.setDeptId(sysDept.getDeptId().toString());
        }
        if (sysDept != null && "一级注册建筑师审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(13);
            personInfo.setCandidateType(1);
//                personInfo.setDeptId(sysDept.getDeptId().toString());
        }
        if (sysDept != null && "二级注册建筑师审核部门".equals(sysDept.getDeptName())) {
            personInfo.setInputType(14);
            personInfo.setCandidateType(2);
//                personInfo.setDeptId(sysDept.getDeptId().toString());
        }


        //todo 2024-3-26 勘察设计审核账号创建
        if ("kanchasheji01".equals(SecurityUtils.getUsername())){
            personInfo.setInputType(11);
        }

        //注册监理工程师审核部门
        if (sysDept!=null && "注册监理工程师审核部门".equals(sysDept.getDeptName())){
//            personInfo.setDeptId(sysDept.getDeptId().toString());
            if ("注册监理审核01".equals(username) || "注册监理审核10".equals(username) || "注册监理审核11".equals(username) || "注册监理审核12".equals(username) ){
                personInfo.setInputType(12);
            }else if ("注册监理审核02".equals(username)){
                personInfo.setInputType(12);
                personInfo.setType("免二科");
            }else if ("注册监理审核03".equals(username)){
                personInfo.setInputType(12);
                personInfo.setType("增报专业");
            }else if ("注册监理审核04".equals(username) || "注册监理审核05".equals(username) || "注册监理审核06".equals(username)|| "注册监理审核07".equals(username) || "注册监理审核08".equals(username)){
                personInfo.setInputType(12);
                personInfo.setType("考全科");
            }
        }
        if (personInfo.getInputType() == null) {
            //二级建造师2024-9-19
            personInfo.setInputType(16);
        }

        JSONObject jsonObject = new JSONObject();
        personInfo.setAuditStatus(null);
        personInfo.setAuditResult(null);
        Integer sum = personInfoMapper.selectPersonInfoListsCount(personInfo);
        personInfo.setAuditStatus(2);
        personInfo.setAuditListStatus(5);
        Integer noAudit = personInfoMapper.selectPersonInfoListsCount(personInfo);
        personInfo.setAuditListStatus(null);
        personInfo.setAuditStatus(3);
        Integer yesAudit = personInfoMapper.selectPersonInfoListsCount(personInfo);
        personInfo.setAuditStatus(4);
        Integer auditnotpassed = personInfoMapper.selectPersonInfoListsCount(personInfo);
        personInfo.setAuditStatus(null);
        personInfo.setAuditResult(0);
        Integer noAdd = personInfoMapper.selectPersonInfoListsCount(personInfo);

        jsonObject.put("sum",sum);//考生人数
        jsonObject.put("noAudit",noAudit);//未审核
        jsonObject.put("yesAudit",yesAudit);//已审核
        jsonObject.put("noAdd",noAdd);//未参加审核
        jsonObject.put("auditnotpassed",auditnotpassed);//主管部门审核不通过

        return jsonObject;
    }

//    @DataScope(deptAlias = "p")
    @Override
    public List<PersonInfoExport> selectPersonInfoListExport(PersonInfo personInfo) {
        String username = SecurityUtils.getUsername();
        if (username.contains("jianli")){
            String s = username.substring(6, 7);
            if ("1".equals(s)){
                personInfo.setWorkTimeMin(0);
                personInfo.setWorkTimeMax(8);
            }else if ("2".equals(s)){
                personInfo.setWorkTimeMin(8);
                personInfo.setWorkTimeMax(10);
            }else if ("3".equals(s)){
                personInfo.setWorkTimeMin(10);
                personInfo.setWorkTimeMax(15);
            }else if ("4".equals(s)) {
                personInfo.setWorkTimeMin(15);
                personInfo.setWorkTimeMax(100);
            }
        }
        return personInfoMapper.selectPersonInfoListExprot(personInfo);
    }

    @Override
    public List<String> getPersonInfoByType() {
        List<String> personInfoByType = personInfoMapper.getPersonInfoByType();
        //遍历数组如果是空置赋值为“其他”
//        personInfoByType =
//                personInfoByType.stream().map(u -> StringUtils.isBlank(u) ? "其他" : u).collect(Collectors.toList());
        //过滤空值

        personInfoByType = personInfoByType.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        return personInfoByType;
    }

    @Override
    public List<PersonInfo> selectCorpAuditPersonList(PersonInfo personInfo) {
        return personInfoMapper.selectCorpAuditPersonList(personInfo);
    }

    @Override
    public List<PersonInfo> selectPersonInfoListByEjzjsPush() {

        //查询最近一批二级造价师的批次
       Integer inputType =  personInfoMapper.selectMaxInputTypeByCandidateType(20);
       if (inputType==null){
           return new ArrayList<>();
       }
       return personInfoMapper.selectPersonInfoListByEjzjsPush(inputType);
    }

    @Override
    public int updateOldWorkByIdCard(OldWorkImport oldWorkImport) {
        return personInfoMapper.updateOldWorkByIdCard(oldWorkImport);
    }


    /**
     * 监理。建筑师。列表转化成普通列表
     * @param personInfoImportJl 监理导入列表
     * @return
     */
    private List<PersonInfoImport> convert2Normal(List<PersonInfoImportJl> personInfoImportJl,List<PersonInfoImportJZ> personInfoImportJZ){
        List<PersonInfoImport> personInfoImportList = new ArrayList<>();
        Optional.ofNullable(personInfoImportJl).ifPresent(u ->personInfoImportList.addAll(u.stream().map(jl -> {
            PersonInfoImport normal = new PersonInfoImport();
            normal.setName(jl.getName());
            normal.setSex(jl.getSex());
            normal.setPhoneNumber(jl.getPhoneNumber());
            normal.setOldWork(jl.getOldWork());
            normal.setRemark(jl.getRemark());
            normal.setAddressExam(jl.getAddressExam());
            normal.setXl(jl.getXl());
            normal.setInputType(jl.getInputType());
            normal.setZzmm(jl.getZzmm());
//            normal.setCandidateType1(jl.getCandidateType1());
            if(jl.getNation()==null){
                normal.setNation("1");
            }else {
                normal.setNation(StringUtils.equals("汉族",jl.getNation())?"1":"2");
            }
//            normal.setNation(StringUtils.equals("汉族",jl.getNation())?"1":"2");
            normal.setIdCard(jl.getIdCard());
            normal.setSchool(jl.getSchool());
//            switch (jl.getEducation()) {
//                case "博士毕业":
//                    normal.setEducation(0);
//                    break;
//                case "硕士研究生毕业":
//                    normal.setEducation(1);
//                    break;
//                case "大学本科毕业":
//                    normal.setEducation(2);
//                    break;
//                case "大学专科毕业":
//                    normal.setEducation(3);
//                    break;
//                case "中专毕业":
//                    normal.setEducation(4);
//                    break;
//                case "高中毕业":
//                    normal.setEducation(5);
//                    break;
//                case "初中毕业":
//                    normal.setEducation(6);
//                    break;
//                case "职高毕业":
//                    normal.setEducation(7);
//                    break;
//                default:
//                    normal.setEducation(8);
//            }
            normal.setEducation(jl.getEducation());
            normal.setSpecializedSubject(jl.getSpecializedSubject());
            normal.setGraduationTime(jl.getGraduationTime());
            normal.setWorkTime(jl.getWorkTime());
            normal.setProfessionalWorkTime(jl.getProfessionalWorkTime());
            normal.setProfessionalTitle(jl.getProfessionalTitle());
            normal.setAddress(jl.getAddress());
            if(normal.getAddressExam() == null) {
                normal.setAddressExam(jl.getCityName().substring(0,jl.getCityName().indexOf("省")+1));
            }
            if (jl.getCityName().equals("省直")){
                normal.setCityName("省直");
            }else {
                normal.setCityName(jl.getCityName().substring(jl.getCityName().indexOf("省")+1));
            }
            normal.setWork(jl.getWork());
//            normal.setCityName(jl.getCityName());
            normal.setJbCode(jl.getJbCode());
            normal.setType(jl.getType());
            normal.setProfessional(jl.getProfessional());
            normal.setArchivesNo(jl.getArchivesNo());
            normal.setExamSubject(jl.getExamSubject());
            normal.setKh_one(jl.getKh_one());
            normal.setKh_two(jl.getKh_two());
            normal.setKh_three(jl.getKh_three());
            normal.setKh_four(jl.getKh_four());
            normal.setKh_five(jl.getKh_five());
            normal.setKh_six(jl.getKh_six());
            normal.setKh_seven(jl.getKh_seven());
            normal.setKh_eight(jl.getKh_eight());
            normal.setKh_nine(jl.getKh_nine());
            normal.setNationality("0");//默认中国
            normal.setPoliticalOutlook(4);//默认群众
            normal.setIsAutomaticCheck(jl.getIsAutomaticCheck());
            //todo 考试批次  在返回后设置
//            normal.setInputType(9);
            normal.setIsoldexaminee(jl.getIsoldexaminee());
            return normal;
        }).collect(Collectors.toList())) );
        Optional.ofNullable(personInfoImportJZ).ifPresent(u ->personInfoImportList.addAll(u.stream().map(jz -> {
            PersonInfoImport normal = new PersonInfoImport();
//            jz.setType(jz.getProfessional());
            normal.setName(jz.getName());
            normal.setSex(jz.getSex());
            normal.setNation(StringUtils.equals("汉族",jz.getNation())?"1":"2");
            normal.setIdCard(jz.getIdCard());
            normal.setSchool(jz.getSchool());
            normal.setType(jz.getType());
//            switch (jz.getEducation()) {
//                case "博士毕业":
//                    normal.setEducation(0);
//                    break;
//                case "硕士毕业":
//                    normal.setEducation(1);
//                    break;
//                case "大学本科毕业":
//                    normal.setEducation(2);
//                    break;
//                case "大学专科毕业":
//                    normal.setEducation(3);
//                    break;
//                case "中专毕业":
//                    normal.setEducation(4);
//                    break;
//                case "高中毕业":
//                    normal.setEducation(5);
//                    break;
//                case "初中毕业":
//                    normal.setEducation(6);
//                    break;
//                case "职高毕业":
//                    normal.setEducation(7);
//                    break;
//                default:
//                    normal.setEducation(8);
//            }
            normal.setSpecializedSubject(jz.getSpecializedSubject());
            normal.setGraduationTime(jz.getGraduationTime());
            normal.setWorkTime(jz.getWorkTime());
            normal.setProfessionalWorkTime(jz.getProfessionalWorkTime());
            normal.setProfessionalTitle(jz.getProfessionalTitle());
            normal.setAddress(jz.getAddress());
            if (jz.getCityName().equals("省直")){
                normal.setCityName("省直");
            }else {
                normal.setCityName(jz.getCityName().substring(jz.getCityName().indexOf("省")+1));
            }
            if(normal.getAddressExam() == null) {
                normal.setAddressExam(jz.getCityName().substring(0,jz.getCityName().indexOf("省")+1));
            }
            normal.setWork(jz.getWork());
//            normal.setCityName(jz.getCityName());
            normal.setJbCode(jz.getJbCode());
            normal.setType(jz.getType());//模板特殊处理
            normal.setProfessional(jz.getProfessional());
            normal.setArchivesNo(jz.getArchivesNo());
            normal.setExamSubject(jz.getExamSubject());
            normal.setKh_one(jz.getKh_one());
            normal.setKh_two(jz.getKh_two());
            normal.setKh_three(jz.getKh_three());
            normal.setKh_four(jz.getKh_four());
            normal.setKh_five(jz.getKh_five());
            normal.setKh_six(jz.getKh_six());
            normal.setKh_seven(jz.getKh_seven());
            normal.setKh_eight(jz.getKh_eight());
            normal.setKh_nine(jz.getKh_nine());
            normal.setNationality("0");//默认中国
            normal.setPoliticalOutlook(4);//默认群众
            normal.setIsoldexaminee(jz.getIsoldexaminee());
            return normal;
        }).collect(Collectors.toList())) );
        return personInfoImportList;

    }
    @Override
    public AjaxResult updatePhoneNum(PersonInfo personInfo) {
        PersonInfo personInfo1=new PersonInfo();
        personInfo1.setIdCard(personInfo.getIdCard());
        List<PersonInfo> list=personInfoMapper.selectPersonInfoList(personInfo1);
        list.forEach(s->{
            s.setPhoneNumber(personInfo.getPhoneNumber());
            personInfoMapper.updatePersonInfo(s);
        });

        SysUser sysUser=sysUserService.selectUserByUserName(personInfo.getIdCard());
        sysUser.setPhonenumber(personInfo.getPhoneNumber());
        sysUserService.updateUserNum(sysUser);
        return AjaxResult.success();
    }

}
