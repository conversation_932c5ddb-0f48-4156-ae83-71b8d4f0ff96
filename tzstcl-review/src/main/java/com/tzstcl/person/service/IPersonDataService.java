package com.tzstcl.person.service;

import java.util.List;

import com.alibaba.fastjson2.JSONArray;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.person.domain.PersonData;

/**
 * 考生资料Service接口
 *
 * <AUTHOR>
 * @date 2022-11-22
 */
public interface IPersonDataService
{
    /**
     * 查询考生资料
     *
     * @param id 考生资料主键
     * @return 考生资料
     */
    public PersonData selectPersonDataById(String id);

    /**
     * 查询考生资料列表
     *
     * @param personData 考生资料
     * @return 考生资料集合
     */
    public List<PersonData> selectPersonDataList(PersonData personData);

    /**
     * 新增考生资料
     *
     * @param personData 考生资料
     * @return 结果
     */
    public int insertPersonData(PersonData personData);

    /**
     * 修改考生资料
     *
     * @param personData 考生资料
     * @return 结果
     */
    public int updatePersonData(PersonData personData);

    /**
     * 批量删除考生资料
     *
     * @param ids 需要删除的考生资料主键集合
     * @return 结果
     */
    public int deletePersonDataByIds(String[] ids);

    /**
     * 删除考生资料信息
     *
     * @param id 考生资料主键
     * @return 结果
     */
    public int deletePersonDataById(String id);

    /**
     * 页面新增资料接口
     * @param array
     * @return
     */
    AjaxResult add(JSONArray array,Integer candidateType);

    /**
     * 根据身份证号获取考生资料
     * @param IdCard
     * @return
     */
    List<PersonData> selectPersonDataByIdCard(String IdCard);

    /**
     * 判断是新增还是更改
     * @param personData
     * @return
     */
    int update(PersonData personData);

    AjaxResult addAudit(JSONArray array, Integer candidateType);

    /**
     * 修改考生审核材料
     * @param array
     * @return
     */
     AjaxResult updateAuditData(JSONArray array,String idCard,Integer candidateType);

    /**
     * 根据文件删除
     * @param url
     * @return
     */
    int deletePersonDataByPath(String url);

    PersonData selectPersonDataByType(PersonData personData);
}
