package com.tzstcl.person.service;

import java.io.IOException;
import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.core.domain.entity.SysDept;
import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.common.core.domain.model.ResetPwdBody;
import com.tzstcl.department.controller.MyException;
import com.tzstcl.person.domain.OldWorkImport;
import com.tzstcl.person.domain.PersonInfo;
import com.tzstcl.person.domain.PersonInfoExport;
import com.tzstcl.person.domain.PersonInfoImport;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.Exceptions;

/**
 * 考生信息Service接口
 *
 * <AUTHOR>
 * @date 2022-11-17
 */
public interface IPersonInfoService
{
    /**
     * 查询考生信息
     *
     * @param id 考生信息主键
     * @return 考生信息
     */
    public PersonInfo selectPersonInfoById(String id);

    /**
     * 查询考生信息列表
     *
     * @param personInfo 考生信息
     * @return 考生信息集合
     */
    public List<PersonInfo> selectPersonInfoList(PersonInfo personInfo);

    /**
     * 新增考生信息
     *
     * @param personInfo 考生信息
     * @return 结果
     */
    public int insertPersonInfo(PersonInfoImport personInfo);

    /**
     * 修改考生信息
     *
     * @param personInfo 考生信息
     * @return 结果
     */
    public int updatePersonInfo(PersonInfo personInfo);

    /**
     * 批量删除考生信息
     *
     * @param ids 需要删除的考生信息主键集合
     * @return 结果
     */
    public int deletePersonInfoByIds(String[] ids);

    /**
     * 删除考生信息信息
     *
     * @param id 考生信息主键
     * @return 结果
     */
    public int deletePersonInfoById(String id);

    /**
     * 根据身份证号获取考生信息
     * @param  IdCard 身份证号
     */
    PersonInfo getInfoByIdCard(String IdCard,Integer candidateType);
    PersonInfo getInfoByArchives_no(String archives_no);

    /**
     * 获取首页数据反馈
     */
    JSONObject getData();

    /**
     * 根据部门查看考生
     * @param sysDept
     * @return
     */
    List<PersonInfo> selectByDept(SysDept sysDept);
    /**
     * 根据部门及条件查看考生
     * @param personInfo
     * @return
     */
    List<PersonInfo> getByDept(PersonInfo personInfo);

    /**
     * 考生信息导入
     * @param file
     * @return
     */
    String importData(Integer candidateType,MultipartFile file) throws Exception;

    /**
     * 根据Id获取考生信息
     * @param  Id
     */
    PersonInfo getInfoById(String Id);


    /**
     *检查注册根据身份证号
     * @param idCard
     * @return
     */
    PersonInfo checkRegister(String idCard);


    /**
     *检查重置密码必须项
     * @param resetPwdBody
     * @return
     */
    SysUser checkResetInfo(ResetPwdBody resetPwdBody);


    List<PersonInfo> getPersonInfoByIdCard(String idCard);

    JSONObject getDataIndex(Integer candidateType);

    PersonInfo getInfoByIdCardAndcandidateType(String idCard, Integer candidateType);

    List<PersonInfo> getInfoByIdCard1(String idCard);

    PersonInfo getInfoById1(String id);

    List<PersonInfo> selectPersonInfoLists(PersonInfo personInfo) throws MyException;

    List<PersonInfo> selectPersonInfoaudirStatusIsNotNull(PersonInfo personInfo);

    List<PersonInfo> selectPersonInfoAuditStatusIsNull(PersonInfo personInfo);

    Integer selectPersonInfoListsCount(PersonInfo personInfo);

    List<PersonInfo> selectPersonInfoListsIndex(PersonInfo personInfo);

    JSONObject generateAuditStatusIndex(PersonInfo personInfo);


    List<PersonInfoExport> selectPersonInfoListExport(PersonInfo personInfo);

    List<String> getPersonInfoByType();

    List<PersonInfo> selectCorpAuditPersonList(PersonInfo personInfo);

    /**
     * 推送二级造价师数据---查询最新一批二级造价师审核通过人员信息
     * @return
     */
    List<PersonInfo> selectPersonInfoListByEjzjsPush();

    int updateOldWorkByIdCard(OldWorkImport oldWorkImport);

    AjaxResult updatePhoneNum(PersonInfo personInfo);
}
