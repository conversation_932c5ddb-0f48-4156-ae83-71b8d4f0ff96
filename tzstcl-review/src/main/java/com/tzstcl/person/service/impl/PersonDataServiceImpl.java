package com.tzstcl.person.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.alibaba.fastjson2.JSONArray;
import com.tzstcl.audit.domain.Audit;
import com.tzstcl.audit.service.IAuditService;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.common.enums.AuditStatusEnum;
import com.tzstcl.common.utils.DateUtils;
import com.tzstcl.common.utils.SecurityUtils;
import com.tzstcl.common.utils.SnowflakeIdWorker;
import com.tzstcl.person.domain.PersonInfo;
import com.tzstcl.person.mapper.PersonInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzstcl.person.mapper.PersonDataMapper;
import com.tzstcl.person.domain.PersonData;
import com.tzstcl.person.service.IPersonDataService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * 考生资料Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-22
 */
@Service
public class PersonDataServiceImpl implements IPersonDataService
{
    @Resource
    private PersonDataMapper personDataMapper;

    @Resource
    private IAuditService auditService;

    @Resource
    private PersonInfoMapper personInfoMapper;

    /**
     * 查询考生资料
     *
     * @param id 考生资料主键
     * @return 考生资料
     */
    @Override
    public PersonData selectPersonDataById(String id)
    {
        return personDataMapper.selectPersonDataById(id);
    }

    /**
     * 查询考生资料列表
     *
     * @param personData 考生资料
     * @return 考生资料
     */
    @Override
    public List<PersonData> selectPersonDataList(PersonData personData)
    {
        return personDataMapper.selectPersonDataList(personData);
    }

    /**
     * 新增考生资料
     *
     * @param personData 考生资料
     * @return 结果
     */
    @Override
    public int insertPersonData(PersonData personData)
    {
        personData.setId(String.valueOf(SnowflakeIdWorker.getInstance().nextId()));
        personData.setCreateBy(SecurityUtils.getUserId().toString());
        personData.setCreateTime(DateUtils.getNowDate());
        personData.setUpdateTime(DateUtils.getNowDate());
        personData.setUpdateBy(SecurityUtils.getUserId().toString());
        return personDataMapper.insertPersonData(personData);
    }

    /**
     * 修改考生资料
     *
     * @param personData 考生资料
     * @return 结果
     */
    @Override
    public int updatePersonData(PersonData personData)
    {
        personData.setUpdateTime(DateUtils.getNowDate());
        personData.setUpdateBy(SecurityUtils.getUserId().toString());
        return personDataMapper.updatePersonData(personData);
    }

    /**
     * 批量删除考生资料
     *
     * @param ids 需要删除的考生资料主键
     * @return 结果
     */
    @Override
    public int deletePersonDataByIds(String[] ids)
    {
        return personDataMapper.deletePersonDataByIds(ids);
    }

    /**
     * 删除考生资料信息
     *
     * @param id 考生资料主键
     * @return 结果
     */
    @Override
    public int deletePersonDataById(String id)
    {
        return personDataMapper.deletePersonDataById(id);
    }

    /**
     * 页面新增资料接口
     * @param array
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add(JSONArray array,Integer candidateType) {
        //判断是否已存在，存在就覆盖
        List<PersonData> personDataList = personDataMapper.getByIdCard(SecurityUtils.getUsername());
        if (!CollectionUtils.isEmpty(personDataList)){
            personDataList.forEach(data-> {

                if (data.getCandidateType()==null || candidateType.equals(data.getCandidateType())){
                    deletePersonDataById(data.getId());
                }
            });
            for (int i = 0; i < array.size(); i++) {
                PersonData data = new PersonData();
                data.setIdCard(SecurityUtils.getUsername());
                data.setType(String.valueOf(i));
                data.setPath(array.getString(i));
                data.setCandidateType(candidateType);
                insertPersonData(data);
            }
        }

        Audit audit = new Audit();
        audit.setCandidateType(candidateType);
        if(auditService.insertAudit(audit)==500){
            return AjaxResult.success("考生的报考地市不在河南省中");
        }

        // 修改审核状态
        PersonInfo personInfo = personInfoMapper.getInfoByIdCardAndcandidateType(SecurityUtils.getUsername(),candidateType);
        if (Objects.nonNull(personInfo)) {
            personInfo.setAuditStatus(AuditStatusEnum.NO_AUDIT.getCode());
            personInfo.setUpdateTime(new Date());
            personInfo.setUpdateBy(SecurityUtils.getUsername());
            personInfoMapper.updatePersonInfo(personInfo);
        }
        return AjaxResult.success("提交成功");
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addAudit(JSONArray array, Integer candidateType) {
        //判断附件是否已存在，存在就覆盖
        List<PersonData> personDataList = personDataMapper.getByIdCard(SecurityUtils.getUsername());
        if (!CollectionUtils.isEmpty(personDataList)){
            personDataList.forEach(data-> {
                if (data.getCandidateType()==null || candidateType.equals(data.getCandidateType())){
                    deletePersonDataById(data.getId());
                }
            });
        }
            for(int i=0;i<array.size();i++){
                PersonData data = new PersonData();
                data.setIdCard(SecurityUtils.getUsername());
                data.setType(String.valueOf(i));
                data.setPath(array.getString(i));
                data.setCandidateType(candidateType);
                insertPersonData(data);
            }


        Audit audit = new Audit();
        audit.setCandidateType(candidateType);
        int i = auditService.personAudit(audit);
        if (i>0){
            return AjaxResult.success("提交成功");
        }else {
            return AjaxResult.error("提交失败");
        }
    }

    @Override
    public AjaxResult updateAuditData(JSONArray array,String idCard,Integer candidateType){
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if ("03".equals(user.getUserType()) && idCard == null){
            idCard = SecurityUtils.getUsername();
        }
        //判断附件是否已存在，存在就覆盖
        List<PersonData> personDataList = personDataMapper.getByIdCard(idCard);
        if (!CollectionUtils.isEmpty(personDataList)){
            personDataList.forEach(data->{
                if ( data.getCandidateType()==null || candidateType.equals(data.getCandidateType())){
                    deletePersonDataById(data.getId());
                }

                    }

            );
            for(int i=0;i<array.size();i++){
                PersonData data = new PersonData();
                data.setIdCard(idCard);
                data.setType(String.valueOf(i));
                data.setPath(array.getString(i));
                data.setCandidateType(candidateType);
                insertPersonData(data);
            }

        }
        return AjaxResult.success();
    }

    @Override
    public int deletePersonDataByPath(String url) {

       PersonData personData = personDataMapper.selectPersonDataByPath(url);
       if (personData!= null){
           personData.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));
           personData.setUpdateTime(DateUtils.getNowDate());
           personData.setDelFlag(1);
         return  personDataMapper.updatePersonData(personData);
       }else {
           return 0;
       }
    }

    @Override
    public PersonData selectPersonDataByType(PersonData personData) {
        return personDataMapper.selectPersonDataByType(personData);
    }

    /**
     * 根据身份证号获取考生资料
     * @param IdCard
     * @return
     */
    @Override
    public List<PersonData> selectPersonDataByIdCard(String IdCard) {
        return  personDataMapper.getByIdCard(IdCard);
    }

    /**
     * 页面修改考生资料
     * @param personData
     * @return
     */
    @Override
    public int update(PersonData personData) {
       List<PersonData> list = selectPersonDataList(personData);
       if(list.size()==0){//新增
           return insertPersonData(personData);
       }else {
           return updatePersonData(personData);
       }
    }



}
