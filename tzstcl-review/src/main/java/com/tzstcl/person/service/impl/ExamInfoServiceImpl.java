package com.tzstcl.person.service.impl;

import java.util.List;
import com.tzstcl.common.utils.DateUtils;
import com.tzstcl.common.utils.SecurityUtils;
import com.tzstcl.common.utils.SnowflakeIdWorker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzstcl.person.mapper.ExamInfoMapper;
import com.tzstcl.person.domain.ExamInfo;
import com.tzstcl.person.service.IExamInfoService;

/**
 * 考生报考信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-11-22
 */
@Service
public class ExamInfoServiceImpl implements IExamInfoService 
{
    @Autowired
    private ExamInfoMapper examInfoMapper;

    /**
     * 查询考生报考信息
     * 
     * @param id 考生报考信息主键
     * @return 考生报考信息
     */
    @Override
    public ExamInfo selectExamInfoById(String id)
    {
        return examInfoMapper.selectExamInfoById(id);
    }

    /**
     * 查询考生报考信息列表
     * 
     * @param examInfo 考生报考信息
     * @return 考生报考信息
     */
    @Override
    public List<ExamInfo> selectExamInfoList(ExamInfo examInfo)
    {
        return examInfoMapper.selectExamInfoList(examInfo);
    }

    /**
     * 新增考生报考信息
     * 
     * @param examInfo 考生报考信息
     * @return 结果
     */
    @Override
    public int insertExamInfo(ExamInfo examInfo)
    {
        examInfo.setId(String.valueOf(SnowflakeIdWorker.getInstance().nextId()));
        examInfo.setCreateBy(SecurityUtils.getUserId().toString());
        examInfo.setCreateTime(DateUtils.getNowDate());
        examInfo.setUpdateTime(DateUtils.getNowDate());
        examInfo.setUpdateBy(SecurityUtils.getUserId().toString());
        return examInfoMapper.insertExamInfo(examInfo);
    }

    /**
     * 修改考生报考信息
     * 
     * @param examInfo 考生报考信息
     * @return 结果
     */
    @Override
    public int updateExamInfo(ExamInfo examInfo)
    {
        examInfo.setUpdateTime(DateUtils.getNowDate());
        return examInfoMapper.updateExamInfo(examInfo);
    }

    /**
     * 批量删除考生报考信息
     * 
     * @param ids 需要删除的考生报考信息主键
     * @return 结果
     */
    @Override
    public int deleteExamInfoByIds(String[] ids)
    {
        return examInfoMapper.deleteExamInfoByIds(ids);
    }

    /**
     * 删除考生报考信息信息
     * 
     * @param id 考生报考信息主键
     * @return 结果
     */
    @Override
    public int deleteExamInfoById(String id)
    {
        return examInfoMapper.deleteExamInfoById(id);
    }
}
