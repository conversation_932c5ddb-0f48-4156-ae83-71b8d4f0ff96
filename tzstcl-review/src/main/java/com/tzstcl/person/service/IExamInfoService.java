package com.tzstcl.person.service;

import java.util.List;
import com.tzstcl.person.domain.ExamInfo;

/**
 * 考生报考信息Service接口
 * 
 * <AUTHOR>
 * @date 2022-11-22
 */
public interface IExamInfoService 
{
    /**
     * 查询考生报考信息
     * 
     * @param id 考生报考信息主键
     * @return 考生报考信息
     */
    public ExamInfo selectExamInfoById(String id);

    /**
     * 查询考生报考信息列表
     * 
     * @param examInfo 考生报考信息
     * @return 考生报考信息集合
     */
    public List<ExamInfo> selectExamInfoList(ExamInfo examInfo);

    /**
     * 新增考生报考信息
     * 
     * @param examInfo 考生报考信息
     * @return 结果
     */
    public int insertExamInfo(ExamInfo examInfo);

    /**
     * 修改考生报考信息
     * 
     * @param examInfo 考生报考信息
     * @return 结果
     */
    public int updateExamInfo(ExamInfo examInfo);

    /**
     * 批量删除考生报考信息
     * 
     * @param ids 需要删除的考生报考信息主键集合
     * @return 结果
     */
    public int deleteExamInfoByIds(String[] ids);

    /**
     * 删除考生报考信息信息
     * 
     * @param id 考生报考信息主键
     * @return 结果
     */
    public int deleteExamInfoById(String id);
}
