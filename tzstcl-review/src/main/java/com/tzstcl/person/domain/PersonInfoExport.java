package com.tzstcl.person.domain;

import com.tzstcl.common.annotation.Excel;
import com.tzstcl.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 公司：天筑科技股份有限公司
 * 作者：wzh
 * 日期：XX年XX月XX日
 * 说明：
 */

public class PersonInfoExport extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 报考省市
     */
    @Excel(name = "省市")
    private String cityName;
    /**
     * 姓名
     */
    @Excel(name = "姓名")
    @NotBlank(message = "姓名不可为空")
    private String name;
    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    @NotBlank(message = "身份证号不可为空")
    @Size(min = 18, max = 18, message = "请输入正确的身份证号")
    private String idCard;
    /**
     * 工作单位
     */
    @Excel(name = "所在企业")
    @NotBlank(message = "工作单位不可为空")
    private String work;

    /**
     * 报考资质类别
     */
    @Excel(name = "报考资质类别", dictType = "candidate_type")
    private Integer candidateType;

    /**
     * 审核状态
     */
    @Excel(name = "审核状态", dictType = "audit_status")
    private Integer auditStatus;
    /**
     * 提交次数
     */
//    @Excel(name = "提交次数")
    private String number;
    /**
     * 审核时间  人员最后修改时间
     */
    @Excel(name = "审核时间")
    private String auditTime;
    /**
     * 审核人
     */
    @Excel(name = "审核人")
    private String auditPerson;

    @Excel(name = "报考专业")
    private String professional;

    @Excel(name = "报考类型")
    private String type;


    /**
     * 考试科目
     */
//    @Excel(name = "考试科目")
    private String exam;

    //    @Excel(name = "所学专业")
    private String specializedSubject;
    /**
     * 提交次数
     */
//    @Excel(name = "提交次数")
    private String applications;
    /**
     * 审核状态
     */
//    @Excel(name = "审核状态")
    private String status;

    /**
     * 审核结果
     */
//    @Excel(name = "公司审核结果")
    private String audit_result_cp;

    /**
     * 审核意见
     */
//    @Excel(name = "公司审核意见")
    private String audit_opinion_cp;

    /**
     * 审核结果
     */
//    @Excel(name = "主管审核结果")
    private String audit_result_zg;

    /**
     * 审核意见
     */
//    @Excel(name = "主管审核意见")
    private String audit_opinion_zg;

    public String getProfessional() {
        return professional;
    }

    public void setProfessional(String professional) {
        this.professional = professional;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getCandidateType() {
        return candidateType;
    }

    public void setCandidateType(Integer candidateType) {
        this.candidateType = candidateType;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(String auditTime) {
        this.auditTime = auditTime;
    }

    public String getAuditPerson() {
        return auditPerson;
    }

    public void setAuditPerson(String auditPerson) {
        this.auditPerson = auditPerson;
    }

    public String getSpecializedSubject() {
        return specializedSubject;
    }

    public void setSpecializedSubject(String specializedSubject) {
        this.specializedSubject = specializedSubject;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getWork() {
        return work;
    }

    public void setWork(String work) {
        this.work = work;
    }

    public String getExam() {
        return exam;
    }

    public void setExam(String exam) {
        this.exam = exam;
    }

    public String getApplications() {
        return applications;
    }

    public void setApplications(String applications) {
        this.applications = applications;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAudit_result_cp() {
        return audit_result_cp;
    }

    public void setAudit_result_cp(String audit_result_cp) {
        this.audit_result_cp = audit_result_cp;
    }

    public String getAudit_opinion_cp() {
        return audit_opinion_cp;
    }

    public void setAudit_opinion_cp(String audit_opinion_cp) {
        this.audit_opinion_cp = audit_opinion_cp;
    }

    public String getAudit_result_zg() {
        return audit_result_zg;
    }

    public void setAudit_result_zg(String audit_result_zg) {
        this.audit_result_zg = audit_result_zg;
    }

    public String getAudit_opinion_zg() {
        return audit_opinion_zg;
    }

    public void setAudit_opinion_zg(String audit_opinion_zg) {
        this.audit_opinion_zg = audit_opinion_zg;
    }

    @Override
    public String toString() {
        return "PersonInfoExport{" +
                "cityName='" + cityName + '\'' +
                ", name='" + name + '\'' +
                ", idCard='" + idCard + '\'' +
                ", work='" + work + '\'' +
                ", exam='" + exam + '\'' +
                ", applications='" + applications + '\'' +
                ", status='" + status + '\'' +
                ", audit_result_cp='" + audit_result_cp + '\'' +
                ", audit_opinion_cp='" + audit_opinion_cp + '\'' +
                ", audit_result_zg='" + audit_result_zg + '\'' +
                ", audit_opinion_zg='" + audit_opinion_zg + '\'' +
                '}';
    }
}
