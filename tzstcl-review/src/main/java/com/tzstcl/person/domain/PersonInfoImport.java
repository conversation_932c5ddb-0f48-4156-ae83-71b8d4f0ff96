package com.tzstcl.person.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tzstcl.common.annotation.Excel;
import com.tzstcl.common.core.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 公司：天筑科技股份有限公司
 * 作者：wzh
 * 日期：XX年XX月XX日
 * 说明：
 */
@Data
public class PersonInfoImport extends BaseEntity {
    private String id;

    @Excel(name = "ksxm")
    @NotBlank(message = "姓名不可为空")
    private String name;

    /**
     * 性别（1男 2女）
     */
    @Excel(name = "xb")
    @NotNull(message = "性别不可为空")
    private Integer sex;

    /**
     * 国籍
     */
    @Excel(name = "gj")
    @NotBlank(message = "国籍不可为空")
    private String nationality;

    /**
     * 民族(1汉族 2少数民族 3其他)
     */
    @Excel(name = "mz")
    @NotBlank(message = "民族不可为空")
    private String nation;

    /**
     * 身份证号
     */
    @Excel(name = "zjhm")
    @NotBlank(message = "身份证号不可为空")
    @Size(min = 18, max = 18, message = "请输入正确的身份证号")
    private String idCard;

    /**
     * 政治面貌（1党员 2团员 3民主党派 4群众）
     */

    private Integer politicalOutlook;

    /**
     * 政治面貌（1党员 2团员 3民主党派 4群众）
     */
    @Excel(name = "zzmm")
    @NotNull(message = "政治面貌不可为空")
    private String zzmm;

    /**
     * 手机号
     */
    @Excel(name = "phone")
    @NotBlank(message = "手机号不可为空")
    @Size(min = 11,max = 11,message = "请输入正确的手机号")
    private String phoneNumber;

    /**
     * 毕业院校
     */
    @Excel(name = "xlbyxx")
    @NotBlank(message = "毕业院校不可为空")
    private String school;

    /**
     * 学历（0博士 1硕研以上 2大学本科 3大学专科 4中专 5高中 6初中 7职高 8其他）
     */

    @NotNull(message = "学历不可为空")
    private Integer education;

    @Excel(name = "xl")
    private String xl;

    /**
     * 所学专业 学历所学专业
     */
    @Excel(name = "xlsxzy")
    @NotBlank(message = "所学专业不可为空")
    private String specializedSubject;

    /**
     * 毕业时间
     */
    @Excel(name = "xlbyby", width = 30)
    @NotNull(message = "毕业时间不可为空")
    private String graduationTime;

    /**
     * 参加工作年限
     */
    @Excel(name = "gznx")
    private String workTime;

    /**
     * 专业工作年限
     */
    @Excel(name = "zynx")
    @NotBlank(message = "专业工作年限")
    private String professionalWorkTime;

    /**
     * 学位
     */
    @Excel(name = "xw")
    private String academicDegree;
    /**
     * 学位证书编号
     */
    @Excel(name = "xwzsbh")
    private String academicDegreeNo;
    /**
     * 专业职称
     */
    @Excel(name = "zc")
    private String professionalTitle;

    /**
     * 专业职务
     */
    @Excel(name = "zw")
    private String professionalPosition;

    /**
     * 专业职务聘任时间
     */
    @Excel(name = "zysj")
    private String professionalPositionTime;

    /**
     * 专业职称聘任时间
     */
    @Excel(name = "zcsj")
    private String professionalTitleTime;

    /**
     * 通讯地址
     */
    @Excel(name = "txdz")
    private String address;

    /**
     * 工作单位
     */
//    @Excel(name = "gzdw")
//    @NotBlank(message = "工作单位不可为空")
    private String work;

    /**
     * 工单位的社会信用代码
     */
    private String corpCode;

    /**
     * 单位性质
     */
    @Excel(name = "dwxz")
    private String workNature;

    /**
     * 照片
     */
//    @Excel(name = "照片")
    private String photo;

    /**
     * 报考省市
     */
    @Excel(name = "ss_code")
    private String addressExam;

    /**
     * 考区代码
     */
    @Excel(name = "kq_code")
    private String addressCode;

    /**
     * 报考地市 地市名称
     */
    @Excel(name = "bmds_mc")
    @NotBlank(message = "报考地市不可为空")
    private String cityName;

    /**
     * bm_serial
     */
//    @Excel(name = "bm_serial")
    private String bmSerial;

    /**
     * jb_code
     */
    @Excel(name = "bkjb")
    private String jbCode;

    /**
     * 报考类型编码
     */
//    @Excel(name = "报考类型编码")
    private String typeCode;

    /**
     * 报考类型 级别名称 报考级别
     */
    @Excel(name = "bkjb_mc")
    @NotBlank(message = "报考类型不可为空")
    private String type;

    /**
     * 专业名称 报考专业
     */
    @Excel(name = "bkzy_mc")
    private String professional;

    /**
     * 档案号
     */
    @Excel(name = "dah")
    @NotBlank(message = "档案号不可为空")
    private String archivesNo;

    /**
     * 报考专业
     */
    @Excel(name = "bkzy")
    @NotBlank(message = "报考专业不可为空")
    private String examSubject;

    /**
     * 删除标记(0未删除 1已删除)
     */
    private Integer delFlag;

    /**
     * 是否合格
     */
    @Excel(name = "qualified")
    private String qualified;

    /**
     * 是否完善信息(0否 1是)
     */
//    @Excel(name = "是否完善信息(0否 1是)")
    private Integer flag;

    private Integer hand;

    /**
     * sflks 是否老考生
     */
    @Excel(name = "sflks")
    private String isoldexaminee;


    /**
     * sfwczdhc   是否完成自动核查
     */
    @Excel(name = "sfwczdhc")
    private String isAutomaticCheck;

    /**
     * 审核状态
     **/
    private Integer auditStatus;

    /**
     * 报考类型
     */
    @Excel(name = "candidateType")
    private String candidateType1;
    private Integer candidateType;
    @Excel(name = "inputType")
    private Integer inputType;

    /**
     * 考试科目一
     */
    @Excel(name = "km_1")
    private String km_one;
    /**
     * 考试科目二
     */
    @Excel(name = "km_2")
    private String km_two;
    /**
     * 考试科目二
     */
    @Excel(name = "km_3")
    private String km_three;
    /**
     * 考号一
     */
    @Excel(name = "kh_1")
    private String kh_one;
    /**
     * 考号二
     */
    @Excel(name = "kh_2")
    private String kh_two;
    /**
     * 考号三
     */
    @Excel(name = "kh_3")
    private String kh_three;

    /**
     * 考号四
     */
    private String kh_four;
    /**
     * 考号五
     */
    private String kh_five;
    /**
     * 考号六
     */
    private String kh_six;
    /**
     * 考号七
     */
    private String kh_seven;
    /**
     * 考号八
     */
    private String kh_eight;
    /**
     * 考号九
     */
    private String kh_nine;

    /**
     * 审核人
     */
    private String auditPerson;
    /**
     * 审核结果
     */
    private String auditResult;
    @Excel(name = "oldWork")
    private String oldWork;
    @Excel(name = "remark")
    private String remark;


}
