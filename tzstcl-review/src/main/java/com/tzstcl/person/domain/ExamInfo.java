package com.tzstcl.person.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzstcl.common.annotation.Excel;
import com.tzstcl.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 考生报考信息对象 exam_info
 * 
 * <AUTHOR>
 * @date 2022-11-22
 */
public class ExamInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private String id;

    /** 考生身份证号 */
    @Excel(name = "考生身份证号")
    @NotNull(message = "身份证号不可为空")
    private String idCard;

    /** 考试科目 */
    @Excel(name = "考试科目")
    @NotBlank(message = "考试科目不可为空")
    private String examSubject;

    /** 考号 */
    @Excel(name = "考号")
    @NotBlank(message = "考号不可为空")
    private String examCode;

    /** 删除标记(0-未删除，1-已删除) */
    private Integer delFlag;

    private Integer candidateType;

    public Integer getCandidateType() {
        return candidateType;
    }

    public void setCandidateType(Integer candidateType) {
        this.candidateType = candidateType;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }
    public void setExamSubject(String examSubject) 
    {
        this.examSubject = examSubject;
    }

    public String getExamSubject() 
    {
        return examSubject;
    }
    public void setExamCode(String examCode) 
    {
        this.examCode = examCode;
    }

    public String getExamCode() 
    {
        return examCode;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("idCard", getIdCard())
            .append("examSubject", getExamSubject())
            .append("examCode", getExamCode())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
