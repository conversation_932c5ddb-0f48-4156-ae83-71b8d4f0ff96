package com.tzstcl.person.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzstcl.common.annotation.Excel;
import com.tzstcl.common.core.domain.BaseEntity;

/**
 * 考生资料对象 person_data
 *
 * <AUTHOR>
 * @date 2022-11-22
 */
public class PersonData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private String id;

    /** 考生身份证号 */
    @Excel(name = "考生身份证号")
    private String idCard;

    /** 资料类型（0-身份证正面,1-身份证反面,2-毕业证书,3-学历认证报告,4-工作证明（单位盖章）） */
    @Excel(name = "资料类型", readConverterExp = "0=-身份证正面,1=身份证反面,2=毕业证书,3=学历认证报告,4=工作证明（单位盖章")
    private String type;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String path;

    private Integer candidateType;

    /** 删除标记(0-未删除，1-已删除) */
    private Integer delFlag;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }
    public void setIdCard(String idCard)
    {
        this.idCard = idCard;
    }

    public String getIdCard()
    {
        return idCard;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setPath(String path)
    {
        this.path = path;
    }

    public String getPath()
    {
        return path;
    }
    public void setDelFlag(Integer delFlag)
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag()
    {
        return delFlag;
    }

    public Integer getCandidateType() {
        return candidateType;
    }

    public void setCandidateType(Integer candidateType) {
        this.candidateType = candidateType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("idCard", getIdCard())
            .append("type", getType())
            .append("path", getPath())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
