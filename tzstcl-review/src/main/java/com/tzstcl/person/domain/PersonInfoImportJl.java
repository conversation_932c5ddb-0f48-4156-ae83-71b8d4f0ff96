package com.tzstcl.person.domain;

import com.tzstcl.common.annotation.Excel;
import com.tzstcl.common.core.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 公司：天筑科技股份有限公司
 * 作者：邱宇阳
 * 日期：XX年XX月XX日
 * 说明：监理excel导入接收类
 */
@Data
public class PersonInfoImportJl extends BaseEntity {

    private String id;

    @Excel(name = "ksxm")
    @NotBlank(message = "姓名不可为空")
    private String name;

    /** 性别（1男 2女） */
    @Excel(name = "xb")
    @NotBlank(message = "性别不可为空")
    private String sex;


    /** 民族(1汉族 2少数民族 3其他) */
    @Excel(name = "mz")
    @NotBlank(message = "民族不可为空")
    private String nation;

    /** 身份证号 */
    @Excel(name = "zjhm")
    @NotBlank(message = "身份证号不可为空")
    @Size(min = 18,max = 18,message = "请输入正确的身份证号")
    private String idCard;


    /** 毕业院校 */
    @Excel(name = "xlbyxx")
    @NotBlank(message = "毕业院校不可为空")
    private String school;

    /** 学历（0博士 1硕研以上 2大学本科 3大学专科 4中专 5高中 6初中 7职高 8其他） */
    @Excel(name = "xl")
    @NotNull(message = "学历不可为空")
    private String education;

    /** 所学专业 */
    @Excel(name = "xlsxzy")
    @NotBlank(message = "所学专业不可为空")
    private String specializedSubject;

    /** 毕业时间 */
    @Excel(name = "xlbyby", width = 30)
    @NotNull(message = "毕业时间不可为空")
    private String graduationTime;

    /** 参加工作年限 */
    @Excel(name = "gznx")
    private String workTime;

    /** 专业工作年限 */
    @Excel(name = "zynx")
    @NotBlank(message = "专业工作年限")
    private String professionalWorkTime;

    /** 专业职称 */
    @Excel(name = "zyzc")
    private String professionalTitle;


    /** 通讯地址 */
    @Excel(name = "txdz")
    private String address;

    /** 工作单位 */
//    @Excel(name = "gzdw")
//    @NotBlank(message = "工作单位不可为空")
    private String work;

    /** 报考地市 */
    @Excel(name = "bmds_mc")
    @NotBlank(message = "报考地市不可为空")
    private String cityName;

    /** bm_serial */
    @Excel(name = "bm_serial")
    private String bmSerial;

    /** jb_code */
    @Excel(name = "bkjb")
    private String jbCode;


    /** 报考类型 */
    @Excel(name = "bkjb_mc")
    @NotBlank(message = "报考类型不可为空")
    private String type;

    /** 专业名称 */
    @Excel(name = "bkzy_mc")
    private String professional;

    /** 档案号 */
    @Excel(name = "dah")
    @NotBlank(message = "档案号不可为空")
    private String archivesNo;

    /** 报考专业 */
    @Excel(name = "bkzy")
    @NotBlank(message = "报考专业不可为空")
    private String examSubject;

    /** sflks 是否老考生*/
    @Excel(name = "sflks")
    private String isoldexaminee;

    /** sfwczdhc   是否完成自动核查*/
    @Excel(name = "sfwczdhc")
    private String isAutomaticCheck;


    /**考号一*/
    @Excel(name = "zkzh_1")
    private String kh_one;
    /**考号二*/
    @Excel(name = "zkzh_2")
    private String kh_two;
    /**考号三*/
    @Excel(name = "zkzh_3")
    private String kh_three;
    /**考号四*/
    @Excel(name = "zkzh_4")
    private String kh_four;
    /**考号五*/
    @Excel(name = "zkzh_5")
    private String kh_five;
    /**考号六*/
    @Excel(name = "zkzh_6")
    private String kh_six;
    /**考号七*/
    @Excel(name = "zkzh_7")
    private String kh_seven;
    /**考号八*/
    @Excel(name = "zkzh_8")
    private String kh_eight;
    /**考号九*/
    @Excel(name = "zkzh_9")
    private String kh_nine;
}
