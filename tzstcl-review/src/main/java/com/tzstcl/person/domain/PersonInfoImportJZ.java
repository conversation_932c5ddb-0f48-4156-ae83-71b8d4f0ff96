package com.tzstcl.person.domain;

import com.tzstcl.common.annotation.Excel;
import com.tzstcl.common.core.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 公司：天筑科技股份有限公司
 * 作者：邱宇阳
 * 日期：XX年XX月XX日
 * 说明：监理excel导入接收类
 */
@Data
public class PersonInfoImportJZ extends BaseEntity {

    private String id;

    @Excel(name = "ksxm")
    @NotBlank(message = "姓名不可为空")
    private String name;

    /** 性别（1男 2女） */
    @Excel(name = "xb")
    @NotBlank(message = "性别不可为空")
    private String sex;


    /** 民族(1汉族 2少数民族 3其他) */
    @Excel(name = "mz")
    private String nation;

    /** 身份证号 */
    @Excel(name = "zjhm")
    @NotBlank(message = "身份证号不可为空")
    @Size(min = 18,max = 18,message = "请输入正确的身份证号")
    private String idCard;


    /** 毕业院校 */
    @Excel(name = "xlbyxx")
    @NotBlank(message = "毕业院校不可为空")
    private String school;

    /** 学历（0博士 1硕研以上 2大学本科 3大学专科 4中专 5高中 6初中 7职高 8其他） */
    @Excel(name = "xl")
    @NotNull(message = "学历不可为空")
    private String education;

    /**xlzsbh*/
    @Excel(name = "xlzsbh")
    private String educationCertificateNumber;


    /** 所学专业 */
    @Excel(name = "xlsxzy")
    @NotBlank(message = "所学专业不可为空")
    private String specializedSubject;

    /** 毕业时间 */
    @Excel(name = "xlbyby", width = 30)
    @NotNull(message = "毕业时间不可为空")
    private String graduationTime;

    /** 参加工作年限 */
    @Excel(name = "gznx")
    private String workTime;

    /** 专业工作年限 */
    @Excel(name = "zynx")
    @NotBlank(message = "专业工作年限")
    private String professionalWorkTime;

    /** 专业职称 */
    @Excel(name = "zyzc")
    private String professionalTitle;


    /** 通讯地址 */
    @Excel(name = "txdz")
    private String address;

    /** 工作单位 */
//    @Excel(name = "gzdw")
//    @NotBlank(message = "工作单位不可为空")
    private String work;

    /** 报考地市 */
    @Excel(name = "bmds_mc")
    @NotBlank(message = "报考地市不可为空")
    private String cityName;

    /** bm_serial */
    @Excel(name = "bm_serial")
    private String bmSerial;

    /** jb_code */
    @Excel(name = "bkjb")
    private String jbCode;



    /** 报考类型 */
    @Excel(name = "bkjb_mc")
    private String type;

    /** 专业名称 */
    @Excel(name = "bkzy_mc")
    private String professional;

    /** 档案号 */
    @Excel(name = "dah")
    @NotBlank(message = "档案号不可为空")
    private String archivesNo;

    /** 报考专业 */
    @Excel(name = "bkzy")
    @NotBlank(message = "报考专业不可为空")
    private String examSubject;

    /** sflks 是否老考生*/
    @Excel(name = "sflks")
    private String isoldexaminee;

    /**考号一*/
    @Excel(name = "zkzh_1")
    private String kh_one;
    /**考号二*/
    @Excel(name = "zkzh_2")
    private String kh_two;
    /**考号三*/
    @Excel(name = "zkzh_3")
    private String kh_three;
    /**考号四*/
    @Excel(name = "zkzh_4")
    private String kh_four;
    /**考号五*/
    @Excel(name = "zkzh_5")
    private String kh_five;
    /**考号六*/
    @Excel(name = "zkzh_6")
    private String kh_six;
    /**考号七*/
    @Excel(name = "zkzh_7")
    private String kh_seven;
    /**考号八*/
    @Excel(name = "zkzh_8")
    private String kh_eight;
    /**考号九*/
    @Excel(name = "zkzh_9")
    private String kh_nine;

    /**pyfs*/
    @Excel(name = "pyfs")
    private String pyfs;

    /**学位*/
    @Excel(name = "xw")
    private String xw;
    /**学位证书编号*/
    @Excel(name = "xwzsbh")
    private String xwzsbh;

    /**xxmc*/
    @Excel(name = "xxmc")
    private String xxmc;

    /**qdsj*/
    @Excel(name = "qdsj")
    private String qdsj;

    /**xwsxzy*/
    @Excel(name = "xwsxzy")
    private String xwsxzy;

    /**zyjszw*/
    @Excel(name = "zyjszw")
    private String zyjszw;

    /**报考级别名称*/
    private String bkjbmc;


    /**csrq*/
    @Excel(name = "csrq")
    private String csrq;


    /**报名地市*/
    @Excel(name = "bmds")
    private String bmds;



    /**政治面貌*/
    @Excel(name = "zzmm")
    private String zzmm;

    /**邮箱*/
    @Excel(name = "dzyx")
    private String dzyx;

    /**zytg*/
    @Excel(name = "zytg")
    private String zytg;

    /**zsglh*/
    @Excel(name = "zsglh")
    private String zsglh;


    /**ydks*/
    @Excel(name = "ydks")
    private String ydks;
    /**shtg*/
    @Excel(name = "shtg")
    private String shtg;

    /**shds*/
    @Excel(name = "shds")
    private String shds;

    /**zdwchc*/
    @Excel(name = "zdwchc")
    private String zdwchc;

    /**wbsfhcfs*/
    @Excel(name = "wbsfhcfs")
    private String wbsfhcfs;

    /**wbsfhczt*/
    @Excel(name = "wbsfhczt")
    private String wbsfhczt;

    /**wbxlhcfs*/
    @Excel(name = "wbxlhcfs")
    private String wbxlhcfs;

    /**wbxlhczt*/
    @Excel(name = "wbxlhczt")
    private String wbxlhczt;

    /**wbxwhcfs*/
    @Excel(name = "wbxwhcfs")
    private String wbxwhcfs;

    /**wbxwhczt*/
    @Excel(name = "wbxwhczt")
    private String wbxwhczt;

    /**km_1*/
    @Excel(name = "km_1")
    private String km_1;

    /**kmtg_1*/
    @Excel(name = "kmtg_1")
    private String kmtg_1;

    /**kdmc_1*/
    @Excel(name = "kdmc_1")
    private String kdmc_1;

    /**km_2*/
    @Excel(name = "km_2")
    private String km_2;

    /**kmtg_2*/
    @Excel(name = "kmtg_2")
    private String kmtg_2;

    /**kdmc_2*/
    @Excel(name = "kdmc_2")
    private String kdmc_2;

    /**km_3*/
    @Excel(name = "km_3")
    private String km_3;

    /**kmtg_3*/
    @Excel(name = "kmtg_3")
    private String kmtg_3;

    /**kdmc_3*/
    @Excel(name = "kdmc_3")
    private String kdmc_3;

    /**km_4*/
    @Excel(name = "km_4")
    private String km_4;

    /**kmtg_4*/
    @Excel(name = "kmtg_4")
    private String kmtg_4;

    /**kdmc_4*/
    @Excel(name = "kdmc_4")
    private String kdmc_4;

    /**kmmc_1*/
    @Excel(name = "kmmc_1")
    private String kmmc_1;

    /**kmmc_2*/
    @Excel(name = "kmmc_2")
    private String kmmc_2;

    /**kmmc_3*/
    @Excel(name = "kmmc_3")
    private String kmmc_3;

    /**kmmc_4*/
    @Excel(name = "kmmc_4")
    private String kmmc_4;

}
