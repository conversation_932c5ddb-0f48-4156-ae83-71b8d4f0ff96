package com.tzstcl.person.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tzstcl.audit.domain.Audit;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzstcl.common.annotation.Excel;
import com.tzstcl.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 考生信息对象 person_info
 *
 * <AUTHOR>
 * @date 2022-11-22
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class PersonInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private String id;

    /** 姓名 */
    @Excel(name = "姓名")
    @NotBlank(message = "姓名不可为空")
    private String name;

    /** 性别（1男 2女） */
    @Excel(name = "性别", readConverterExp = "1=男,2=女")
    @NotNull(message = "性别不可为空")
    private Integer sex;

    /** 国籍 */
    @Excel(name = "国籍")
    @NotBlank(message = "国籍不可为空")
    private String nationality;

    /** 民族(1汉族 2少数民族 3其他) */
    @Excel(name = "民族(1汉族 2少数民族 3其他)")
    @NotBlank(message = "民族不可为空")
    private String nation;

    /** 身份证号 */
    @Excel(name = "身份证号")
    @NotBlank(message = "身份证号不可为空")
    @Size(min = 18,max = 18,message = "请输入正确的身份证号")
    private String idCard;

    /**出生年月日*/
    private String birthday;

    /** 政治面貌（1党员 2团员 3民主党派 4群众） */
    @Excel(name = "政治面貌", readConverterExp = "1=党员,2=团员,3=民主党派,4=群众")
    @NotNull(message = "政治面貌不可为空")
    private Integer politicalOutlook;

    /** 手机号 */
    @Excel(name = "手机号")
    @NotBlank(message = "手机号不可为空")
    @Size(min = 11,max = 11,message = "请输入正确的手机号")
    private String phoneNumber;

    /** 毕业院校 */
    @Excel(name = "毕业院校")
    @NotBlank(message = "毕业院校不可为空")
    private String school;

    /** 学历（0博士 1硕研以上 2大学本科 3大学专科 4中专 5高中 6初中 7职高 8其他） */
    @Excel(name = "学历", readConverterExp = "0=博士,1=硕研以上,2=大学本科,3=大学专科,4=中专,5=高中,6=初中,7=职高,8=其他")
    @NotNull(message = "学历不可为空")
    private Integer education;

    /** 所学专业 */
    @Excel(name = "所学专业", readConverterExp = "1=与报考相同 ,2=与报考相近,3=与报考不同,4=其他")
    @NotBlank(message = "所学专业不可为空")
    private String specializedSubject;

    /** 毕业时间 */
    @Excel(name = "毕业时间")
    @NotNull(message = "毕业时间不可为空")
    private String graduationTime;

    /** 参加工作年限 */
    @Excel(name = "参加工作年限")
    private String workTime;

    private Integer workTimeMax;
    private Integer workTimeMin;

    /** 专业工作年限 */
    @Excel(name = "专业工作年限")
    @NotBlank(message = "专业工作年限")
    private String professionalWorkTime;

    /** 专业职称 */
    @Excel(name = "专业职称")
    private String professionalTitle;

    /** 专业职务 */
    @Excel(name = "专业职务")
    private String professionalPosition;

    /** 专业职称聘任时间 */
    @Excel(name = "专业职称聘任时间")
    private String professionalTitleTime;

    /** 专业职务聘任时间 */
    @Excel(name = "专业职务聘任时间")
    private String professionalPositionTime;

    /** 通讯地址 */
    @Excel(name = "通讯地址")
    @NotBlank(message = "通讯地址不可为空")
    private String address;

    /** 工作单位 */
    @Excel(name = "工作单位")
    @NotBlank(message = "工作单位不可为空")
    private String work;

    /**工单位的社会信用代码*/
    private String corpCode;

    /** 单位性质 */
    @Excel(name = "单位性质")
    private String workNature;

    /** 照片 */
    @Excel(name = "照片")
    private String photo;

    /** 报考省市 */
    @Excel(name = "报考省市")
    private String addressExam;

    /** 考区代码 */
    @Excel(name = "考区代码")
    private String addressCode;

    /** 报考地市 */
    @Excel(name = "报考地市")
    @NotBlank(message = "报考地市不可为空")
    private String cityName;

    /** bm_serial */
    @Excel(name = "bm_serial")
    private String bmSerial;

    /** jb_code */
    @Excel(name = "jb_code")
    private String jbCode;

    /** 报考类型编码 */
    @Excel(name = "报考类型编码")
    private String typeCode;

    /** 报考类型 */
    @Excel(name = "报考类型")
    @NotBlank(message = "报考类型不可为空")
    private String type;

    /** 专业名称 */
    @Excel(name = "专业名称")
    private String professional;

    /** 档案号 */
    @Excel(name = "档案号")
    @NotBlank(message = "档案号不可为空")
    private String archivesNo;

    /** 报考专业 */
    @Excel(name = "报考专业")
    @NotBlank(message = "报考专业不可为空")
    private String examSubject;

    /** 删除标记(0未删除 1已删除) */
    private Integer delFlag;

    /** 是否合格 */
    @Excel(name = "是否合格")
    private String qualified;

    /** 是否完善信息(0否 1是) */
    @Excel(name = "是否完善信息(0否 1是)")
    private Integer flag;

    /** 报考资质类型 */
    private Integer candidateType;

    /** 报考资质类型 */
    private List<Integer> candidateTypeList = new ArrayList<>();

    /**考试信息*/
    private List<ExamInfo> examInfoList;

    /**审核信息*/
    private List<Audit> auditList;

    /**考生资料*/
    private List<PersonData> personDataList;

    /**搜索条件 0全部,1未审核,2已审核,3未参加审核*/
    private Integer select;

    /** 部门id*/
    private String deptId;

    /** 部门属地*/
    private  String deptName;

    /** 审核状态 **/
    private Integer auditStatus;

    /**年份*/
    private String yearInfo;

    /**是否手动录入*/
    private Integer hand;

    private Integer auditListStatus;
    private String number;

    /**
     * 审核时间*/
    private String auditTime;

    /**
     * 第几批数据*/
    private Integer inputType;

    /**审核id*/
    private String auditId;

    /**
     * 审核人
     * */
    private String auditPerson;





    /**审核结果*/
    private Integer auditResult;

    /**
     * 是否完成自动核查*/
    private String isAutomaticCheck;

    /**是否可以注册*/
    private Integer registerStatus;


    private String isoldexaminee;

    /** 均分考生审核数量，  当前审核账号在审核账号list中所处位置  */
    private Integer indexTotal;
    /** 均分考生审核数量，  审核账号list总数  */
    private Integer auditTotal;



    /** 报名企业  */
    private String oldWork;



}



