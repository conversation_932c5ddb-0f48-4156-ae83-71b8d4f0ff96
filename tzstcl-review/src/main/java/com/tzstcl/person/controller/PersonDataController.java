package com.tzstcl.person.controller;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.common.utils.DateUtils;
import com.tzstcl.common.utils.SecurityUtils;
import com.tzstcl.department.controller.MyException;
import com.tzstcl.person.domain.PersonInfo;
import com.tzstcl.person.service.IPersonInfoService;
import com.tzstcl.uploadFile.util.MinioUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import com.tzstcl.common.annotation.Log;
import com.tzstcl.common.core.controller.BaseController;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.enums.BusinessType;
import com.tzstcl.person.domain.PersonData;
import com.tzstcl.person.service.IPersonDataService;
import com.tzstcl.common.utils.poi.ExcelUtil;
import com.tzstcl.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import static com.tzstcl.common.core.domain.AjaxResult.error;

/**
 * 考生资料Controller
 *
 * <AUTHOR>
 * @date 2022-11-22
 */
@RestController
@RequestMapping("/person/data")
public class PersonDataController extends BaseController {
    @Autowired
    private IPersonDataService personDataService;

    @Autowired
    private IPersonInfoService personInfoService;


    @Autowired
    private MinioUtils minioUtils;

    /**
     * 查询考生资料列表
     */
    @PreAuthorize("@ss.hasPermi('person:data:list')")
    @GetMapping("/list")
    public TableDataInfo list(PersonData personData) {
        startPage();
        List<PersonData> list = personDataService.selectPersonDataList(personData);
        return getDataTable(list);
    }

    /**
     * 导出考生资料列表
     */
    /*@PreAuthorize("@ss.hasPermi('person:data:export')")
    @Log(title = "考生资料", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PersonData personData) {
        List<PersonData> list = personDataService.selectPersonDataList(personData);
        ExcelUtil<PersonData> util = new ExcelUtil<PersonData>(PersonData.class);
        util.exportExcel(response, list, "考生资料数据");
    }*/

    /**
     * 根据身份证号获取考生资料
     */

    @GetMapping("getByIdCard")
    public AjaxResult getInfo(String IdCard) {
        SysUser sysUser= SecurityUtils.getLoginUser().getUser();
        if(sysUser.getUserType().equals("03")){
            if(!sysUser.getUserName().equals(IdCard)){
                return AjaxResult.error("禁止查询非本人的信息！");
            }
        }
        return AjaxResult.success(personDataService.selectPersonDataByIdCard(IdCard));
    }

//    /**
//     * 新增考生资料
//     */
//    @PreAuthorize("@ss.hasPermi('person:data:add')")
//    @Log(title = "考生资料", businessType = BusinessType.INSERT)
//    @PostMapping("/addData")
//    public AjaxResult add(@RequestBody JSONObject object)
//    {
//        JSONArray array = JSONArray.parseArray(object.getString("array"));
//        Integer candidateType = object.getInteger("candidateType");
//        return personDataService.add(array,candidateType);
//    }

    /**
     * 上传考生相关资料
     */
    @PostMapping("/up")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "考生资料上传文件", businessType = BusinessType.INSERT)
    public AjaxResult up(@RequestParam("file") MultipartFile file, @RequestParam("candidateType") Integer candidateType, @RequestParam("type") String type) throws Exception {
        //允许上传的文件格式
        String[] extensions = {"jpg", "jpeg", "png", "pdf"};

        int flag = 0;
        for (String extension : extensions) {
            if (extension.equals(MinioUtils.getExtension(file))) {
                flag = 1;
            }
        }
        if (flag == 0) {
            return AjaxResult.error("只允许上传图片");
        }
        if (file.getSize() > 33554432L) {
            throw new MyException("文件过大，请上传32Mb一下的文件");
        }

        //查询考生资料
        PersonData personData = new PersonData();
        personData.setIdCard(SecurityUtils.getUsername());
        personData.setCandidateType(candidateType);
        personData.setType(type);
        PersonData dataByType = personDataService.selectPersonDataByType(personData);
        if (dataByType != null) {
            return AjaxResult.error("上传失败，该附件已存在，请先删除后再次上传");
        }


        String fileName = minioUtils.uploadFile(file);

        personData.setPath(fileName);
        personData.setCreateBy(SecurityUtils.getUserId().toString());
        personData.setCreateTime(DateUtils.getNowDate());
        personData.setDelFlag(0);
        int i = personDataService.insertPersonData(personData);
        if (i > 0) {
            return AjaxResult.success(fileName);
        }


        return error("上传异常，请联系管理员");
    }

    @PreAuthorize("@ss.hasPermi('person:data:add')")
    @Log(title = "考生资料", businessType = BusinessType.INSERT)
    @PostMapping("/addData")
    public AjaxResult addAudit(@RequestBody JSONObject object) {
        //判断用户状态
        PersonInfo personInfo = personInfoService.getInfoByIdCardAndcandidateType(SecurityUtils.getUsername(), object.getInteger("candidateType"));
        if (personInfo.getAuditStatus() == 4) {
            //如果是审核驳回状态，禁止再次提交审核
            return AjaxResult.error("审核不通过，无法再次提交，请联系主管部门前往线下提交材料进行审核");
        }
        JSONArray array = JSONArray.parseArray(object.getString("array"));
        Integer candidateType = object.getInteger("candidateType");
        return personDataService.addAudit(array, candidateType);
    }

    @Log(title = "考生资料", businessType = BusinessType.INSERT)
    @PostMapping("/updateData")
    public AjaxResult updateAuditData(@RequestBody JSONObject object) {
        JSONArray array = JSONArray.parseArray(object.getString("array"));
        String idCard = String.valueOf(object.getString("idCard"));
        Integer candidateType = object.getInteger("candidateType");
        return personDataService.updateAuditData(array, idCard, candidateType);
    }

    /**
     * 修改考生资料
     */
    @PreAuthorize("@ss.hasPermi('person:data:edit')")
    @Log(title = "考生资料", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody PersonData personData) {
        return toAjax(personDataService.updatePersonData(personData));
    }

    /**
     * 删除考生资料
     */
    @PreAuthorize("@ss.hasPermi('person:data:remove')")
    @Log(title = "考生资料", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(personDataService.deletePersonDataByIds(ids));
    }
}
