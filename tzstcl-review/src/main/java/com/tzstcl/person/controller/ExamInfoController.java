package com.tzstcl.person.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzstcl.common.annotation.Log;
import com.tzstcl.common.core.controller.BaseController;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.enums.BusinessType;
import com.tzstcl.person.domain.ExamInfo;
import com.tzstcl.person.service.IExamInfoService;
import com.tzstcl.common.utils.poi.ExcelUtil;
import com.tzstcl.common.core.page.TableDataInfo;

/**
 * 考生报考信息Controller
 * 
 * <AUTHOR>
 * @date 2022-11-22
 */
@RestController
@RequestMapping("/person/exam")
public class ExamInfoController extends BaseController
{
    @Autowired
    private IExamInfoService examInfoService;

    /**
     * 查询考生报考信息列表
     */
    @PreAuthorize("@ss.hasPermi('person:exam:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExamInfo examInfo)
    {
        startPage();
        List<ExamInfo> list = examInfoService.selectExamInfoList(examInfo);
        return getDataTable(list);
    }

    /**
     * 导出考生报考信息列表
     */
    /*@PreAuthorize("@ss.hasPermi('person:exam:export')")
    @Log(title = "考生报考信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExamInfo examInfo)
    {
        List<ExamInfo> list = examInfoService.selectExamInfoList(examInfo);
        ExcelUtil<ExamInfo> util = new ExcelUtil<ExamInfo>(ExamInfo.class);
        util.exportExcel(response, list, "考生报考信息数据");
    }*/

    /**
     * 获取考生报考信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('person:exam:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(examInfoService.selectExamInfoById(id));
    }

    /**
     * 新增考生报考信息
     */
    @PreAuthorize("@ss.hasPermi('person:exam:add')")
    @Log(title = "考生报考信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExamInfo examInfo)
    {
        return toAjax(examInfoService.insertExamInfo(examInfo));
    }

    /**
     * 修改考生报考信息
     */
    @PreAuthorize("@ss.hasPermi('person:exam:edit')")
    @Log(title = "考生报考信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExamInfo examInfo)
    {
        return toAjax(examInfoService.updateExamInfo(examInfo));
    }

    /**
     * 删除考生报考信息
     */
    @PreAuthorize("@ss.hasPermi('person:exam:remove')")
    @Log(title = "考生报考信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(examInfoService.deleteExamInfoByIds(ids));
    }
}
