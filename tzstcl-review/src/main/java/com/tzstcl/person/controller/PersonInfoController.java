package com.tzstcl.person.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.poi.excel.ExcelReader;
import com.alibaba.fastjson2.JSONObject;
import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.common.utils.SecurityUtils;
import com.tzstcl.common.utils.StringUtils;
import com.tzstcl.person.domain.OldWorkImport;
import com.tzstcl.person.domain.PersonInfoImport;
//import com.tzstcl.sms.service.SmsService;
import com.tzstcl.person.domain.PersonInfoImportJl;
import com.tzstcl.system.service.ISysUserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.parameters.P;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.tzstcl.common.annotation.Log;
import com.tzstcl.common.core.controller.BaseController;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.enums.BusinessType;
import com.tzstcl.person.domain.PersonInfo;
import com.tzstcl.person.service.IPersonInfoService;
import com.tzstcl.common.utils.poi.ExcelUtil;
import com.tzstcl.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 考生信息Controller
 *
 * <AUTHOR>
 * @date 2022-11-17
 */
@RestController
@RequestMapping("/person/info")
public class PersonInfoController extends BaseController {

    @Autowired
    private IPersonInfoService personInfoService;
    @Autowired
    private ISysUserService sysUserService;

    /**
     * 查询考生信息列表
     */
    @PreAuthorize("@ss.hasPermi('person:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(PersonInfo personInfo) {
        startPage();
        List<PersonInfo> list = personInfoService.selectPersonInfoList(personInfo);
        return getDataTable(list);
    }

    /**
     * 导出考生信息列表
     */
  /*  @PreAuthorize("@ss.hasPermi('person:info:export')")
    @Log(title = "考生信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PersonInfo personInfo) {
        List<PersonInfo> list = personInfoService.selectPersonInfoList(personInfo);
        ExcelUtil<PersonInfo> util = new ExcelUtil<PersonInfo>(PersonInfo.class);
        util.exportExcel(response, list, "考生信息数据");
    }
*/

    /**
     * 获取考生信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('person:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(personInfoService.selectPersonInfoById(id));
    }

    /**
     * 根据身份证号获取考生信息
     *
     * @param candidateType
     * @return
     */
    @GetMapping("/getInfoByIdCard")
    public AjaxResult getInfoByIdCard(Integer candidateType) {
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        //防止查询其他人信息
        PersonInfo personInfo = personInfoService.getInfoByIdCardAndcandidateType(sysUser.getUserName(), candidateType);
        if (personInfo == null) {
            return AjaxResult.error("未查询到考生信息");
        }
        return AjaxResult.success("存在此考生信息", personInfo);
    }

    /**
     * 检查注册
     */
    @GetMapping("/checkRegister")
    public AjaxResult checkRegister(String IdCard) {
        PersonInfo personInfo = personInfoService.checkRegister(IdCard);
        if (sysUserService.selectUserByUserName(IdCard) != null) {
            return AjaxResult.success("该账号已注册");
        }
        if (personInfo == null) {
            return AjaxResult.success("不可注册");
        }
        return AjaxResult.success("可注册", personInfo);
    }

    /**
     * 根据Id获取考生信息
     *
     * @param Id
     */
    @GetMapping("/getInfoById")
    public AjaxResult getInfoById(String Id) {
        PersonInfo personInfo = personInfoService.getInfoById1(Id);
        return AjaxResult.success(personInfo);
    }

    /**
     * 新增考生信息
     */
    @PreAuthorize("@ss.hasPermi('person:info:add')")
    @Log(title = "考生信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PersonInfoImport personInfo) {
        return toAjax(personInfoService.insertPersonInfo(personInfo));
    }

    /**
     * 修改考生信息
     */
    @PreAuthorize("@ss.hasPermi('person:info:edit')")
    @Log(title = "考生信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody PersonInfo personInfo) {
        Integer hand = personInfo.getHand();
        String corpCode = personInfo.getCorpCode();
        if (hand == 0) {
            if (corpCode == null || corpCode.equals("")) {
                return AjaxResult.error("请完善选择工作单位信息");
            }
        }
        personInfo.setFlag(1);
        return toAjax(personInfoService.updatePersonInfo(personInfo));
    }

    /**
     * 删除考生信息
     */
    @PreAuthorize("@ss.hasPermi('person:info:remove')")
    @Log(title = "考生信息", businessType = BusinessType.DELETE)
    @GetMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(personInfoService.deletePersonInfoByIds(ids));
    }

    /**
     * 获取首页数据反馈
     */
    @GetMapping("/getData")
    public AjaxResult getData(Integer candidateType) {

        JSONObject dataIndex = personInfoService.getDataIndex(candidateType);
        Object registerStatus = dataIndex.get("registerStatus");
        if (registerStatus != null && registerStatus.equals(1)) {
            return AjaxResult.error("审核时间已结束，如有异议请联系主管部门！");
        }
        return AjaxResult.success(dataIndex);
    }

    /**
     * 考生信息导入
     */
    @PostMapping("/importData")
    public AjaxResult importData(Integer candidateType, MultipartFile file) throws Exception {
        return AjaxResult.success(personInfoService.importData(candidateType, file));
    }

    /**
     * 考生信息上传模板
     */
    @PostMapping("/getImport")
    public void getImport(HttpServletResponse response) {
        List<PersonInfoImport> list = new ArrayList<>();
        ExcelUtil<PersonInfoImport> util = new ExcelUtil<PersonInfoImport>(PersonInfoImport.class);
        util.exportExcel(response, list, "考生信息上传模板");
    }

    /**
     * 根据身份证号查找人员报考资质信息
     */
    @GetMapping("/getPersonInfoByIdCard")
    public AjaxResult getPersonInfoByIdCard() {
        String IdCard = SecurityUtils.getUsername();
        return AjaxResult.success(personInfoService.getPersonInfoByIdCard(IdCard));
    }

    /**
     * 根据报考类型
     */
    @GetMapping("/getPersonInfoByType")
    public AjaxResult getPersonInfoByType() {
        return AjaxResult.success(personInfoService.getPersonInfoByType());
    }

    /**
     * 考生提交信息返回上一步
     */
    @PutMapping("/back/{id}")
    public AjaxResult backInfo(@PathVariable String id) {
        PersonInfo personInfo = personInfoService.selectPersonInfoById(id);
        personInfo.setFlag(0);
        return toAjax(personInfoService.updatePersonInfo(personInfo));
    }

    //yijingbuyongle
    @PutMapping("/updateOldWork")
    public AjaxResult updateOldWork(Integer inputType, MultipartFile file) throws Exception {
        ExcelUtil<OldWorkImport> util = new ExcelUtil<OldWorkImport>(OldWorkImport.class);
        List<OldWorkImport> list = util.importExcel(file.getInputStream());

        List<OldWorkImport> errorList = new ArrayList<>();

        for (OldWorkImport oldWorkImport : list) {
            oldWorkImport.setInputType(inputType);
            int i = personInfoService.updateOldWorkByIdCard(oldWorkImport);
            if (i < 1) {
                errorList.add(oldWorkImport);
            }
        }
        return AjaxResult.success("导入成功,失败的有", errorList);

    }

    @PostMapping("/updateOldType")
    public AjaxResult updateOldType(MultipartFile file) throws Exception {
        ExcelUtil<OldWorkImport> util = new ExcelUtil<OldWorkImport>(OldWorkImport.class);
        List<OldWorkImport> list = util.importExcel(file.getInputStream());
        List<OldWorkImport> errorList = new ArrayList<>();
        for (OldWorkImport oldWorkImport : list) {
            oldWorkImport.setInputType(19);
            String candidateType1 = oldWorkImport.getCandidateType1();
            if ("一级注册建筑师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(1);
            } else if ("二级注册建筑师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(2);
            } else if ("勘察设计注册工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(3);
            } else if ("一级注册结构工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(4);
            } else if ("二级注册结构工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(5);
            } else if ("注册土木工程师（岩土）".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(6);
            } else if ("注册土木工程师（港口与航道工程）".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(7);
            } else if ("注册公用设备工程师（暖通空调）".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(8);
            } else if ("注册公用设备工程师（给水排水）".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(9);
            } else if ("注册公用设备工程师（动力）".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(10);
            } else if ("注册电气工程师（发输变电）".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(11);
            } else if ("注册电气工程师（供配电）".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(12);
            } else if ("注册化工工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(13);
            } else if ("注册监理工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(14);
            } else if ("注册建造师".equals(candidateType1.trim())) {
                // 注意：这里你有一个“注册建造师”但没有指定是一级还是二级，可能需要进一步细化
                oldWorkImport.setCandidateType(15); // 假设这是通用的注册建造师类型
            } else if ("一级注册建造师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(16);
            } else if ("二级注册建造师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(17);
            } else if ("注册造价工程师".equals(candidateType1.trim())) {
                // 同样，这里你有一个“注册造价工程师”但没有指定是一级还是二级
                // 根据你的业务逻辑，你可能需要处理这种情况
                oldWorkImport.setCandidateType(18); // 需要一个默认值或者进一步细化
            } else if ("一级注册造价工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(19);
            } else if ("二级注册造价工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(20);
            } else if ("注册建筑师".equals(candidateType1.trim())) {
                // 注意：这里你有一个泛指的“注册建筑师”，它可能与“一级注册建筑师”或“二级注册建筑师”不同
                // 根据你的业务逻辑，你可能需要处理这种情况
                oldWorkImport.setCandidateType(21); // 需要一个默认值或者进一步细化，或者与上面的某个类型合并
            } else if ("注册土木工程师(水利水电工程(水利水电工程规划))".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(22);
            } else if ("注册土木工程师(水利水电工程(水工结构))".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(23);
            } else if ("注册土木工程师(水利水电工程(水利水电工程地质))".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(24);
            } else if ("注册土木工程师(水利水电工程(水利水电工程移民))".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(25);
            } else if ("注册土木工程师(水利水电工程(水利水电工程水土保持))".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(26);
            } else if ("注册土木工程师(道路工程)".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(27);
            } else if ("注册环保工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(28);
            } else {
                // 处理未知类型
                System.out.println("未知的候选人类型: " + candidateType1);
                // 可以选择设置一个默认类型或者抛出异常等
                // personInfoImport.setCandidateType(someDefaultValue);
            }
            int i = personInfoService.updateOldWorkByIdCard(oldWorkImport);
            if (i < 1) {
                errorList.add(oldWorkImport);
            }
        }
        return AjaxResult.success("导入成功,失败的有", errorList);

    }
/*

    @PostMapping("/updateOldPerson")
    public AjaxResult updateOldPerson(MultipartFile file) throws Exception {
        ExcelUtil<OldWorkImport> util = new ExcelUtil<OldWorkImport>(OldWorkImport.class);
        List<OldWorkImport> list = util.importExcel(file.getInputStream());
        List<OldWorkImport> errorList = new ArrayList<>();
        for (OldWorkImport oldWorkImport : list) {
            oldWorkImport.setInputType(19);
            String candidateType1 = oldWorkImport.getCandidateType1();
            if ("一级注册建筑师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(1);
            } else if ("二级注册建筑师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(2);
            } else if ("勘察设计注册工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(3);
            } else if ("一级注册结构工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(4);
            } else if ("二级注册结构工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(5);
            } else if ("注册土木工程师（岩土）".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(6);
            } else if ("注册土木工程师（港口与航道工程）".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(7);
            } else if ("注册公用设备工程师（暖通空调）".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(8);
            } else if ("注册公用设备工程师（给水排水）".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(9);
            } else if ("注册公用设备工程师（动力）".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(10);
            } else if ("注册电气工程师（发输变电）".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(11);
            } else if ("注册电气工程师（供配电）".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(12);
            } else if ("注册化工工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(13);
            } else if ("注册监理工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(14);
            } else if ("注册建造师".equals(candidateType1.trim())) {
                // 注意：这里你有一个“注册建造师”但没有指定是一级还是二级，可能需要进一步细化
                oldWorkImport.setCandidateType(15); // 假设这是通用的注册建造师类型
            } else if ("一级注册建造师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(16);
            } else if ("二级注册建造师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(17);
            } else if ("注册造价工程师".equals(candidateType1.trim())) {
                // 同样，这里你有一个“注册造价工程师”但没有指定是一级还是二级
                // 根据你的业务逻辑，你可能需要处理这种情况
                oldWorkImport.setCandidateType(18); // 需要一个默认值或者进一步细化
            } else if ("一级注册造价工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(19);
            } else if ("二级注册造价工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(20);
            } else if ("注册建筑师".equals(candidateType1.trim())) {
                // 注意：这里你有一个泛指的“注册建筑师”，它可能与“一级注册建筑师”或“二级注册建筑师”不同
                // 根据你的业务逻辑，你可能需要处理这种情况
                oldWorkImport.setCandidateType(21); // 需要一个默认值或者进一步细化，或者与上面的某个类型合并
            } else if ("注册土木工程师(水利水电工程(水利水电工程规划))".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(22);
            } else if ("注册土木工程师(水利水电工程(水工结构))".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(23);
            } else if ("注册土木工程师(水利水电工程(水利水电工程地质))".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(24);
            } else if ("注册土木工程师(水利水电工程(水利水电工程移民))".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(25);
            } else if ("注册土木工程师(水利水电工程(水利水电工程水土保持))".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(26);
            } else if ("注册土木工程师(道路工程)".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(27);
            } else if ("注册环保工程师".equals(candidateType1.trim())) {
                oldWorkImport.setCandidateType(28);
            } else {
                // 处理未知类型
                System.out.println("未知的候选人类型: " + candidateType1);
                // 可以选择设置一个默认类型或者抛出异常等
                // personInfoImport.setCandidateType(someDefaultValue);
            }


    }
*/

    @PostMapping("/importData/updateStatus")
    public AjaxResult updateStatus(Integer inputType, MultipartFile file) throws Exception {
        ExcelUtil<OldWorkImport> util = new ExcelUtil<OldWorkImport>(OldWorkImport.class);
        List<OldWorkImport> list = util.importExcel(file.getInputStream());
        List<OldWorkImport> errorList = new ArrayList<>();
        for (OldWorkImport oldWorkImport : list) {
            SysUser sysUser = new SysUser();
            sysUser.setUserName(oldWorkImport.getIdCard());
            sysUser.setStatus("1");
            int i = sysUserService.updateUserStatusByUserName(sysUser);
            if (i < 1) {
                errorList.add(oldWorkImport);
            }
        }
        return AjaxResult.success("导入成功,失败的有", errorList);
    }

    @PostMapping("update/person/phoneNum/by/admin123")
    public AjaxResult updatePhoneNum(@RequestBody PersonInfo personInfo) {
        return personInfoService.updatePhoneNum(personInfo);
    }


}
