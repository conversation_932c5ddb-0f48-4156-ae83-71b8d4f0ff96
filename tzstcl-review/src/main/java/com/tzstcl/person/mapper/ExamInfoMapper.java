package com.tzstcl.person.mapper;

import java.util.List;
import com.tzstcl.person.domain.ExamInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 考生报考信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-11-22
 */
@Mapper
public interface ExamInfoMapper 
{
    /**
     * 查询考生报考信息
     * 
     * @param id 考生报考信息主键
     * @return 考生报考信息
     */
    public ExamInfo selectExamInfoById(String id);

    /**
     * 查询考生报考信息列表
     * 
     * @param examInfo 考生报考信息
     * @return 考生报考信息集合
     */
    public List<ExamInfo> selectExamInfoList(ExamInfo examInfo);
    public List<ExamInfo> selectExamInfoList1(@Param("idCard") String idCard,@Param("candidateType") Integer candidateType);


    /**
     * 新增考生报考信息
     * 
     * @param examInfo 考生报考信息
     * @return 结果
     */
    public int insertExamInfo(ExamInfo examInfo);

    /**
     * 修改考生报考信息
     * 
     * @param examInfo 考生报考信息
     * @return 结果
     */
    public int updateExamInfo(ExamInfo examInfo);

    /**
     * 删除考生报考信息
     * 
     * @param id 考生报考信息主键
     * @return 结果
     */
    public int deleteExamInfoById(String id);

    /**
     * 批量删除考生报考信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExamInfoByIds(String[] ids);
}
