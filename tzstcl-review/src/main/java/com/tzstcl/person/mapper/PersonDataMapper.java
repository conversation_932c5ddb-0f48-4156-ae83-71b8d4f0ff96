package com.tzstcl.person.mapper;

import java.util.List;
import com.tzstcl.person.domain.PersonData;
import com.tzstcl.person.domain.PersonInfo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 考生资料Mapper接口
 *
 * <AUTHOR>
 * @date 2022-11-22
 */
@Mapper
public interface PersonDataMapper
{
    /**
     * 查询考生资料
     *
     * @param id 考生资料主键
     * @return 考生资料
     */
    public PersonData selectPersonDataById(String id);

    /**
     * 查询考生资料列表
     *
     * @param personData 考生资料
     * @return 考生资料集合
     */
    public List<PersonData> selectPersonDataList(PersonData personData);

    /**
     * 新增考生资料
     *
     * @param personData 考生资料
     * @return 结果
     */
    public int insertPersonData(PersonData personData);

    /**
     * 修改考生资料
     *
     * @param personData 考生资料
     * @return 结果
     */
    public int updatePersonData(PersonData personData);

    /**
     * 删除考生资料
     *
     * @param id 考生资料主键
     * @return 结果
     */
    public int deletePersonDataById(String id);

    /**
     * 批量删除考生资料
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePersonDataByIds(String[] ids);

    List<PersonData> getByIdCard(String IdCard);

    int updatePersonDataByIdCard(PersonInfo personInfo);

    PersonData selectPersonDataByPath(String url);

    PersonData selectPersonDataByType(PersonData personData);
}
