package com.tzstcl.person.mapper;

import java.util.List;

import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.core.domain.entity.SysDept;
import com.tzstcl.person.domain.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 考生信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-11-17
 */
@Mapper
public interface PersonInfoMapper
{
    /**
     * 查询考生信息
     *
     * @param id 考生信息主键
     * @return 考生信息
     */
    public PersonInfo selectPersonInfoById(String id);

    public PersonInfo getOneInfoexamInfo(String id);

    /**
     * 查询考生信息列表
     *
     * @param personInfo 考生信息
     * @return 考生信息集合
     */
    public List<PersonInfo> selectPersonInfoList(PersonInfo personInfo);

    /**
     * 新增考生信息
     *
     * @param personInfo 考生信息
     * @return 结果
     */
    public int insertPersonInfo(PersonInfoImport personInfo);

    /**
     * 修改考生信息
     *
     * @param personInfo 考生信息
     * @return 结果
     */
    public int updatePersonInfo(PersonInfo personInfo);

    /**
     * 删除考生信息
     *
     * @param id 考生信息主键
     * @return 结果
     */
    public int deletePersonInfoById(String id);

    /**
     * 批量删除考生信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePersonInfoByIds(String[] ids);

    /**
     * 根据身份证号获取考生信息
     * @param  IdCard 身份证号
     */
    PersonInfo getInfoByIdCard(String IdCard);
    PersonInfo getInfoByArchives_no(String IdCard);

    /**
     * 根据部门查看考生
     * @param sysDept
     * @return
     */
    List<PersonInfo> selectByDept(SysDept sysDept);

    /**
     * 根据部门及条件查看考生
     * @param personInfo
     * @return
     */
    List<PersonInfo> getByDept(PersonInfo personInfo);

    /**
     * 根据身份证号修改
     * @param personInfoImport
     */

    int updatePersonInfoByIdCard(PersonInfoImport personInfoImport);

//    PersonInfo getInfoByIdCardAndcandidateType(@Param("idCard") String idCard,@Param("candidateType") Integer candidateType);
    PersonInfo getInfoByIdCardAndcandidateType(@Param("idCard")String idCard,@Param("candidateType")Integer candidateType);

    List<PersonInfo> getPersonInfoByIdCard(String IdCard);

    PersonInfo getInfoByIdCard1(String idCard);

    List<PersonInfo> selectPersonInfoById1(String IdCard);


    List<PersonInfo> selectPersonInfoLists(PersonInfo personInfo);

    List<PersonInfo> selectPersonInfoaudirStatusIsNotNull(PersonInfo personInfo);

    List<PersonInfo> selectPersonInfoAuditStatusIsNull(PersonInfo personInfo);

    Integer selectPersonInfoListsCount(PersonInfo personInfo);

    List<PersonInfo> selectPersonInfoLists1(PersonInfo personInfo);

    Long countPersonInfoAuditStatusIsNull();

    int updatePersonInfoAuditPersonByIdCard(AuditPersonVo auditPersonVo);

    List<PersonInfoExport> selectPersonInfoListExprot(PersonInfo personInfo);

    PersonInfo selectPersonInfoByIdCardAndCandType(PersonInfo personInfo);

    int updatePersonInfoByCandidateType(@Param("registerStatus") Integer registerStatus, @Param("candidateType") Integer candidateType);

    int updatePersonInfoById(@Param("id") String id,@Param("registerStatus") Integer registerStatus);

    List<String> getPersonInfoByType();

    List<PersonInfo> getPersonInfoByPhone(PersonInfo personInfo);

    List<PersonInfo> selectCorpAuditPersonList(PersonInfo personInfo);

    /**
     * 根据审核账号数量分配考生数量
     * @param personInfo
     * @return
     */
    List<PersonInfo> selectPersonInfoListsByAverage(PersonInfo personInfo);

    /**
     * 根据待审核分配考生数量
     * @param personInfo
     * @return
     */
    List<PersonInfo> selectPersonInfoListsByAverageExam(PersonInfo personInfo);

    Integer selectMaxInputTypeByCandidateType(Integer candidateType);

    List<PersonInfo> selectPersonInfoListByEjzjsPush(Integer inputType);

    int selectMaxInputType();

    int updateOldWorkByIdCard(OldWorkImport oldWorkImport);

    List<Long> selectUserListByAuditStatus(PersonInfo personInfo);
}
