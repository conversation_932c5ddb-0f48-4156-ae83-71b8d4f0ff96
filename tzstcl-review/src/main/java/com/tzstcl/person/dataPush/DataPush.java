package com.tzstcl.person.dataPush;

import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONObject;
import com.tzstcl.person.domain.PersonInfo;
import com.tzstcl.person.service.IPersonInfoService;
import com.tzstcl.person.utils.AESUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

import static com.tzstcl.person.utils.AESUtil.encrypt;

/**
 * 推送数据
 *
 * <AUTHOR> on 2024/5/6
 */

@RestController
@RequestMapping("/data/push")
public class DataPush {

    @Autowired
    private IPersonInfoService personInfoService;

    @GetMapping("/ejzjs")
    public String ejzjsPush(String code) throws Exception {
        if (!"zxmmxz".equals(code)) {
            return "令牌验证码错误";
        }

        PersonInfozghs personInfozghs = new PersonInfozghs();
        String userName = encrypt("lsq0514");
        String password = encrypt("lsq0514dsz");
        personInfozghs.setUserName(userName);

        personInfozghs.setPassword(password);

        List<PersonInfo> personInfos = personInfoService.selectPersonInfoListByEjzjsPush();
        String jsonString = JSONObject.toJSONString(personInfos);
        personInfozghs.setPersonInfo(jsonString);
        String params = JSONObject.toJSONString(personInfozghs);
        String result = HttpRequest.post("http://***********:9007/personInfo/zghsAdd")
//        String result = HttpRequest.post("http://************:8666/personInfo/zghsAdd")
                .header(Header.ACCEPT, String.valueOf(ContentType.JSON))
                .body(params)
                .execute().body();
        System.out.println(result);
        return result+"\n\t结果: "+jsonString;

    }


}
