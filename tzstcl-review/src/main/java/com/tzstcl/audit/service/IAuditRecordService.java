package com.tzstcl.audit.service;

import java.util.List;
import com.tzstcl.audit.domain.AuditRecord;

/**
 * 审核记录Service接口
 * 
 * <AUTHOR>
 * @date 2022-11-23
 */
public interface IAuditRecordService 
{
    /**
     * 查询审核记录
     * 
     * @param id 审核记录主键
     * @return 审核记录
     */
    public AuditRecord selectAuditRecordById(String id);

    /**
     * 查询审核记录列表
     * 
     * @param auditRecord 审核记录
     * @return 审核记录集合
     */
    public List<AuditRecord> selectAuditRecordList(AuditRecord auditRecord);

    /**
     * 新增审核记录
     * 
     * @param auditRecord 审核记录
     * @return 结果
     */
    public int insertAuditRecord(AuditRecord auditRecord);

    /**
     * 修改审核记录
     * 
     * @param auditRecord 审核记录
     * @return 结果
     */
    public int updateAuditRecord(AuditRecord auditRecord);

    /**
     * 批量删除审核记录
     * 
     * @param ids 需要删除的审核记录主键集合
     * @return 结果
     */
    public int deleteAuditRecordByIds(String[] ids);

    /**
     * 删除审核记录信息
     * 
     * @param id 审核记录主键
     * @return 结果
     */
    public int deleteAuditRecordById(String id);
}
