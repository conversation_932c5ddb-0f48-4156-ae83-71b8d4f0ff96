package com.tzstcl.audit.service;

import java.util.List;
import com.tzstcl.audit.domain.Audit;
import com.tzstcl.common.core.domain.AjaxResult;

/**
 * 审批
Service接口
 * 
 * <AUTHOR>
 * @date 2022-11-23
 */
public interface IAuditService 
{
    /**
     * 查询审批

     * 
     * @param id 审批
主键
     * @return 审批

     */
    public Audit selectAuditById(String id);

    /**
     * 查询审批
列表
     * 
     * @param audit 审批

     * @return 审批
集合
     */
    public List<Audit> selectAuditList(Audit audit);

    /**
     * 新增审批

     * 
     * @param audit 审批

     * @return 结果
     */
    public int insertAudit(Audit audit);

    /**
     * 修改审批

     * 
     * @param audit 审批

     * @return 结果
     */
    public int updateAudit(Audit audit);

    /**
     * 批量删除审批

     * 
     * @param ids 需要删除的审批主键集合
     * @return 结果
     */
    public int deleteAuditByIds(String[] ids);

    /**
     * 删除审批信息
     * 
     * @param id 审批主键
     * @return 结果
     */
    public int deleteAuditById(String id);
    /**
     * 根据身份证号获取审核信息
     */
    List<Audit> getByIdCard(String idCard);


    int revokeAudit(String id);

    List<Audit> getByIdCard1(String idCard, Integer candidateType);

    int personAudit(Audit audit);

    int updateAuditByCorp(Audit audit);
}
