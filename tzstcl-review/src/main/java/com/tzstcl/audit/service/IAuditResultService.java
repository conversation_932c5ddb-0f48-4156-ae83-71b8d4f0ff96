package com.tzstcl.audit.service;


import com.tzstcl.audit.domain.AuditResult;
import com.tzstcl.common.core.domain.AjaxResult;

import java.util.List;

/**
 * 人员审核全部完成后提交的审核结果Service接口
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
public interface IAuditResultService {
    /**
     * 查询人员审核全部完成后提交的审核结果
     *
     * @param id 人员审核全部完成后提交的审核结果主键
     * @return 人员审核全部完成后提交的审核结果
     */
    public AuditResult selectAuditResultById(Long id);

    /**
     * 查询人员审核全部完成后提交的审核结果列表
     *
     * @param auditResult 人员审核全部完成后提交的审核结果
     * @return 人员审核全部完成后提交的审核结果集合
     */
    public List<AuditResult> selectAuditResultList(AuditResult auditResult);

    /**
     * 新增人员审核全部完成后提交的审核结果
     *
     * @param auditResult 人员审核全部完成后提交的审核结果
     * @return 结果
     */
    public AjaxResult insertAuditResult(AuditResult auditResult);

    /**
     * 修改人员审核全部完成后提交的审核结果
     *
     * @param auditResult 人员审核全部完成后提交的审核结果
     * @return 结果
     */
    public int updateAuditResult(AuditResult auditResult);

    /**
     * 批量删除人员审核全部完成后提交的审核结果
     *
     * @param ids 需要删除的人员审核全部完成后提交的审核结果主键集合
     * @return 结果
     */
    public int deleteAuditResultByIds(Long[] ids);

    /**
     * 删除人员审核全部完成后提交的审核结果信息
     *
     * @param id 人员审核全部完成后提交的审核结果主键
     * @return 结果
     */
    public int deleteAuditResultById(Long id);
}
