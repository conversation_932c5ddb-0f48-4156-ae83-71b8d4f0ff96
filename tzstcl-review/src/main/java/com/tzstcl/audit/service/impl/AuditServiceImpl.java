package com.tzstcl.audit.service.impl;

import java.text.SimpleDateFormat;
import java.util.*;

import afu.org.checkerframework.checker.units.qual.A;
import com.tzstcl.audit.domain.AuditRecord;
import com.tzstcl.audit.service.IAuditRecordService;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.core.domain.entity.SysDept;
import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.common.enums.AuditStatusEnum;
import com.tzstcl.common.utils.DateUtils;
import com.tzstcl.common.utils.SecurityUtils;
import com.tzstcl.common.utils.SnowflakeIdWorker;
import com.tzstcl.common.utils.StringUtils;
import com.tzstcl.company.domain.Company;
import com.tzstcl.company.service.ICompanyService;
import com.tzstcl.person.domain.PersonInfo;
import com.tzstcl.person.mapper.PersonDataMapper;
import com.tzstcl.person.mapper.PersonInfoMapper;
import com.tzstcl.person.service.IPersonInfoService;
import com.tzstcl.system.mapper.SysDeptMapper;
import com.tzstcl.system.service.ISysDeptService;
import com.tzstcl.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzstcl.audit.mapper.AuditMapper;
import com.tzstcl.audit.domain.Audit;
import com.tzstcl.audit.service.IAuditService;
import org.springframework.transaction.annotation.Transactional;

import javax.xml.crypto.Data;

import static com.tzstcl.common.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 审批
 * Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-23
 */
@Service
public class AuditServiceImpl implements IAuditService {
    @Autowired
    private AuditMapper auditMapper;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IAuditRecordService auditRecordService;
    @Autowired
    private IPersonInfoService personInfoService;
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private PersonInfoMapper personInfoMapper;

    @Autowired
    private PersonDataMapper personDataMapper;

    /**
     * 查询审批
     *
     * @param id 审批主键
     * @return 审批
     */
    @Override
    public Audit selectAuditById(String id) {
        return auditMapper.selectAuditById(id);
    }

    /**
     * 查询审批列表
     *
     * @param audit 审批
     * @return 审批
     */
    @Override
    public List<Audit> selectAuditList(Audit audit) {
        return auditMapper.selectAuditList(audit);
    }

    /**
     * 新增审批
     *
     * @param audit 审批
     * @return 结果
     */
    @Override
    public int insertAudit(Audit audit) {
        SysDept dept = new SysDept();
        List<SysDept> deptList = new ArrayList<>();
        //查询该考生
        PersonInfo personInfo = personInfoService.getInfoByIdCardAndcandidateType(SecurityUtils.getUsername(), audit.getCandidateType());
        audit.setSponsor(SecurityUtils.getUserId().toString());//发起人

        //todo 创建一个时间在第六批刚开始的时间 2023-06-12 14:51:20
        audit.setTime(personInfo.getCreateTime());
        List<Audit> list = selectAuditList(audit);
        audit.setNumber(StringUtils.numToChinese(String.valueOf((list.size() + 1))) + "次申请");//提交次数
        audit.setId(String.valueOf(SnowflakeIdWorker.getInstance().nextId()));
        audit.setTime(DateUtils.getNowDate());
        if (personInfo != null) {
            if (personInfo.getHand() == 1) {
                //根据报考地址获取主管部门的行政区域代码
                if (StringUtils.isNotEmpty(personInfo.getCityName())) {
                    dept.setRemark(personInfo.getCityName());
                    deptList = sysDeptService.selectDeptList(dept);
                    if (deptList.size() == 0) {
                        audit.setHanlder(null);
                    } else {
                        audit.setHanlder(deptList.get(0).getDeptId().toString());
                    }
                }
                audit.setAuditStatus(2);
            } else if (StringUtils.isNotEmpty(personInfo.getCorpCode()) && companyService.getByCorpCode(personInfo.getCorpCode()) != null) {//考生的工作单位在系统中，下一步审核人则为企业
                audit.setHanlder(personInfo.getCorpCode());
                audit.setAuditStatus(0);
                personInfo.setAuditStatus(1);
                personInfo.setUpdateBy(SecurityUtils.getUsername());
                personInfo.setUpdateTime(new Date());
                personInfoService.updatePersonInfo(personInfo);
            } else {
                //根据报考地址获取主管部门的行政区域代码
                if (StringUtils.isNotEmpty(personInfo.getCityName())) {
                    dept.setRemark(personInfo.getCityName());
                    deptList = sysDeptService.selectDeptList(dept);
                    if (deptList.size() == 0) {
                        audit.setHanlder(null);
                    } else {
                        audit.setHanlder(deptList.get(0).getDeptId().toString());
                    }
                }
                audit.setAuditStatus(2);
            }
        }
        return auditMapper.insertAudit(audit);
    }


    @Override
    public int personAudit(Audit audit) {
        //查询该考生
        PersonInfo personInfo = personInfoService.getInfoByIdCardAndcandidateType(SecurityUtils.getUsername(), audit.getCandidateType());
        //审核表基本信息
        audit.setSponsor(SecurityUtils.getLoginUser().getUserId().toString());//发起人
        audit.setIdCard(SecurityUtils.getUsername());

        //todo 创建一个时间在第六批刚开始的时间 2023-06-12 14:51:20
        audit.setTime(personInfo.getCreateTime());
        Integer number = auditMapper.selectAuditListByIdCard(audit);
        if (number == null) {
            number = 0;
        }
        audit.setAuditNumber(number + 1);
        audit.setId(String.valueOf(SnowflakeIdWorker.getInstance().nextId()));
        audit.setTime(DateUtils.getNowDate());

        Company byCorpCode = companyService.getByCorpCode(personInfo.getCorpCode());
        if (!Objects.isNull(personInfo)) {
            //根据考生是否是企业录入，判断下一步审核人hand==1是主管部门，否则是企业
            Integer hand = personInfo.getHand();
            if (hand == 0 && StringUtils.isNotEmpty(personInfo.getCorpCode()) && Objects.nonNull(byCorpCode)) {
                audit.setHanlder(byCorpCode.getCorpCode());
                //待企业审核
                audit.setAuditStatus(0);
                personInfo.setAuditStatus(0);

            } else if (hand == 1) {
                //根据报考地址获取主管部门的行政区域代码
                if (StringUtils.isNotEmpty(personInfo.getCityName())) {
                    SysDept sysDept = sysDeptMapper.selectDeptListByRemark(personInfo.getCityName());
                    if (Objects.isNull(sysDept)) {
                        audit.setHanlder(null);
                    } else {
                        audit.setHanlder(sysDept.getDeptId().toString());
                    }
                }
                //待主管部门审核
                audit.setAuditStatus(2);
                personInfo.setAuditStatus(2);
            }
        }


        personInfo.setUpdateBy(SecurityUtils.getUsername());
        personInfo.setUpdateTime(new Date());
        personInfo.setAuditResult(1);
        personInfoService.updatePersonInfo(personInfo);
        return auditMapper.insertAudit(audit);
    }

    @Override
    public int updateAuditByCorp(Audit audit) {
        Audit audit1 = auditMapper.selectAuditById(audit.getId());

        PersonInfo personInfo = new PersonInfo();
        personInfo.setIdCard(audit1.getIdCard());
        personInfo.setCandidateType(audit1.getCandidateType());
        PersonInfo info = personInfoMapper.selectPersonInfoByIdCardAndCandType(personInfo);
        if (!Objects.isNull(info)) {
            //人员表审核状态和审核表状态一致
            info.setAuditStatus(audit.getAuditStatus());
            //如果需要补交资料，需要修改考生的flag字段
            if (audit.getAuditStatus() == 1 || audit.getAuditStatus() == 6 || audit.getAuditStatus() == 7) {
                info.setFlag(0);
            }
            //审核人是企业
            info.setAuditPerson(SecurityUtils.getUsername());
            info.setUpdateBy(SecurityUtils.getUsername());
            info.setUpdateTime(new Date());
            personInfoMapper.updatePersonInfo(info);
        }
        return auditMapper.updateAudit(audit);
    }

    /**
     * 修改审批
     *
     * @param audit 审批
     * @return 结果
     */
    @Override
    public int updateAudit(Audit audit) {
        if (audit.getAuditStatus() == 1 || audit.getAuditStatus() == 6) {//如果被驳回和需要补交资料，需要修改考生的flag字段
            PersonInfo personInfo = new PersonInfo();
            personInfo.setIdCard(sysUserService.selectUserById(Long.valueOf(audit.getSponsor())).getUserName());
            personInfo = personInfoService.selectPersonInfoList(personInfo).get(0);
            personInfo.setFlag(0);
            personInfoService.updatePersonInfo(personInfo);
        }
        return auditMapper.updateAudit(audit);
    }

    /**
     * 批量删除审批
     *
     * @param ids 需要删除的审批主键
     * @return 结果
     */
    @Override
    public int deleteAuditByIds(String[] ids) {
        return auditMapper.deleteAuditByIds(ids);
    }

    /**
     * 删除审批
     * 信息
     *
     * @param id 审批
     *           主键
     * @return 结果
     */
    @Override
    public int deleteAuditById(String id) {
        return auditMapper.deleteAuditById(id);
    }

    /**
     * 根据身份证号获取审核信息
     */
    @Override
    public List<Audit> getByIdCard(String idCard) {
        SysUser sysUser = sysUserService.selectUserByUserName(idCard);
        Audit audit = new Audit();
        if (sysUser != null) {
            audit.setSponsor(sysUser.getUserId().toString());//发起人
        } else {
            return new ArrayList<>();
        }

        List<Audit> list = auditMapper.selectAuditList(audit);
        if (!list.isEmpty()) {
            for (Audit audit1 : list) {
                AuditRecord record = new AuditRecord();
                record.setAuditId(audit1.getId());//查询审核记录
                List<AuditRecord> auditRecordList = auditRecordService.selectAuditRecordList(record);
                audit1.setAuditRecord(auditRecordList);
                if (!auditRecordList.isEmpty()) {
                    audit1.setAuditType(auditRecordList.get(0).getAuditType());
                    audit1.setAuditResult(auditRecordList.get(0).getAuditResult());
                    audit1.setOpinion(auditRecordList.get(0).getOpinion());
                }
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int revokeAudit(String id) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String userName = user.getUserName();
        Audit audit = auditMapper.selectAuditById(id);
        //查询该考生
        PersonInfo personInfo1 = new PersonInfo();
        personInfo1.setIdCard(audit.getIdCard());
        personInfo1.setCandidateType(audit.getCandidateType());
        PersonInfo personInfo = personInfoMapper.selectPersonInfoByIdCardAndCandType(personInfo1);
        // PersonInfo personInfo = personInfoService.getInfoByIdCard(userName);
        audit.setAuditStatus(1);
        int i = auditMapper.updateAudit(audit);
        if (i > 0) {
            Audit audit2 = auditMapper.selectAuditById(id);
            audit2.setOpinion("考生撤回");
            audit2.setAuditResult(1);
            audit2.setAuditStatus(7);
//            String audit1 = companyService.audit(audit2);
            String audit1 = companyService.corpAudit(audit2);

            personDataMapper.updatePersonDataByIdCard(personInfo);
            if (audit1.equals("审核成功")) {
                return 1;
            } else {
                return 0;
            }
        } else {
            return 0;
        }
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public int revokeAudit(String id) {
//        SysUser user = SecurityUtils.getLoginUser().getUser();
//        String userName = user.getUserName();
//        Audit audit = new Audit();
//        audit.setId(id);
//        audit.setAuditStatus(7);
//        int i = auditMapper.updateAudit(audit);
//        if (i>0){
//            Audit audit2 = auditMapper.selectAuditById(id);
//            audit2.setOpinion("考生撤回");
//            audit2.setAuditResult(1);
//            String audit1 = companyService.audit(audit2);
//            personDataMapper.updatePersonDataByIdCard(userName);
//            if (audit1.equals("审核成功")) {
//                return 1;
//            }else {
//                return 0;
//            }
//        }else {
//            return 0;
//        }
//    }

    @Override
    public List<Audit> getByIdCard1(String idCard, Integer candidateType) {
//        SysUser sysUser = sysUserService.selectUserByUserName(idCard);
        PersonInfo personInfo = new PersonInfo();
        personInfo.setIdCard(idCard);
        personInfo.setCandidateType(candidateType);

        PersonInfo info = personInfoMapper.selectPersonInfoByIdCardAndCandType(personInfo);
        Audit audit = new Audit();
//        if (sysUser != null) {
////            audit.setSponsor(sysUser.getUserId().toString());//发起人
//            audit.setIdCard(sysUser.getUserName());
//        }else {
//            return new ArrayList<Audit>();
//        }
        audit.setIdCard(idCard);
        audit.setCandidateType(candidateType);
        // 筛选出大于本批次考生导入时间的审核记录
        audit.setTime(info.getCreateTime());
        List<Audit> list = auditMapper.selectAuditList(audit);
        if (!list.isEmpty()) {
            for (Audit audit1 : list) {
                audit1.setNumber("第" + audit1.getAuditNumber() + "次申请");
                AuditRecord record = new AuditRecord();
                record.setAuditId(audit1.getId());//查询审核记录
                record.setCandidateType(candidateType);
                if (SecurityUtils.getLoginUser().getUser().getUserType().equals("02")||SecurityUtils.getLoginUser().getUser().getUserType().equals("03")) {
                    record.setIsSee("否");
                }
                List<AuditRecord> auditRecordList = auditRecordService.selectAuditRecordList(record);
                for (AuditRecord auditRecord : auditRecordList) {

                    auditRecord.setIsSame(false);
                    if (!StringUtils.isEmpty(auditRecord.getCorpName()) && !StringUtils.isEmpty(info.getOldWork())) {
                        if (auditRecord.getCorpName().trim().equals(info.getOldWork().trim())) {
                            auditRecord.setIsSame(true);
                        }

                    }
                }
                audit1.setAuditRecord(auditRecordList);
                if (!auditRecordList.isEmpty()) {
                    audit1.setAuditType(auditRecordList.get(0).getAuditType());
                    audit1.setAuditResult(auditRecordList.get(0).getAuditResult());
                    audit1.setOpinion(auditRecordList.get(0).getOpinion());
                    audit1.setCorpName(auditRecordList.get(0).getCorpName());
                }
            }
        }
        return list;
    }


}
