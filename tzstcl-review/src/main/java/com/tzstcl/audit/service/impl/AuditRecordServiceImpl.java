package com.tzstcl.audit.service.impl;

import com.tzstcl.audit.domain.AuditRecord;
import com.tzstcl.audit.mapper.AuditRecordMapper;
import com.tzstcl.audit.service.IAuditRecordService;
import com.tzstcl.common.core.domain.entity.SysRole;
import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.common.utils.DateUtils;
import com.tzstcl.common.utils.SecurityUtils;
import com.tzstcl.common.utils.SnowflakeIdWorker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 审核记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-23
 */
@Service
public class AuditRecordServiceImpl implements IAuditRecordService
{
    @Autowired
    private AuditRecordMapper auditRecordMapper;



    /**
     * 查询审核记录
     *
     * @param id 审核记录主键
     * @return 审核记录
     */
    @Override
    public AuditRecord selectAuditRecordById(String id)
    {
        return auditRecordMapper.selectAuditRecordById(id);
    }

    /**
     * 查询审核记录列表
     *
     * @param auditRecord 审核记录
     * @return 审核记录
     */
    @Override
    public List<AuditRecord> selectAuditRecordList(AuditRecord auditRecord)
    {
        return auditRecordMapper.selectAuditRecordList(auditRecord);
    }

    /**
     * 新增审核记录
     *
     * @param auditRecord 审核记录
     * @return 结果
     */
    @Override
    public int insertAuditRecord(AuditRecord auditRecord)
    {
        auditRecord.setId(String.valueOf(SnowflakeIdWorker.getInstance().nextId()));
        auditRecord.setAuditPerson(SecurityUtils.getUserId().toString());
//        auditRecord.setAuditPerson(SecurityUtils.getUsername());
        auditRecord.setTime(DateUtils.getNowDate());
        String userType = SecurityUtils.getLoginUser().getUser().getUserType();
        if ("02".equals(userType)){
            auditRecord.setAuditType(0);
        }else {
            auditRecord.setAuditType(1);
        }
//        SysUser user = SecurityUtils.getLoginUser().getUser();
//        List<String> userRoles = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
//        int auditType =0;
//        if(userRoles.contains("dept")||userRoles.contains("city")||userRoles.contains("dist")){
//            auditType = 1;
//        }
//        auditRecord.setAuditType(auditType);
        return auditRecordMapper.insertAuditRecord(auditRecord);
    }

    /**
     * 修改审核记录
     *
     * @param auditRecord 审核记录
     * @return 结果
     */
    @Override
    public int updateAuditRecord(AuditRecord auditRecord)
    {
        return auditRecordMapper.updateAuditRecord(auditRecord);
    }

    /**
     * 批量删除审核记录
     *
     * @param ids 需要删除的审核记录主键
     * @return 结果
     */
    @Override
    public int deleteAuditRecordByIds(String[] ids)
    {
        return auditRecordMapper.deleteAuditRecordByIds(ids);
    }

    /**
     * 删除审核记录信息
     *
     * @param id 审核记录主键
     * @return 结果
     */
    @Override
    public int deleteAuditRecordById(String id)
    {
        return auditRecordMapper.deleteAuditRecordById(id);
    }
}
