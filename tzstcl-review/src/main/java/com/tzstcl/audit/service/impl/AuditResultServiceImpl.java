
package com.tzstcl.audit.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.tzstcl.audit.domain.AuditResult;
import com.tzstcl.audit.mapper.AuditResultMapper;
import com.tzstcl.audit.service.IAuditResultService;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.core.domain.entity.SysDept;
import com.tzstcl.common.utils.DateUtils;
import com.tzstcl.common.utils.SecurityUtils;
import com.tzstcl.system.service.ISysDeptService;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 人员审核全部完成后提交的审核结果Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
@Service
public class AuditResultServiceImpl implements IAuditResultService {
    @Autowired
    private AuditResultMapper auditResultMapper;
    @Autowired
    private ISysDeptService sysDeptService;

    /**
     * 查询人员审核全部完成后提交的审核结果
     *
     * @param id 人员审核全部完成后提交的审核结果主键
     * @return 人员审核全部完成后提交的审核结果
     */
    @Override
    public AuditResult selectAuditResultById(Long id) {
        return auditResultMapper.selectAuditResultById(id);
    }

    /**
     * 查询人员审核全部完成后提交的审核结果列表
     *
     * @param auditResult 人员审核全部完成后提交的审核结果
     * @return 人员审核全部完成后提交的审核结果
     */
    @Override
    public List<AuditResult> selectAuditResultList(AuditResult auditResult) {
        SysDept sysDept = sysDeptService.selectDeptById(SecurityUtils.getDeptId());
        if (sysDept != null) {
            if (sysDept.getDeptId() == 410000L || sysDept.getDeptId() == 410005L) {
                return auditResultMapper.selectAuditResultList(auditResult);
            }
            auditResult.setAuditPerson(sysDept.getRemark() + sysDept.getDeptName());
            return auditResultMapper.selectAuditResultList(auditResult);
        } else if (SecurityUtils.getLoginUser().getUserId() == 14141313) {
            return auditResultMapper.selectAuditResultList(auditResult);

        }
        return new ArrayList<>();
    }

    /**
     * 新增人员审核全部完成后提交的审核结果
     *
     * @param auditResult 人员审核全部完成后提交的审核结果
     * @return 结果
     */
    @Override
    public AjaxResult insertAuditResult(AuditResult auditResult) {
        auditResult.setCreateTime(DateUtils.getNowDate());
        SysDept sysDept = sysDeptService.selectDeptById(SecurityUtils.getDeptId());
        if (!sysDept.getDeptName().equals("总账号")) {
            return AjaxResult.error("请使用市核查部门的总账号上传审核结果！");
        }
        auditResult.setAuditPerson(sysDept.getRemark() + sysDept.getDeptName());
        auditResultMapper.insertAuditResult(auditResult);
        return AjaxResult.success();
    }

    /**
     * 修改人员审核全部完成后提交的审核结果
     *
     * @param auditResult 人员审核全部完成后提交的审核结果
     * @return 结果
     */
    @Override
    public int updateAuditResult(AuditResult auditResult) {
        return auditResultMapper.updateAuditResult(auditResult);
    }

    /**
     * 批量删除人员审核全部完成后提交的审核结果
     *
     * @param ids 需要删除的人员审核全部完成后提交的审核结果主键
     * @return 结果
     */
    @Override
    public int deleteAuditResultByIds(Long[] ids) {
        return auditResultMapper.deleteAuditResultByIds(ids);
    }

    /**
     * 删除人员审核全部完成后提交的审核结果信息
     *
     * @param id 人员审核全部完成后提交的审核结果主键
     * @return 结果
     */
    @Override
    public int deleteAuditResultById(Long id) {
        return auditResultMapper.deleteAuditResultById(id);
    }
}
