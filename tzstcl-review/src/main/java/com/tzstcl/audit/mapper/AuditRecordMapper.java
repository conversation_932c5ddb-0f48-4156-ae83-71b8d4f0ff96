package com.tzstcl.audit.mapper;

import java.util.List;
import com.tzstcl.audit.domain.AuditRecord;
import org.apache.ibatis.annotations.Mapper;

/**
 * 审核记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-11-23
 */
@Mapper
public interface AuditRecordMapper
{
    /**
     * 查询审核记录
     *
     * @param id 审核记录主键
     * @return 审核记录
     */
    public AuditRecord selectAuditRecordById(String id);

    /**
     * 查询审核记录列表
     *
     * @param auditRecord 审核记录
     * @return 审核记录集合
     */
    public List<AuditRecord> selectAuditRecordList(AuditRecord auditRecord);

    /**
     * 新增审核记录
     *
     * @param auditRecord 审核记录
     * @return 结果
     */
    public int insertAuditRecord(AuditRecord auditRecord);

    /**
     * 修改审核记录
     *
     * @param auditRecord 审核记录
     * @return 结果
     */
    public int updateAuditRecord(AuditRecord auditRecord);

    /**
     * 删除审核记录
     *
     * @param id 审核记录主键
     * @return 结果
     */
    public int deleteAuditRecordById(String id);

    /**
     * 批量删除审核记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAuditRecordByIds(String[] ids);

    List<AuditRecord> selectAuditRecordListByauditId(String auditId);

    /**
     * 根据审核id查询最新一条审核记录
     * @param auditId 审核id
     * @return 结果
     */
    AuditRecord selectAuditRecordByAuditId(String auditId);
}
