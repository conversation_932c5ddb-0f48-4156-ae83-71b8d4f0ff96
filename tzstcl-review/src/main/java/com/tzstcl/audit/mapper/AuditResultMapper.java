package com.tzstcl.audit.mapper;

import com.tzstcl.audit.domain.AuditResult;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 人员审核全部完成后提交的审核结果Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
@Mapper
public interface AuditResultMapper {
    /**
     * 查询人员审核全部完成后提交的审核结果
     *
     * @param id 人员审核全部完成后提交的审核结果主键
     * @return 人员审核全部完成后提交的审核结果
     */
    public AuditResult selectAuditResultById(Long id);

    /**
     * 查询人员审核全部完成后提交的审核结果列表
     *
     * @param auditResult 人员审核全部完成后提交的审核结果
     * @return 人员审核全部完成后提交的审核结果集合
     */
    public List<AuditResult> selectAuditResultList(AuditResult auditResult);

    /**
     * 新增人员审核全部完成后提交的审核结果
     *
     * @param auditResult 人员审核全部完成后提交的审核结果
     * @return 结果
     */
    public int insertAuditResult(AuditResult auditResult);

    /**
     * 修改人员审核全部完成后提交的审核结果
     *
     * @param auditResult 人员审核全部完成后提交的审核结果
     * @return 结果
     */
    public int updateAuditResult(AuditResult auditResult);

    /**
     * 删除人员审核全部完成后提交的审核结果
     *
     * @param id 人员审核全部完成后提交的审核结果主键
     * @return 结果
     */
    public int deleteAuditResultById(Long id);

    /**
     * 批量删除人员审核全部完成后提交的审核结果
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAuditResultByIds(Long[] ids);
}
