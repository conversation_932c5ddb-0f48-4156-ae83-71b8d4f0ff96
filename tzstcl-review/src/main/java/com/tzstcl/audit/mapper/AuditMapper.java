package com.tzstcl.audit.mapper;

import java.util.List;
import com.tzstcl.audit.domain.Audit;
import com.tzstcl.person.domain.AuditPersonVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 审批
Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-11-23
 */
@Mapper
public interface AuditMapper 
{
    /**
     * 查询审批

     * 
     * @param id 审批
主键
     * @return 审批

     */
    public Audit selectAuditById(String id);

    /**
     * 查询审批
列表
     * 
     * @param audit 审批

     * @return 审批
集合
     */
    public List<Audit> selectAuditList(Audit audit);

    /**
     * 新增审批

     * 
     * @param audit 审批

     * @return 结果
     */
    public int insertAudit(Audit audit);

    /**
     * 修改审批

     * 
     * @param audit 审批

     * @return 结果
     */
    public int updateAudit(Audit audit);

    /**
     * 删除审批

     * 
     * @param id 审批
主键
     * @return 结果
     */
    public int deleteAuditById(String id);

    /**
     * 批量删除审批

     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAuditByIds(String[] ids);

//    public List<Audit> selectAuditListByIdCardandType(String id_card,Integer candidate_type);


    Audit getOneAudit(@Param("idCard") String idCard, @Param("candidateType") Integer candidateType);


    List<AuditPersonVo> selectAuditListByAuditPerson();

    Integer selectAuditListByIdCard(Audit audit);

    List<String> selectAuditListBySponson();

    int updateAuditBysponsor(@Param("sponsor") String sponsor,@Param("idCard") String idCard);
}
