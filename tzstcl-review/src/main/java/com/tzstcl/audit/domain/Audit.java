package com.tzstcl.audit.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzstcl.common.annotation.Excel;
import com.tzstcl.common.core.domain.BaseEntity;

/**
 * 审批
 * 对象 audit
 *
 * <AUTHOR>
 * @date 2022-11-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Audit extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * 发起人id
     */
    @Excel(name = "发起人id")
    private String sponsor;

    /**
     * 发起时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发起时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date time;

    /**
     * 处理人id
     */
    @Excel(name = "处理人id")
    private String hanlder;

    /**
     * 次数
     */
    @Excel(name = "次数")
    private String number;

    /**
     * 状态(0待企业审核 1企业驳回 2待主管部门审核 3主管部门通过 4主管部门驳回）
     */
    @Excel(name = "状态(0待企业审核 1企业驳回 2待主管部门审核 3主管部门通过 4主管部门驳回）")
    private Integer auditStatus;

    /**
     * 审核结果
     */
    private Integer auditResult;

    /**
     * 审核类型
     */
    private Integer auditType;

    /**
     * 审核意见
     */
    private String opinion;

    private String examineeId;

    /**
     * 报考资质类别
     */
    private Integer candidateType;

    /**
     * 考生所属企业
     */
    private String corpName;

    private String idCard;
    private Integer auditNumber;

    /**
     * 审核记录
     */
    private List<AuditRecord> auditRecord;

    /**
     * 审核附件
     */
    private String cityFile;

    private String isSee;
}
