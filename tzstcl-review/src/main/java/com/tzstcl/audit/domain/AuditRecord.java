package com.tzstcl.audit.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzstcl.common.annotation.Excel;
import com.tzstcl.common.core.domain.BaseEntity;

/**
 * 审核记录对象 audit_record
 *
 * <AUTHOR>
 * @date 2022-11-23
 */
public class AuditRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  id */
    private String id;

    /** 审核id */
    @Excel(name = "审核id")
    private String auditId;

    /** 审核人id */
    @Excel(name = "审核人id")
    private String auditPerson;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date time;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String opinion;

    /** 审核结果(0合格 1不合格) */
    @Excel(name = "审核结果(0合格 1不合格)")
    private Integer auditResult;

    /** 审核类型（0企业审核 1主管部门审核） */
    @Excel(name = "审核类型", readConverterExp = "0=企业审核,1=主管部门审核")
    private Integer auditType;

    /**报考资质类别*/
    @Excel(name = "报考资质类别")
    private Integer candidateType;
    @Excel(name = "考生所属企业")
    private String corpName;
     /** 地市上传审核结果材料 */
    private String cityFile;
    /** 报名企业和填报企业是否相同 */
    private Boolean isSame;


    private String isSee;

    public String getIsSee() {
        return isSee;
    }

    public void setIsSee(String isSee) {
        this.isSee = isSee;
    }

    public Boolean getIsSame() {
        return isSame;
    }

    public void setIsSame(Boolean same) {
        isSame = same;
    }

    public String getCityFile() {
        return cityFile;
    }

    public void setCityFile(String cityFile) {
        this.cityFile = cityFile;
    }

    public String getCorpName() {
        return corpName;
    }

    public void setCorpName(String corpName) {
        this.corpName = corpName;
    }

    public Integer getCandidateType() {
        return candidateType;
    }

    public void setCandidateType(Integer candidateType) {
        this.candidateType = candidateType;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setAuditId(String auditId) {
        this.auditId = auditId;
    }

    public String getAuditId() {
        return auditId;
    }

    public void setAuditPerson(String auditPerson) {
        this.auditPerson = auditPerson;
    }

    public String getAuditPerson() {
        return auditPerson;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Date getTime() {
        return time;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setAuditResult(Integer auditResult) {
        this.auditResult = auditResult;
    }

    public Integer getAuditResult() {
        return auditResult;
    }

    public void setAuditType(Integer auditType) {
        this.auditType = auditType;
    }

    public Integer getAuditType() {
        return auditType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("auditId", getAuditId())
                .append("auditPerson", getAuditPerson())
                .append("time", getTime())
                .append("opinion", getOpinion())
                .append("auditResult", getAuditResult())
                .append("auditType", getAuditType())
                .toString();
    }
}
