package com.tzstcl.audit.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzstcl.common.annotation.Excel;
import com.tzstcl.common.core.domain.BaseEntity;

/**
 * 人员审核全部完成后提交的审核结果对象 audit_result
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
public class AuditResult extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 附件
     */
    @Excel(name = "附件")
    private String cityFile;

    /**
     * 审核部门-主管部门为行政区域代码
     */
    @Excel(name = "审核部门-主管部门为行政区域代码")
    private String auditPerson;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setCityFile(String cityFile) {
        this.cityFile = cityFile;
    }

    public String getCityFile() {
        return cityFile;
    }

    public void setAuditPerson(String auditPerson) {
        this.auditPerson = auditPerson;
    }

    public String getAuditPerson() {
        return auditPerson;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("createTime", getCreateTime())
                .append("cityFile", getCityFile())
                .append("auditPerson", getAuditPerson())
                .toString();
    }
}
