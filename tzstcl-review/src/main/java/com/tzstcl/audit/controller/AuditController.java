package com.tzstcl.audit.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.tzstcl.common.annotation.Log;
import com.tzstcl.common.core.controller.BaseController;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.enums.BusinessType;
import com.tzstcl.audit.domain.Audit;
import com.tzstcl.audit.service.IAuditService;
import com.tzstcl.common.utils.poi.ExcelUtil;
import com.tzstcl.common.core.page.TableDataInfo;

/**
 * 审批
 * Controller
 *
 * <AUTHOR>
 * @date 2022-11-23
 */
@RestController
@RequestMapping("/audit/info")
public class AuditController extends BaseController {
    @Autowired
    private IAuditService auditService;

    /**
     * 查询审批列表
     */
    @PreAuthorize("@ss.hasPermi('audit:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(Audit audit) {
        startPage();
        List<Audit> list = auditService.selectAuditList(audit);
        return getDataTable(list);
    }

    /**
     * 导出审批列表
     */
  /*  @PreAuthorize("@ss.hasPermi('audit:info:export')")
    @Log(title = "审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Audit audit) {
        List<Audit> list = auditService.selectAuditList(audit);
        ExcelUtil<Audit> util = new ExcelUtil<Audit>(Audit.class);
        util.exportExcel(response, list, "审批 数据");
    }*/

    /**
     * 获取审批详细信息
     */
    @PreAuthorize("@ss.hasPermi('audit:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(auditService.selectAuditById(id));
    }

    /**
     * 新增审批
     */
    @PreAuthorize("@ss.hasPermi('audit:info:add')")
    @Log(title = "审批", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(Audit audit) {
        return toAjax(auditService.insertAudit(audit));
    }

    /**
     * 修改审批
     */
    @PreAuthorize("@ss.hasPermi('audit:info:edit')")
    @Log(title = "审批", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Audit audit) {
        return toAjax(auditService.updateAudit(audit));
    }

    /**
     * 删除审批
     */
    @PreAuthorize("@ss.hasPermi('audit:info:remove')")
    @Log(title = "审批", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(auditService.deleteAuditByIds(ids));
    }


//    /**
////     * 根据身份证号获取审核信息
////     */
////    @GetMapping("/getByIdCard")
////    public AjaxResult getByIdCard(String IdCard){
////        return AjaxResult.success(auditService.getByIdCard(IdCard));
////    }

    /**
     * 根据身份证号获取审核信息
     */
    @GetMapping("/getByIdCard")
    public AjaxResult getByIdCard(Integer candidateType) {
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        /*if (sysUser.getUserType().equals("03")) {
            if (!sysUser.getUserName().equals(IdCard)) {
                return AjaxResult.success("禁止查询非本人的审核信息！");
            }
        }
        if (sysUser.getUserType().equals("02")) {

        }*/
        AjaxResult success = AjaxResult.success(auditService.getByIdCard1(sysUser.getUserName(), candidateType));
        return success;
    }

    /**
     * 根据审核状态撤回审核
     */
    @PutMapping("/revokeAudit")
    @Log(title = "根据审核状态撤回审核", businessType = BusinessType.UPDATE)
    public AjaxResult revokeAudit(String id) {
        return toAjax(auditService.revokeAudit(id));
    }
}
