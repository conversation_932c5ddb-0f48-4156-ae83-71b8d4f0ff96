package com.tzstcl.audit.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tzstcl.audit.domain.AuditResult;
import com.tzstcl.audit.service.IAuditResultService;
import com.tzstcl.uploadFile.util.MinioUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzstcl.common.annotation.Log;
import com.tzstcl.common.core.controller.BaseController;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.enums.BusinessType;

import com.tzstcl.common.utils.poi.ExcelUtil;
import com.tzstcl.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 人员审核全部完成后提交的审核结果Controller
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
@RestController
@RequestMapping("/audit/result")
public class AuditResultController extends BaseController {
    @Autowired
    private IAuditResultService auditResultService;
    @Autowired
    private MinioUtils minioUtils;

    /**
     * 查询人员审核全部完成后提交的审核结果列表
     */
    //@PreAuthorize("@ss.hasPermi('system:result:list')")
    @GetMapping("/list")
    public TableDataInfo list(AuditResult auditResult) {
        startPage();
        List<AuditResult> list = auditResultService.selectAuditResultList(auditResult);
        return getDataTable(list);
    }

    /**
     * 导出人员审核全部完成后提交的审核结果列表
     */
    // @PreAuthorize("@ss.hasPermi('system:result:export')")
    /*@Log(title = "人员审核全部完成后提交的审核结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AuditResult auditResult) {
        List<AuditResult> list = auditResultService.selectAuditResultList(auditResult);
        ExcelUtil<AuditResult> util = new ExcelUtil<AuditResult>(AuditResult.class);
        util.exportExcel(response, list, "人员审核全部完成后提交的审核结果数据");
    }*/

    /**
     * 获取人员审核全部完成后提交的审核结果详细信息
     */
    // @PreAuthorize("@ss.hasPermi('system:result:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(auditResultService.selectAuditResultById(id));
    }

    /**
     * 新增人员审核全部完成后提交的审核结果
     */
    //  @PreAuthorize("@ss.hasPermi('system:result:add')")
    @Log(title = "人员审核全部完成后提交的审核结果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(MultipartFile file) {
        //允许上传的文件格式
        String[] extensions = {"pdf", "doc", "docx"};
        int flag = 0;
        for (String extension : extensions) {
            if (extension.equals(MinioUtils.getExtension(file))) {
                flag = 1;
            }
        }
        if (flag == 0) {
            return AjaxResult.error("只允许上传pdf或doc文件");
        }
        AuditResult auditResult = new AuditResult();
        if (file != null) {
            auditResult.setCityFile(minioUtils.uploadFile(file));
        }
        return auditResultService.insertAuditResult(auditResult);
    }

    /**
     * 修改人员审核全部完成后提交的审核结果
     */
    // @PreAuthorize("@ss.hasPermi('system:result:edit')")
    @Log(title = "人员审核全部完成后提交的审核结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AuditResult auditResult) {
        return toAjax(auditResultService.updateAuditResult(auditResult));
    }

    /**
     * 删除人员审核全部完成后提交的审核结果
     */
    //  @PreAuthorize("@ss.hasPermi('system:result:remove')")
    @Log(title = "人员审核全部完成后提交的审核结果", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(auditResultService.deleteAuditResultByIds(ids));
    }
}
