package com.tzstcl.audit.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzstcl.common.annotation.Log;
import com.tzstcl.common.core.controller.BaseController;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.enums.BusinessType;
import com.tzstcl.audit.domain.AuditRecord;
import com.tzstcl.audit.service.IAuditRecordService;
import com.tzstcl.common.utils.poi.ExcelUtil;
import com.tzstcl.common.core.page.TableDataInfo;

/**
 * 审核记录Controller
 * 
 * <AUTHOR>
 * @date 2022-11-23
 */
@RestController
@RequestMapping("/audit/record")
public class AuditRecordController extends BaseController
{
    @Autowired
    private IAuditRecordService auditRecordService;

    /**
     * 查询审核记录列表
     */
    @PreAuthorize("@ss.hasPermi('audit:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(AuditRecord auditRecord)
    {
        startPage();
        List<AuditRecord> list = auditRecordService.selectAuditRecordList(auditRecord);
        return getDataTable(list);
    }

    /**
     * 导出审核记录列表
     */
    /*@PreAuthorize("@ss.hasPermi('audit:record:export')")
    @Log(title = "审核记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AuditRecord auditRecord)
    {
        List<AuditRecord> list = auditRecordService.selectAuditRecordList(auditRecord);
        ExcelUtil<AuditRecord> util = new ExcelUtil<AuditRecord>(AuditRecord.class);
        util.exportExcel(response, list, "审核记录数据");
    }*/

    /**
     * 获取审核记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('audit:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(auditRecordService.selectAuditRecordById(id));
    }

    /**
     * 新增审核记录
     */
    @PreAuthorize("@ss.hasPermi('audit:record:add')")
    @Log(title = "审核记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AuditRecord auditRecord)
    {
        return toAjax(auditRecordService.insertAuditRecord(auditRecord));
    }

    /**
     * 修改审核记录
     */
    @PreAuthorize("@ss.hasPermi('audit:record:edit')")
    @Log(title = "审核记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AuditRecord auditRecord)
    {
        return toAjax(auditRecordService.updateAuditRecord(auditRecord));
    }

    /**
     * 删除审核记录
     */
    @PreAuthorize("@ss.hasPermi('audit:record:remove')")
    @Log(title = "审核记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(auditRecordService.deleteAuditRecordByIds(ids));
    }
}
