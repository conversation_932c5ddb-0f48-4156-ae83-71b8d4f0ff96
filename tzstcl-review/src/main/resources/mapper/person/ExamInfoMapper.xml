<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.person.mapper.ExamInfoMapper">
    
    <resultMap type="com.tzstcl.person.domain.ExamInfo" id="ExamInfoResult">
        <result property="id"    column="id"    />
        <result property="idCard"    column="id_card"    />
        <result property="examSubject"    column="exam_subject"    />
        <result property="examCode"    column="exam_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="candidateType"    column="candidate_type"    />
    </resultMap>

    <sql id="selectExamInfoVo">
        select id, id_card, exam_subject, exam_code, create_by, create_time, update_by, update_time, del_flag,candidate_type from exam_info
    </sql>

    <select id="selectExamInfoList" parameterType="com.tzstcl.person.domain.ExamInfo" resultMap="ExamInfoResult">
        <include refid="selectExamInfoVo"/>
        <where>
            del_flag =0
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="examSubject != null  and examSubject != ''"> and exam_subject = #{examSubject}</if>
            <if test="examCode != null  and examCode != ''"> and exam_code = #{examCode}</if>
            <if test="candidateType != null  and candidateType != ''"> and candidate_type = #{candidateType}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectExamInfoById" parameterType="String" resultMap="ExamInfoResult">
        <include refid="selectExamInfoVo"/>
        where id = #{id} and del_flag=0
    </select>

    <select id="selectExamInfoList1" resultMap="ExamInfoResult">
        <include refid="selectExamInfoVo"/>
        <where>
            del_flag =0
            and id_card = #{idCard}
            and candidate_type = #{candidateType}
        </where>
        order by create_time desc
    </select>

    <insert id="insertExamInfo" parameterType="com.tzstcl.person.domain.ExamInfo">
        insert into exam_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="examSubject != null and examSubject != ''">exam_subject,</if>
            <if test="examCode != null and examCode != ''">exam_code,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="candidateType != null">candidate_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="examSubject != null and examSubject != ''">#{examSubject},</if>
            <if test="examCode != null and examCode != ''">#{examCode},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="candidateType != null">#{candidateType},</if>
         </trim>
    </insert>

    <update id="updateExamInfo" parameterType="com.tzstcl.person.domain.ExamInfo">
        update exam_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="examSubject != null and examSubject != ''">exam_subject = #{examSubject},</if>
            <if test="examCode != null and examCode != ''">exam_code = #{examCode},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="candidateType != null">candidate_type = #{candidateType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExamInfoById" parameterType="String">
        update exam_info set del_flag =1 where id = #{id}
    </delete>

    <delete id="deleteExamInfoByIds" parameterType="String">
        update exam_info set del_flag =1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>