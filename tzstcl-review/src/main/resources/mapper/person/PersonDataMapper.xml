<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.person.mapper.PersonDataMapper">

    <resultMap type="com.tzstcl.person.domain.PersonData" id="PersonDataResult">
        <result property="id"    column="id"    />
        <result property="idCard"    column="id_card"    />
        <result property="type"    column="type"    />
        <result property="path"    column="path"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="candidateType"    column="candidate_type"    />
    </resultMap>

    <sql id="selectPersonDataVo">
        select id, id_card, type, path, create_by, create_time, update_by, update_time, del_flag,candidate_type from person_data
    </sql>

    <select id="selectPersonDataList" parameterType="com.tzstcl.person.domain.PersonData" resultMap="PersonDataResult">
        <include refid="selectPersonDataVo"/>
        <where>
            del_flag =0
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="path != null  and path != ''"> and path = #{path}</if>
            <if test="candidateType != null "> and candidate_type = #{candidateType}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectPersonDataById" parameterType="String" resultMap="PersonDataResult">
        <include refid="selectPersonDataVo"/>
        where id = #{id} and del_flag =0
    </select>
    <select id="getByIdCard" resultMap="PersonDataResult">
        <include refid="selectPersonDataVo"/>
        where  id_card = #{IdCard} and del_flag = 0
    </select>
    <select id="selectPersonDataByPath" resultMap="PersonDataResult">
        <include refid="selectPersonDataVo"/>
        where  path = #{url} and del_flag =0
    </select>
    <select id="selectPersonDataByType" resultMap="PersonDataResult">
        <include refid="selectPersonDataVo"/>
        where  del_flag =0
        <if test="idCard != null and idCard != ''">
           and id_card = #{idCard}
        </if>>
        <if test="type != null and type != ''">
            and type = #{type}
        </if>
        <if test="candidateType != null">
            and candidate_type = #{candidateType}
        </if>
    </select>

    <insert id="insertPersonData" parameterType="com.tzstcl.person.domain.PersonData">
        insert into person_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="path != null and path != ''">path,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="candidateType != null">candidate_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="path != null and path != ''">#{path},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="candidateType != null">#{candidateType},</if>
         </trim>
    </insert>

    <update id="updatePersonData" parameterType="com.tzstcl.person.domain.PersonData">
        update person_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="path != null and path != ''">path = #{path},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="candidateType != null">candidate_type = #{candidateType},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updatePersonDataByIdCard">
        UPDATE person_data
        SET del_flag = 1
        WHERE
            id_card = #{idCard} and (candidate_type = #{candidateType} or candidate_type is null)
          AND del_flag =0

    </update>

    <delete id="deletePersonDataById" parameterType="String">
        update person_data set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deletePersonDataByIds" parameterType="String">
        update person_data set del_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
