<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.person.mapper.PersonInfoMapper">

    <resultMap type="com.tzstcl.person.domain.PersonInfo" id="PersonInfoResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="sex"    column="sex"    />
        <result property="nationality"    column="nationality"    />
        <result property="nation"    column="nation"    />
        <result property="idCard"    column="id_card"    />
        <result property="politicalOutlook"    column="political_outlook"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="school"    column="school"    />
        <result property="education"    column="education"    />
        <result property="specializedSubject"    column="specialized_subject"    />
        <result property="graduationTime"    column="graduation_time"    />
        <result property="workTime"    column="work_time"    />
        <result property="professionalWorkTime"    column="professional_work_time"    />
        <result property="professionalTitle"    column="professional_title"    />
        <result property="professionalPosition"    column="professional_position"    />
        <result property="professionalTitleTime" column="professional_title_time"/>
        <result property="professionalPositionTime"    column="professional_position_time"    />
        <result property="address"    column="address"    />
        <result property="work"    column="work"    />
        <result property="workNature"    column="work_nature"    />
        <result property="photo"    column="photo"    />
        <result property="addressExam"    column="address_exam"    />
        <result property="addressCode"    column="address_code"    />
        <result property="cityName"    column="city_name"    />
        <result property="bmSerial"    column="bm_serial"    />
        <result property="jbCode"    column="jb_code"    />
        <result property="typeCode"    column="type_code"    />
        <result property="type"    column="type"    />
        <result property="professional"    column="professional"    />
        <result property="archivesNo"    column="archives_no"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="examSubject"    column="exam_subject"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="qualified"    column="qualified"    />
        <result property="flag"    column="flag"    />
        <result property="corpCode" column="corp_code"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="candidateType" column="candidate_type"/>
        <result property="isoldexaminee" column="isoldexaminee"/>
        <result property="hand" column="hand"/>
        <result property="inputType" column="inputType"/>
        <result property="auditPerson" column="auditPerson"/>
        <result property="auditResult" column="auditResult"/>
        <result property="deptId" column="dept_id"/>
        <result property="isAutomaticCheck" column="isAutomaticCheck"/>
        <result property="registerStatus" column="registerStatus"/>
        <result property="oldWork" column="old_work"/>
    </resultMap>


    <resultMap type="com.tzstcl.person.domain.PersonInfo" id="PersonInfoResult1">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="sex"    column="sex"    />
        <result property="nationality"    column="nationality"    />
        <result property="nation"    column="nation"    />
        <result property="idCard"    column="id_card"    />
        <result property="politicalOutlook"    column="political_outlook"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="school"    column="school"    />
        <result property="education"    column="education"    />
        <result property="specializedSubject"    column="specialized_subject"    />
        <result property="graduationTime"    column="graduation_time"    />
        <result property="workTime"    column="work_time"    />
        <result property="professionalWorkTime"    column="professional_work_time"    />
        <result property="professionalTitle"    column="professional_title"    />
        <result property="professionalPosition"    column="professional_position"    />
        <result property="professionalTitleTime" column="professional_title_time"/>
        <result property="professionalPositionTime"    column="professional_position_time"    />
        <result property="address"    column="address"    />
        <result property="work"    column="work"    />
        <result property="workNature"    column="work_nature"    />
        <result property="photo"    column="photo"    />
        <result property="addressExam"    column="address_exam"    />
        <result property="addressCode"    column="address_code"    />
        <result property="cityName"    column="city_name"    />
        <result property="bmSerial"    column="bm_serial"    />
        <result property="jbCode"    column="jb_code"    />
        <result property="typeCode"    column="type_code"    />
        <result property="type"    column="type"    />
        <result property="professional"    column="professional"    />
        <result property="archivesNo"    column="archives_no"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="examSubject"    column="exam_subject"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="qualified"    column="qualified"    />
        <result property="flag"    column="flag"    />
        <result property="corpCode" column="corp_code"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="candidateType" column="candidate_type"/>
        <result property="isoldexaminee" column="isoldexaminee"/>
        <result property="oldWork" column="old_work"/>
        <collection property="examInfoList" javaType="java.util.List"
                    ofType="com.tzstcl.person.domain.ExamInfo"
                    select="queryexamInfo" column="{id_card=id_card,candidate_type=candidate_type}">
        </collection>
        <collection property="personDataList" javaType="java.util.List"
                    ofType="com.tzstcl.person.domain.PersonData"
                    select="querypersonDataList" column="id_card=id_card">
        </collection>
<!--        <collection property="auditList" javaType="java.util.List"-->
<!--                    ofType="com.tzstcl.audit.domain.Audit"-->
<!--                    select="queryauditList" column="{id_card=id_card,candidate_type=candidate_type}">-->
<!--        </collection>-->
<!--        <collection property="examInfoList" column="{id_card=idCard,candidate_type=candidateType}" select="com.tzstcl.person.mapper.ExamInfoMapper.selectExamInfoList1"/>-->
<!--        <collection property="personDataList" column="id_card=idCard" select="com.tzstcl.person.mapper.PersonDataMapper.getByIdCard"/>-->
<!--        <collection property="auditList" column="{id_card=id_card,candidate_type=candidate_type}" select="com.tzstcl.audit.mapper.AuditMapper.selectAuditList"/>-->
    </resultMap>


    <resultMap type="com.tzstcl.person.domain.ExamInfo" id="ExamInfoResult">
        <result property="id"    column="id"    />
        <result property="idCard"    column="id_card"    />
        <result property="examSubject"    column="exam_subject"    />
        <result property="examCode"    column="exam_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="candidateType"    column="candidate_type"    />
    </resultMap>

    <sql id="selectPersonInfoVo">
        select id, name, sex, nationality, nation, id_card, political_outlook, phone_number, school, education, specialized_subject, graduation_time, work_time, professional_work_time, professional_title, professional_position , professional_title_time, professional_position_time, address, work, work_nature, corp_code, photo, address_exam, address_code, city_name, bm_serial, jb_code, type_code, type, professional, archives_no, create_by, create_time, exam_subject, update_by, update_time, del_flag, qualified, flag,audit_status,candidate_type,isoldexaminee,hand,inputType,auditPerson,auditResult,dept_id,isAutomaticCheck,registerStatus from person_info
    </sql>
    <sql id="selectPersonInfoVo1">
        select id, name, sex, nationality, nation, id_card, political_outlook, phone_number, school, education, specialized_subject, graduation_time, work_time, professional_work_time, professional_title, professional_position , professional_title_time, professional_position_time, address, work, work_nature, corp_code, photo, address_exam, address_code, city_name, bm_serial, jb_code, type_code, type, professional, archives_no, create_by, create_time, exam_subject, update_by, update_time, del_flag, qualified, flag,audit_status,candidate_type,isoldexaminee,hand,inputType,registerStatus,isAutomaticCheck,old_work ,remark from person_info
    </sql>

    <select id="selectPersonInfoList" parameterType="com.tzstcl.person.domain.PersonInfo" resultMap="PersonInfoResult">
        <include refid="selectPersonInfoVo"/>
        <where>
            del_flag = 0
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="sex != null "> and sex = #{sex}</if>
            <if test="nationality != null  and nationality != ''"> and nationality = #{nationality}</if>
            <if test="nation != null  and nation != ''"> and nation = #{nation}</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="politicalOutlook != null "> and political_outlook = #{politicalOutlook}</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and phone_number = #{phoneNumber}</if>
            <if test="school != null  and school != ''"> and school = #{school}</if>
            <if test="education != null "> and education = #{education}</if>
            <if test="specializedSubject != null  and specializedSubject != ''"> and specialized_subject = #{specializedSubject}</if>
            <if test="graduationTime != null "> and graduation_time = #{graduationTime}</if>
            <if test="workTime != null  and workTime != ''"> and work_time = #{workTime}</if>
            <if test="professionalWorkTime != null  and professionalWorkTime != ''"> and professional_work_time = #{professionalWorkTime}</if>
            <if test="professionalTitle != null  and professionalTitle != ''"> and professional_title = #{professionalTitle}</if>
            <if test="professionalPosition != null  and professionalPosition != ''"> and professional_position = #{professionalPosition}</if>
            <if test="professionalPositionTime != null  and professionalPositionTime != ''"> and professional_position_time = #{professionalPositionTime}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="corpCode !=null and corpCode !=''">and corp_code = #{corpCode}</if>
            <if test="work != null  and work != ''"> and work like concat('%',#{work},'%') </if>
            <if test="workNature != null "> and work_nature = #{workNature}</if>
            <if test="photo != null  and photo != ''"> and photo = #{photo}</if>
            <if test="addressExam != null  and addressExam != ''"> and address_exam = #{addressExam}</if>
            <if test="addressCode != null  and addressCode != ''"> and address_code = #{addressCode}</if>
            <if test="cityName != null  and cityName != ''"> and city_name like concat('%', #{cityName}, '%')</if>
            <if test="bmSerial != null  and bmSerial != ''"> and bm_serial = #{bmSerial}</if>
            <if test="jbCode != null  and jbCode != ''"> and jb_code = #{jbCode}</if>
            <if test="typeCode != null  and typeCode != ''"> and type_code = #{typeCode}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="professional != null  and professional != ''"> and professional = #{professional}</if>
            <if test="archivesNo != null  and archivesNo != ''"> and archives_no = #{archivesNo}</if>
            <if test="examSubject != null  and examSubject != ''"> and exam_subject = #{examSubject}</if>
            <if test="qualified != null  and qualified != ''"> and qualified = #{qualified}</if>
            <if test="flag != null "> and flag = #{flag}</if>
            <if test="candidateType != null "> and candidate_type = #{candidateType}</if>
            <if test="auditStatus != null"> and audit_status = #{auditStatus}</if>
            <if test="isoldexaminee != null"> and isoldexaminee = #{isoldexaminee}</if>
            <if test="yearInfo != null"> and YEAR(create_time) = #{yearInfo}</if>
            <if test="inputType != null"> and inputType = inputType</if>
            <if test="isAutomaticCheck != null"> and isAutomaticCheck = isAutomaticCheck</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectPersonInfoById" parameterType="String" resultMap="PersonInfoResult">
        <include refid="selectPersonInfoVo"/>
        where id = #{id} and del_flag =0
    </select>
    <select id="getInfoByIdCard" resultMap="PersonInfoResult">
        <include refid="selectPersonInfoVo1"/> where  id_card = #{IdCard}  and del_flag = 0 ;

    </select>


    <select id="getOneInfoexamInfo" resultMap="PersonInfoResult1">
        <include refid="selectPersonInfoVo1"/> where  id = #{id}  and del_flag =0;
    </select>
    <select id="queryexamInfo" resultMap="ExamInfoResult">
        select * from exam_info where del_flag =0 and id_card = #{id_card} and candidate_type = #{candidate_type}
    </select>

    <select id="querypersonDataList" resultMap="com.tzstcl.person.mapper.PersonDataMapper.PersonDataResult">
        select * from person_data where del_flag =0 and id_card = #{id_card}
    </select>


    <select id="getInfoByArchives_no" resultMap="PersonInfoResult">
        <include refid="selectPersonInfoVo1"/> where  archives_no = #{archivesNo} and del_flag =0;
    </select>
    <select id="selectByDept" resultMap="PersonInfoResult">
<!--       SELECT id,id_card,audit_status from-->
<!--        (-->
<!--        select p.id,id_card,audit_status from person_info p left join company c-->
<!--        on p.corp_code  = c.corp_code-->
<!--        where-->
<!--        p.del_flag =0 and c.del_flag = 0 and c.city_num = #{deptId} &lt;!&ndash;该部门下的企业&ndash;&gt;-->
<!--        union-->
<!--        select id,id_card,audit_status from person_info where city_name like concat ('%',#{deptName},'%' and del_flag =0)-->
<!--            )as tb-->
<!--        20230220按照报考地市直接查询，上面按照企业查询的暂时不用(可能无法获取企业corp_code)-->
        SELECT
        id,
        id_card,
        audit_status
        FROM
        person_info
        <where>
            del_flag = 0
            AND city_name LIKE concat ('%',#{remark},'%')
            <if test="yearInfo != null"> and YEAR(create_time) = #{yearInfo}</if>
            <if test="candidateType != null "> and candidate_type = #{candidateType}</if>
        </where>
    </select>
    <select id="getByDept" resultMap="PersonInfoResult">
        SELECT * from
        (
        select p.* from person_info p left join company c
        on p.corp_code  = c.corp_code
        where
        p.del_flag =0 and c.del_flag = 0 and c.city_num = #{deptId} <!--该部门下的企业-->
        union
        select * from person_info where city_name like concat ('%',#{deptName},'%') and   del_flag=0
        )as tb
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="work != null  and work != ''"> and work like concat('%',#{work},'%') </if>
            <if test="cityName != null  and cityName != ''"> and city_name like concat('%', #{cityName}, '%')</if>
        </where>
    </select>
    <select id="getInfoByIdCardAndcandidateType" resultMap="PersonInfoResult">
        <include refid="selectPersonInfoVo"/>
        where id_card = #{idCard} and candidate_type = #{candidateType} and del_flag =0;
    </select>
    <select id="getPersonInfoByIdCard" resultMap="PersonInfoResult">
        <include refid="selectPersonInfoVo1"/> where  id_card = #{IdCard} and del_flag =0;
    </select>
    <select id="getInfoByIdCard1" resultType="com.tzstcl.person.domain.PersonInfo">
        <include refid="selectPersonInfoVo1"/> where  id_card = #{IdCard} and del_flag =0 and registerStatus=0  group by id_card;
    </select>
    <select id="selectPersonInfoById1" resultMap="PersonInfoResult">
        <include refid="selectPersonInfoVo"/>
        where id_card = #{IdCard} and del_flag =0
    </select>
    <select id="selectPersonInfoLists" resultType="com.tzstcl.person.domain.PersonInfo">
        SELECT
        p.id as id,
        p.city_name as cityName,
        p.NAME as name,
        p.id_card as idCard,
        p.WORK as work,
        p.candidate_type AS candidateType,
        p.hand as hand,
        p.auditPerson as auditPerson,
        s.user_id AS userid,
        c.audit_status as auditListStatus,
        p.create_time as createTime,
        c.time as auditTime,
        c.id as auditId,
        c.number as number
        FROM
        `person_info` p
        LEFT JOIN sys_user s ON s.user_name = p.id_card AND s.del_flag = 0
        LEFT JOIN (
        SELECT a.sponsor, a.candidate_type, MAX(a.time) AS time
        FROM audit a
        GROUP BY a.sponsor, a.candidate_type
        ) b ON b.sponsor = s.user_id AND b.candidate_type = p.candidate_type
        LEFT JOIN audit c ON c.sponsor = b.sponsor AND c.candidate_type = b.candidate_type AND c.time = b.time
        <where>
            p.del_flag = 0
            <if test="yearInfo != null"> and YEAR(p.create_time) = #{yearInfo}</if>
            <if test="name != null  and name != ''"> and p.name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and p.id_card = #{idCard}</if>
            <if test="corpCode !=null and corpCode !=''">and p.corp_code = #{corpCode}</if>
            <if test="work != null  and work != ''"> and p.work like concat('%',#{work},'%') </if>
            <if test="auditPerson != null  and auditPerson != ''"> and p.auditPerson like concat(#{auditPerson},'%') </if>
            <if test="cityName != null  and cityName != ''"> and p.city_name like concat('%', #{cityName}, '%')</if>
            <if test="candidateType != null "> and p.candidate_type = #{candidateType}</if>
            <if test="inputType != null "> and p.inputType = #{inputType}</if>
            <if test="isAutomaticCheck != null "> and p.isAutomaticCheck = #{isAutomaticCheck}</if>
            <if test="auditStatus != null"> and (c.audit_status = #{auditStatus} or c.audit_status = #{auditListStatus})</if>
        </where>
        ${params.dataScope}
        GROUP BY
        p.id_card, p.candidate_type
        ORDER BY
        p.create_time DESC



<!--        SELECT-->
<!--        p.id as id,-->
<!--        p.city_name as cityName,-->
<!--        p.NAME as name,-->
<!--        p.id_card as idCard,-->
<!--        p.WORK as work,-->
<!--        p.candidate_type AS candidateType,-->
<!--        s.user_id AS userid,-->
<!--        c.audit_status as auditListStatus,-->
<!--        p.create_time as createTime-->
<!--        FROM-->
<!--        `person_info` p-->
<!--        LEFT JOIN sys_user s ON s.user_name = p.id_card-->
<!--        LEFT JOIN (-->
<!--        SELECT-->
<!--        a.sponsor,-->
<!--        a.audit_status,-->
<!--        a.candidate_type-->
<!--        FROM-->
<!--        (SELECT sponsor, candidate_type, MAX(time) AS time FROM audit GROUP BY sponsor, candidate_type) b-->
<!--        JOIN audit a ON a.sponsor = b.sponsor AND a.candidate_type = b.candidate_type AND a.time = b.time-->
<!--        ) c ON c.candidate_type = p.candidate_type AND c.sponsor = s.user_id-->
<!--        <where>-->
<!--            p.del_flag = 0-->
<!--            <if test="yearInfo != null"> and YEAR(p.create_time) = #{yearInfo}</if>-->
<!--            <if test="name != null  and name != ''"> and p.name like concat('%', #{name}, '%')</if>-->
<!--            <if test="idCard != null  and idCard != ''"> and p.id_card = #{idCard}</if>-->
<!--            <if test="corpCode !=null and corpCode !=''">and p.corp_code = #{corpCode}</if>-->
<!--            <if test="work != null  and work != ''"> and p.work like concat('%',#{work},'%') </if>-->
<!--            <if test="cityName != null  and cityName != ''"> and p.city_name like concat('%', #{cityName}, '%')</if>-->
<!--            <if test="candidateType != null "> and p.candidate_type = #{candidateType}</if>-->
<!--            <if test="inputType != null "> and p.inputType = #{inputType}</if>-->
<!--            <if test="auditStatus != null"> and c.audit_status = #{auditStatus} or c.audit_status = #{auditListStatus}</if>-->
<!--        </where>-->
<!--        GROUP BY-->
<!--        p.id_card, p.candidate_type-->
<!--        order by p.create_time desc-->
    </select>
    <select id="selectPersonInfoaudirStatusIsNotNull" resultType="com.tzstcl.person.domain.PersonInfo">
        SELECT
        p.id as id,
        p.city_name as cityName,
        p.NAME as name,
        p.id_card as idCard,
        p.WORK as work,
        p.candidate_type AS candidateType,
        p.create_time as createTime,
        p.auditPerson as auditPerson
        FROM
        `person_info` p
        <where>
            p.del_flag = 0
            and p.audit_status!=0
            <if test="yearInfo != null"> and YEAR(p.create_time) = #{yearInfo}</if>
            <if test="name != null  and name != ''"> and p.name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and p.id_card = #{idCard}</if>
            <if test="corpCode !=null and corpCode !=''">and p.corp_code = #{corpCode}</if>
            <if test="work != null  and work != ''"> and p.work like concat('%',#{work},'%') </if>
            <if test="auditPerson != null  and auditPerson != ''"> and p.auditPerson like concat(#{auditPerson},'%') </if>
            <if test="cityName != null  and cityName != ''"> and p.city_name like concat('%', #{cityName}, '%')</if>
            <if test="candidateType != null "> and p.candidate_type = #{candidateType}</if>
            <if test="isAutomaticCheck != null "> and p.isAutomaticCheck = #{isAutomaticCheck}</if>
            <if test="inputType != null "> and p.inputType = #{inputType}</if>
        </where>
        ${params.dataScope}
        GROUP BY
        p.id_card, p.candidate_type
        ORDER BY
        p.create_time DESC


<!--        SELECT-->
<!--        p.id as id,-->
<!--        p.city_name as cityName,-->
<!--        p.NAME as name,-->
<!--        p.id_card as idCard,-->
<!--        p.WORK as work,-->
<!--        p.candidate_type AS candidateType,-->
<!--        s.user_id AS userid,-->
<!--        c.audit_status as auditListStatus,-->
<!--        p.create_time as createTime-->
<!--        FROM-->
<!--        `person_info` p-->
<!--        LEFT JOIN sys_user s ON s.user_name = p.id_card AND s.del_flag = 0-->
<!--        LEFT JOIN (-->
<!--        SELECT a.sponsor, a.candidate_type, MAX(a.time) AS time-->
<!--        FROM audit a-->
<!--        GROUP BY a.sponsor, a.candidate_type-->
<!--        ) b ON b.sponsor = s.user_id AND b.candidate_type = p.candidate_type-->
<!--        LEFT JOIN audit c ON c.sponsor = b.sponsor AND c.candidate_type = b.candidate_type AND c.time = b.time-->
<!--        <where>-->
<!--            p.del_flag = 0-->
<!--            and c.audit_status is not null-->
<!--            <if test="yearInfo != null"> and YEAR(p.create_time) = #{yearInfo}</if>-->
<!--            <if test="name != null  and name != ''"> and p.name like concat('%', #{name}, '%')</if>-->
<!--            <if test="idCard != null  and idCard != ''"> and p.id_card = #{idCard}</if>-->
<!--            <if test="corpCode !=null and corpCode !=''">and p.corp_code = #{corpCode}</if>-->
<!--            <if test="work != null  and work != ''"> and p.work like concat('%',#{work},'%') </if>-->
<!--            <if test="cityName != null  and cityName != ''"> and p.city_name like concat('%', #{cityName}, '%')</if>-->
<!--            <if test="candidateType != null "> and p.candidate_type = #{candidateType}</if>-->
<!--            <if test="inputType != null "> and p.inputType = #{inputType}</if>-->
<!--&lt;!&ndash;            <if test="auditStatus != null"> and c.audit_status = #{auditStatus} or c.audit_status = #{auditListStatus}</if>&ndash;&gt;-->

<!--        </where>-->
<!--        GROUP BY-->
<!--        p.id_card, p.candidate_type-->
<!--        order by p.create_time desc-->
    </select>
    <select id="selectPersonInfoAuditStatusIsNull" resultType="com.tzstcl.person.domain.PersonInfo">
        SELECT
        p.id as id,
        p.city_name as cityName,
        p.NAME as name,
        p.id_card as idCard,
        p.WORK as work,
        p.candidate_type AS candidateType,
        p.create_time as createTime,
        p.auditPerson as auditPerson
        FROM
        `person_info` p
        <where>
            p.del_flag = 0
            and p.audit_status=0
            <if test="yearInfo != null"> and YEAR(p.create_time) = #{yearInfo}</if>
            <if test="name != null  and name != ''"> and p.name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and p.id_card = #{idCard}</if>
            <if test="corpCode !=null and corpCode !=''">and p.corp_code = #{corpCode}</if>
            <if test="work != null  and work != ''"> and p.work like concat('%',#{work},'%') </if>
            <if test="auditPerson != null  and auditPerson != ''"> and p.auditPerson like concat(#{auditPerson},'%') </if>
            <if test="cityName != null  and cityName != ''"> and p.city_name like concat('%', #{cityName}, '%')</if>
            <if test="candidateType != null "> and p.candidate_type = #{candidateType}</if>
            <if test="inputType != null "> and p.inputType = #{inputType}</if>
            <if test="isAutomaticCheck != null "> and p.isAutomaticCheck = #{isAutomaticCheck}</if>
        </where>
        ${params.dataScope}
        GROUP BY
        p.id_card, p.candidate_type
        ORDER BY
        p.create_time DESC


<!--        SELECT-->
<!--        p.id as id,-->
<!--        p.city_name as cityName,-->
<!--        p.NAME as name,-->
<!--        p.id_card as idCard,-->
<!--        p.WORK as work,-->
<!--        p.candidate_type AS candidateType,-->
<!--        s.user_id AS userid,-->
<!--        c.audit_status as auditListStatus,-->
<!--        p.create_time as createTime-->
<!--        FROM-->
<!--        `person_info` p-->
<!--        LEFT JOIN sys_user s ON s.user_name = p.id_card AND s.del_flag = 0-->
<!--        LEFT JOIN (-->
<!--        SELECT a.sponsor, a.candidate_type, MAX(a.time) AS time-->
<!--        FROM audit a-->
<!--        GROUP BY a.sponsor, a.candidate_type-->
<!--        ) b ON b.sponsor = s.user_id AND b.candidate_type = p.candidate_type-->
<!--        LEFT JOIN audit c ON c.sponsor = b.sponsor AND c.candidate_type = b.candidate_type AND c.time = b.time-->
<!--        <where>-->
<!--            p.del_flag = 0-->
<!--            and c.audit_status is null-->
<!--            <if test="yearInfo != null"> and YEAR(p.create_time) = #{yearInfo}</if>-->
<!--            <if test="name != null  and name != ''"> and p.name like concat('%', #{name}, '%')</if>-->
<!--            <if test="idCard != null  and idCard != ''"> and p.id_card = #{idCard}</if>-->
<!--            <if test="corpCode !=null and corpCode !=''">and p.corp_code = #{corpCode}</if>-->
<!--            <if test="work != null  and work != ''"> and p.work like concat('%',#{work},'%') </if>-->
<!--            <if test="cityName != null  and cityName != ''"> and p.city_name like concat('%', #{cityName}, '%')</if>-->
<!--            <if test="candidateType != null "> and p.candidate_type = #{candidateType}</if>-->
<!--            <if test="inputType != null "> and p.inputType = #{inputType}</if>-->
<!--            &lt;!&ndash;            <if test="auditStatus != null"> and c.audit_status = #{auditStatus} or c.audit_status = #{auditListStatus}</if>&ndash;&gt;-->

<!--        </where>-->
<!--        GROUP BY-->
<!--        p.id_card, p.candidate_type-->
<!--        order by p.create_time desc-->
    </select>
    <select id="selectPersonInfoListsCount" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        person_info p
        <where>
            p.del_flag = 0
            <if test="yearInfo != null"> and YEAR(p.create_time) = #{yearInfo}</if>
            <if test="deptId != null"> and p.dept_id = #{deptId}</if>
            <if test="name != null  and name != ''"> and p.name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and p.id_card = #{idCard}</if>
            <if test="corpCode !=null and corpCode !=''">and p.corp_code = #{corpCode}</if>
            <if test="work != null  and work != ''"> and p.work like concat('%',#{work},'%') </if>
            <if test="cityName != null  and cityName != ''"> and p.city_name like concat('%', #{cityName}, '%')</if>
            <if test="candidateType != null "> and p.candidate_type = #{candidateType}</if>
            <if test="inputType != null "> and p.inputType = #{inputType}</if>
            <if test="type != null "> and p.type = #{type}</if>
            <if test="isAutomaticCheck != null "> and p.isAutomaticCheck = #{isAutomaticCheck}</if>
            <if test="auditResult != null "> and p.auditResult = #{auditResult}</if>
            <if test="auditStatus != null"> and (p.audit_status = #{auditStatus} or p.audit_status = #{auditListStatus})</if>
        </where>
        ${params.dataScope}
    </select>
    <select id="selectPersonInfoLists1" resultType="com.tzstcl.person.domain.PersonInfo">

        SELECT
        p.id as id,
        p.city_name as cityName,
        p.NAME as name,
        p.id_card as idCard,
        p.WORK as work,
        p.candidate_type AS candidateType,
        p.create_time as createTime,
        p.hand as hand,
        p.auditPerson as auditPerson,
        p.audit_status as auditStatus,
        p.auditResult as auditResult,
        p.dept_id as deptId,
        p.update_time as updateTime
        FROM
        `person_info` p
        <where>
            p.del_flag = 0
            <if test="yearInfo != null"> and YEAR(p.create_time) = #{yearInfo}</if>
            <if test="name != null  and name != ''"> and p.name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and p.id_card = #{idCard}</if>
            <if test="deptId != null "> and p.dept_id = #{deptId}</if>
            <if test="corpCode !=null and corpCode !=''">and p.corp_code = #{corpCode}</if>
            <if test="work != null  and work != ''"> and p.work like concat('%',#{work},'%') </if>
            <if test="cityName != null  and cityName != ''"> and p.city_name like concat('%', #{cityName}, '%')</if>
            <if test="candidateType != null "> and p.candidate_type = #{candidateType}</if>
            <if test="inputType != null "> and p.inputType = #{inputType}</if>
            <if test="isAutomaticCheck != null "> and p.isAutomaticCheck = #{isAutomaticCheck}</if>
            <if test="auditPerson != null and auditPerson!=''"> and p.auditPerson like concat(#{auditPerson}, '%')</if>
            <if test="auditResult != null ">and p.auditResult = #{auditResult}</if>
            <if test="type != null ">and p.type like concat('%',#{type},'%')</if>
            <if test="auditStatus != null"> and (p.audit_status = #{auditStatus} or p.audit_status = #{auditListStatus})</if>
            <if test="workTimeMin != null"> and (p.work_time &gt;= #{workTimeMin} and  p.work_time &lt; #{workTimeMax})</if>
        </where>
        ${params.dataScope}
        GROUP BY
        p.id_card, p.candidate_type
        order by p.update_time asc
    </select>
    <select id="countPersonInfoAuditStatusIsNull" resultType="java.lang.Long">
        SELECT
            count(DISTINCT id_card, candidate_type)
        FROM
            `person_info`
        where
            del_flag = 0
          and audit_status=0
    </select>
    <select id="selectPersonInfoListExprot" resultType="com.tzstcl.person.domain.PersonInfoExport">
        SELECT
        p.id as id,
        p.city_name as cityName,
        p.NAME as name,
        p.id_card as idCard,
        p.WORK as work,
        p.candidate_type AS candidateType,
        p.create_time as createTime,
        p.auditPerson as auditPerson,
        p.audit_status as auditStatus,
        p.auditResult as auditResult,
        p.update_time as auditTime,
        p.professional as professional,
        p.type as type
        FROM
        `person_info` p
        <where>
            p.del_flag = 0
            <if test="yearInfo != null"> and YEAR(p.create_time) = #{yearInfo}</if>
            <if test="name != null  and name != ''"> and p.name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and p.id_card = #{idCard}</if>
            <if test="corpCode !=null and corpCode !=''">and p.corp_code = #{corpCode}</if>
            <if test="work != null  and work != ''"> and p.work like concat('%',#{work},'%') </if>
            <if test="cityName != null  and cityName != ''"> and p.city_name like concat('%', #{cityName}, '%')</if>
            <if test="candidateType != null "> and p.candidate_type = #{candidateType}</if>
            <if test="inputType != null "> and p.inputType = #{inputType}</if>
            <if test="type != null ">and p.type like concat(#{type},'%')</if>
            <if test="isAutomaticCheck != null "> and p.isAutomaticCheck = #{isAutomaticCheck}</if>
            <if test="auditPerson != null and auditPerson!=''"> and p.auditPerson like concat(#{auditPerson}, '%')</if>
            <if test="auditResult != null ">and p.auditResult = #{auditResult}</if>
            <if test="auditStatus != null"> and (p.audit_status = #{auditStatus} or p.audit_status = #{auditListStatus})</if>
            <if test="workTimeMin != null"> and (p.work_time &gt;= #{workTimeMin} and  p.work_time &lt; #{workTimeMax})</if>
        </where>
        ${params.dataScope}
        GROUP BY
        p.id_card, p.candidate_type
    </select>
    <select id="selectPersonInfoByIdCardAndCandType" resultMap="PersonInfoResult">
        select * from person_info where id_card = #{idCard} and candidate_type = #{candidateType} and del_flag = 0
    </select>
    <select id="getPersonInfoByType" resultType="java.lang.String">
        SELECT
            type
        FROM
            person_info
        GROUP BY
            type
    </select>
    <select id="getPersonInfoByPhone" resultType="com.tzstcl.person.domain.PersonInfo">
        SELECT
        p.id as id,
        p.city_name as cityName,
        p.NAME as name,
        p.id_card as idCard,
        p.WORK as work,
        p.candidate_type AS candidateType,
        p.create_time as createTime,
        p.hand as hand,
        p.auditPerson as auditPerson,
        p.audit_status as auditStatus,
        p.auditResult as auditResult,
        p.dept_id as deptId,
        p.update_time as updateTime
        FROM
        `person_info` p
        <where>
            p.del_flag = 0
            and p.phone_number is not null
            <if test="yearInfo != null"> and YEAR(p.create_time) = #{yearInfo}</if>
            <if test="name != null  and name != ''"> and p.name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and p.id_card = #{idCard}</if>
            <if test="corpCode !=null and corpCode !=''">and p.corp_code = #{corpCode}</if>
            <if test="work != null  and work != ''"> and p.work like concat('%',#{work},'%') </if>
            <if test="cityName != null  and cityName != ''"> and p.city_name like concat('%', #{cityName}, '%')</if>
            <if test="candidateType != null "> and p.candidate_type = #{candidateType}</if>
            <if test="inputType != null "> and p.inputType = #{inputType}</if>
            <if test="isAutomaticCheck != null "> and p.isAutomaticCheck = #{isAutomaticCheck}</if>
            <if test="auditPerson != null and auditPerson!=''"> and p.auditPerson like concat(#{auditPerson}, '%')</if>
            <if test="auditResult != null ">and p.auditResult = #{auditResult}</if>
            <if test="type != null ">and p.type like concat(#{type},'%')</if>
            <if test="auditStatus != null"> and (p.audit_status = #{auditStatus} or p.audit_status = #{auditListStatus})</if>
            <if test="workTimeMin != null"> and (p.work_time &gt;= #{workTimeMin} and  p.work_time &lt; #{workTimeMax})</if>
        </where>
        GROUP BY
        p.id_card, p.candidate_type
    </select>
    <select id="selectCorpAuditPersonList" resultMap="PersonInfoResult">
        <include refid="selectPersonInfoVo"/>
        <where>
            del_flag = 0
            and audit_status != 0
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="sex != null "> and sex = #{sex}</if>
            <if test="nationality != null  and nationality != ''"> and nationality = #{nationality}</if>
            <if test="nation != null  and nation != ''"> and nation = #{nation}</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="politicalOutlook != null "> and political_outlook = #{politicalOutlook}</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and phone_number = #{phoneNumber}</if>
            <if test="school != null  and school != ''"> and school = #{school}</if>
            <if test="education != null "> and education = #{education}</if>
            <if test="specializedSubject != null  and specializedSubject != ''"> and specialized_subject = #{specializedSubject}</if>
            <if test="graduationTime != null "> and graduation_time = #{graduationTime}</if>
            <if test="workTime != null  and workTime != ''"> and work_time = #{workTime}</if>
            <if test="professionalWorkTime != null  and professionalWorkTime != ''"> and professional_work_time = #{professionalWorkTime}</if>
            <if test="professionalTitle != null  and professionalTitle != ''"> and professional_title = #{professionalTitle}</if>
            <if test="professionalPosition != null  and professionalPosition != ''"> and professional_position = #{professionalPosition}</if>
            <if test="professionalPositionTime != null  and professionalPositionTime != ''"> and professional_position_time = #{professionalPositionTime}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="corpCode !=null and corpCode !=''">and corp_code = #{corpCode}</if>
            <if test="work != null  and work != ''"> and work like concat('%',#{work},'%') </if>
            <if test="workNature != null "> and work_nature = #{workNature}</if>
            <if test="photo != null  and photo != ''"> and photo = #{photo}</if>
            <if test="addressExam != null  and addressExam != ''"> and address_exam = #{addressExam}</if>
            <if test="addressCode != null  and addressCode != ''"> and address_code = #{addressCode}</if>
            <if test="cityName != null  and cityName != ''"> and city_name like concat('%', #{cityName}, '%')</if>
            <if test="bmSerial != null  and bmSerial != ''"> and bm_serial = #{bmSerial}</if>
            <if test="jbCode != null  and jbCode != ''"> and jb_code = #{jbCode}</if>
            <if test="typeCode != null  and typeCode != ''"> and type_code = #{typeCode}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="professional != null  and professional != ''"> and professional = #{professional}</if>
            <if test="archivesNo != null  and archivesNo != ''"> and archives_no = #{archivesNo}</if>
            <if test="examSubject != null  and examSubject != ''"> and exam_subject = #{examSubject}</if>
            <if test="qualified != null  and qualified != ''"> and qualified = #{qualified}</if>
            <if test="flag != null "> and flag = #{flag}</if>
            <if test="candidateType != null "> and candidate_type = #{candidateType}</if>
            <if test="isoldexaminee != null"> and isoldexaminee = #{isoldexaminee}</if>
            <if test="yearInfo != null"> and YEAR(create_time) = #{yearInfo}</if>
            <if test="inputType != null"> and inputType = inputType</if>
            <if test="isAutomaticCheck != null"> and isAutomaticCheck = isAutomaticCheck</if>
        </where>
        order by create_time desc
    </select>
    <select id="selectPersonInfoListsByAverage" resultType="com.tzstcl.person.domain.PersonInfo">

        SELECT
        p.id as id,
        p.city_name as cityName,
        p.NAME as name,
        p.id_card as idCard,
        p.WORK as work,
        p.candidate_type AS candidateType,
        p.create_time as createTime,
        p.hand as hand,
        p.auditPerson as auditPerson,
        p.audit_status as auditStatus,
        p.auditResult as auditResult,
        p.dept_id as deptId,
        p.update_time as updateTime
        FROM
        `person_info` p
        <where>
            p.del_flag = 0 and SUBSTRING(p.id, LENGTH(p.id) -1, 1) % #{auditTotal} = #{indexTotal}
            <if test="yearInfo != null"> and YEAR(p.create_time) = #{yearInfo}</if>
            <if test="name != null  and name != ''"> and p.name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and p.id_card = #{idCard}</if>
            <if test="corpCode !=null and corpCode !=''">and p.corp_code = #{corpCode}</if>
            <if test="work != null  and work != ''"> and p.work like concat('%',#{work},'%') </if>
            <if test="cityName != null  and cityName != ''"> and p.city_name like concat('%', #{cityName}, '%')</if>
            <if test="candidateType != null "> and p.candidate_type = #{candidateType}</if>
            <if test="candidateTypeList != null and candidateTypeList.size() > 0 ">
                            and p.candidate_type IN
                <foreach collection="candidateTypeList" item="candidateType" open="(" separator="," close=")">
                    #{candidateType}
                </foreach>
            </if>
            <if test="inputType != null "> and p.inputType = #{inputType}</if>
            <if test="isAutomaticCheck != null "> and p.isAutomaticCheck = #{isAutomaticCheck}</if>
            <if test="auditPerson != null and auditPerson!=''"> and p.auditPerson like concat(#{auditPerson}, '%')</if>
            <if test="auditResult != null ">and p.auditResult = #{auditResult}</if>
            <if test="type != null ">and p.type like concat(#{type},'%')</if>
            <if test="auditStatus != null"> and (p.audit_status = #{auditStatus} or p.audit_status = #{auditListStatus})</if>
            <if test="workTimeMin != null"> and (p.work_time &gt;= #{workTimeMin} and  p.work_time &lt; #{workTimeMax})</if>
            <if test="deptId != null"> and p.dept_id = #{deptId}</if>
        </where>
        ${params.dataScope}
        GROUP BY
        p.id_card, p.candidate_type
        order by p.update_time asc
    </select>
    <select id="selectMaxInputTypeByCandidateType" resultType="java.lang.Integer">
        select max(inputType) from person_info where candidate_type = #{candidateType}
    </select>
    <select id="selectPersonInfoListByEjzjsPush"  resultMap="PersonInfoResult">
        <include refid="selectPersonInfoVo"/>
            where del_flag = 0 and audit_status = 3 and candidate_type = 20 and inputType = #{inputType}

    </select>
    <select id="selectMaxInputType" resultType="java.lang.Integer">
        select max(inputType) from person_info
    </select>

    <insert id="insertPersonInfo" parameterType="com.tzstcl.person.domain.PersonInfoImport">
        insert into person_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="sex != null">sex,</if>
            <if test="nationality != null and nationality != ''">nationality,</if>
            <if test="nation != null and nation != ''">nation,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="politicalOutlook != null">political_outlook,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="school != null and school != ''">school,</if>
            <if test="education != null">education,</if>
            <if test="specializedSubject != null and specializedSubject != ''">specialized_subject,</if>
            <if test="graduationTime != null">graduation_time,</if>
            <if test="workTime != null and workTime != ''">work_time,</if>
            <if test="professionalWorkTime != null and professionalWorkTime != ''">professional_work_time,</if>
            <if test="professionalTitle != null">professional_title,</if>
            <if test="professionalPosition != null">professional_position,</if>
            <if test="professionalPositionTime != null">professional_position_time,</if>
            <if test="professionalTitleTime != null and professionalTitleTime !=''">professional_title_time,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="work != null">work,</if>
            <if test="workNature != null">work_nature,</if>
            <if test="photo != null">photo,</if>
            <if test="addressExam != null and addressExam != ''">address_exam,</if>
            <if test="addressCode != null and addressCode != ''">address_code,</if>
            <if test="cityName != null and cityName != ''">city_name,</if>
            <if test="bmSerial != null and bmSerial != ''">bm_serial,</if>
            <if test="jbCode != null and jbCode != ''">jb_code,</if>
            <if test="typeCode != null">type_code,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="professional != null and professional != ''">professional,</if>
            <if test="archivesNo != null and archivesNo != ''">archives_no,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="examSubject != null and examSubject != ''">exam_subject,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="qualified != null">qualified,</if>
            <if test="flag != null">flag,</if>
            <if test="corpCode !=null and corpCode !=''">corp_code,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="candidateType != null">candidate_type,</if>
            <if test="isoldexaminee != null">isoldexaminee,</if>
            <if test="hand != null">hand,</if>
            <if test="inputType != null">inputType,</if>
            <if test="isAutomaticCheck != null">isAutomaticCheck,</if>
            <if test="oldWork != null">old_work,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="sex != null">#{sex},</if>
            <if test="nationality != null and nationality != ''">#{nationality},</if>
            <if test="nation != null and nation != ''">#{nation},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="politicalOutlook != null">#{politicalOutlook},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="school != null and school != ''">#{school},</if>
            <if test="education != null">#{education},</if>
            <if test="specializedSubject != null and specializedSubject != ''">#{specializedSubject},</if>
            <if test="graduationTime != null">#{graduationTime},</if>
            <if test="workTime != null and workTime != ''">#{workTime},</if>
            <if test="professionalWorkTime != null and professionalWorkTime != ''">#{professionalWorkTime},</if>
            <if test="professionalTitle != null">#{professionalTitle},</if>
            <if test="professionalPosition != null">#{professionalPosition},</if>
            <if test="professionalPositionTime != null">#{professionalPositionTime},</if>
            <if test="professionalTitleTime != null">#{professionalTitleTime},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="work != null">#{work},</if>
            <if test="workNature != null">#{workNature},</if>
            <if test="photo != null">#{photo},</if>
            <if test="addressExam != null and addressExam != ''">#{addressExam},</if>
            <if test="addressCode != null and addressCode != ''">#{addressCode},</if>
            <if test="cityName != null and cityName != ''">#{cityName},</if>
            <if test="bmSerial != null and bmSerial != ''">#{bmSerial},</if>
            <if test="jbCode != null and jbCode != ''">#{jbCode},</if>
            <if test="typeCode != null">#{typeCode},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="professional != null and professional != ''">#{professional},</if>
            <if test="archivesNo != null and archivesNo != ''">#{archivesNo},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="examSubject != null and examSubject != ''">#{examSubject},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="qualified != null">#{qualified},</if>
            <if test="flag != null">#{flag},</if>
            <if test="corpCode !=null and corpCode !=''">#{corpCode},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="candidateType != null">#{candidateType},</if>
            <if test="isoldexaminee != null">#{isoldexaminee},</if>
            <if test="hand != null">#{hand},</if>
            <if test="inputType != null">#{inputType},</if>
            <if test="isAutomaticCheck != null">#{isAutomaticCheck},</if>
            <if test="oldWork != null">#{oldWork},</if>
            <if test="remark != null">#{remark},</if>

        </trim>
    </insert>

    <update id="updatePersonInfo" parameterType="com.tzstcl.person.domain.PersonInfo">
        update person_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="nationality != null and nationality != ''">nationality = #{nationality},</if>
            <if test="nation != null and nation != ''">nation = #{nation},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="politicalOutlook != null">political_outlook = #{politicalOutlook},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="school != null and school != ''">school = #{school},</if>
            <if test="education != null">education = #{education},</if>
            <if test="specializedSubject != null and specializedSubject != ''">specialized_subject = #{specializedSubject},</if>
            <if test="graduationTime != null">graduation_time = #{graduationTime},</if>
            <if test="workTime != null and workTime != ''">work_time = #{workTime},</if>
            <if test="professionalWorkTime != null and professionalWorkTime != ''">professional_work_time = #{professionalWorkTime},</if>
            <if test="professionalTitle != null">professional_title = #{professionalTitle},</if>
            <if test="professionalPosition != null">professional_position = #{professionalPosition},</if>
            <if test="professionalPositionTime != null">professional_position_time = #{professionalPositionTime},</if>
            <if test="professionalTitleTime != null">professional_title_time = #{professionalTitleTime},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="work != null">work = #{work},</if>
            <if test="workNature != null">work_nature = #{workNature},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="addressExam != null and addressExam != ''">address_exam = #{addressExam},</if>
            <if test="addressCode != null and addressCode != ''">address_code = #{addressCode},</if>
            <if test="cityName != null and cityName != ''">city_name = #{cityName},</if>
            <if test="bmSerial != null and bmSerial != ''">bm_serial = #{bmSerial},</if>
            <if test="jbCode != null and jbCode != ''">jb_code = #{jbCode},</if>
            <if test="typeCode != null">type_code = #{typeCode},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="professional != null and professional != ''">professional = #{professional},</if>
            <if test="archivesNo != null and archivesNo != ''">archives_no = #{archivesNo},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="examSubject != null and examSubject != ''">exam_subject = #{examSubject},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="qualified != null">qualified = #{qualified},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="corpCode !=null and corpCode !=''">corp_code =#{corpCode},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="candidateType != null">candidate_type = #{candidateType},</if>
            <if test="isoldexaminee != null">isoldexaminee = #{isoldexaminee},</if>
            <if test="hand != null">hand = #{hand},</if>
            <if test="inputType != null">inputType = #{inputType},</if>
            <if test="isAutomaticCheck != null">isAutomaticCheck = #{isAutomaticCheck},</if>
            <if test="auditPerson != null">auditPerson = #{auditPerson},</if>
            <if test="auditResult != null">auditResult = #{auditResult},</if>
            <if test="oldWork != null">old_work = #{oldWork},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updatePersonInfoByIdCard">
        update person_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="nationality != null and nationality != ''">nationality = #{nationality},</if>
            <if test="nation != null and nation != ''">nation = #{nation},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="politicalOutlook != null">political_outlook = #{politicalOutlook},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="school != null and school != ''">school = #{school},</if>
            <if test="education != null">education = #{education},</if>
            <if test="specializedSubject != null and specializedSubject != ''">specialized_subject = #{specializedSubject},</if>
            <if test="graduationTime != null">graduation_time = #{graduationTime},</if>
            <if test="workTime != null and workTime != ''">work_time = #{workTime},</if>
            <if test="professionalWorkTime != null and professionalWorkTime != ''">professional_work_time = #{professionalWorkTime},</if>
            <if test="professionalTitle != null">professional_title = #{professionalTitle},</if>
            <if test="professionalPosition != null">professional_position = #{professionalPosition},</if>
            <if test="professionalPositionTime != null">professional_position_time = #{professionalPositionTime},</if>
            <if test="professionalTitleTime != null">professional_title_time = #{professionalTitleTime},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="work != null">work = #{work},</if>
            <if test="workNature != null">work_nature = #{workNature},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="addressExam != null and addressExam != ''">address_exam = #{addressExam},</if>
            <if test="addressCode != null and addressCode != ''">address_code = #{addressCode},</if>
            <if test="cityName != null and cityName != ''">city_name = #{cityName},</if>
            <if test="bmSerial != null and bmSerial != ''">bm_serial = #{bmSerial},</if>
            <if test="jbCode != null and jbCode != ''">jb_code = #{jbCode},</if>
            <if test="typeCode != null">type_code = #{typeCode},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="professional != null and professional != ''">professional = #{professional},</if>
            <if test="archivesNo != null and archivesNo != ''">archives_no = #{archivesNo},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="examSubject != null and examSubject != ''">exam_subject = #{examSubject},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="qualified != null">qualified = #{qualified},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="corpCode !=null and corpCode !=''">corp_code =#{corpCode},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="candidateType != null">candidate_type = #{candidateType},</if>
            <if test="isoldexaminee != null">isoldexaminee = #{isoldexaminee},</if>
            <if test="hand != null">hand = #{hand},</if>
            <if test="inputType != null">inputType = #{inputType},</if>
            <if test="isAutomaticCheck != null">isAutomaticCheck = #{isAutomaticCheck},</if>
            <if test="auditPerson != null">auditPerson = #{auditPerson},</if>
            <if test="auditResult != null">auditResult = #{auditResult},</if>
            <if test="oldWork != null">old_work = #{oldWork},</if>
        </trim>
        where id_card = #{idCard} and candidate_type = #{candidateType}
    </update>
    <update id="updatePersonInfoAuditPersonByIdCard">
        update person_info
        set auditPerson=#{auditPersonName}
        where id_card = #{idCard} and candidate_type = #{candidateType}
    </update>
    <update id="updatePersonInfoByCandidateType">
        update person_info
        set registerStatus = #{registerStatus}
        where
            del_flag =0 and
            candidate_type = #{candidateType}
    </update>
    <update id="updatePersonInfoById">
        update person_info
        set registerStatus = #{registerStatus}
        where
            del_flag =0 and
            id = #{id}
    </update>
    <update id="updateOldWorkByIdCard">
        update person_info
        set candidate_type = #{candidateType}
        where id_card = #{idCard} and inputType = #{inputType}
    </update>

    <delete id="deletePersonInfoById" parameterType="String">
        update person_info set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deletePersonInfoByIds" parameterType="String">
        update person_info set del_flag = 1 in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
