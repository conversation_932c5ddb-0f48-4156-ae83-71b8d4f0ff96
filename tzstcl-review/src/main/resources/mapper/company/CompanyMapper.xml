<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.company.mapper.CompanyMapper">

    <resultMap type="Company" id="CompanyResult">
        <result property="id"    column="id"    />
        <result property="corpCode"    column="corp_code"    />
        <result property="corpName"    column="corp_name"    />
        <result property="cityNum"    column="city_num"    />
        <result property="countyNum"    column="county_num"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectCompanyVo">
        select id, corp_code, corp_name, city_num, county_num, create_by, create_time, update_by, update_time, del_flag from company
    </sql>

    <select id="selectCompanyList" parameterType="Company" resultMap="CompanyResult">
        <include refid="selectCompanyVo"/>
        <where>
            <if test="corpCode != null  and corpCode != ''"> and corp_code = #{corpCode}</if>
            <if test="corpName != null  and corpName != ''"> and corp_name like concat('%', #{corpName}, '%')</if>
            <if test="cityNum != null  and cityNum != ''"> and city_num = #{cityNum}</if>
            <if test="countyNum != null  and countyNum != ''"> and county_num = #{countyNum}</if>
        </where>
    </select>

    <select id="selectCompanyById" parameterType="String" resultMap="CompanyResult">
        <include refid="selectCompanyVo"/>
        where id = #{id}
    </select>
    <select id="getByCorpCode" resultMap="CompanyResult">
        SELECT corp_code,corp_name,city_num,county_num
        FROM company
        where corp_code = #{corpCode} and del_flag =0
        ORDER BY create_time DESC
        LIMIT 1
    </select>
    <select id="getByName" resultType="java.lang.String">
        select corp_code from company where corp_name =#{corpName} and del_flag =0
    </select>
    <select id="selectCompanyByCorpCode" resultMap="CompanyResult">
        <include refid="selectCompanyVo"></include>
        where corp_code = #{corpCode}
    </select>

    <insert id="insertCompany" parameterType="Company">
        insert into company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="corpCode != null and corpCode != ''">corp_code,</if>
            <if test="corpName != null and corpName != ''">corp_name,</if>
            <if test="cityNum != null">city_num,</if>
            <if test="countyNum != null">county_num,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="corpCode != null and corpCode != ''">#{corpCode},</if>
            <if test="corpName != null and corpName != ''">#{corpName},</if>
            <if test="cityNum != null">#{cityNum},</if>
            <if test="countyNum != null">#{countyNum},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateCompany" parameterType="Company">
        update company
        <trim prefix="SET" suffixOverrides=",">
            <if test="corpCode != null and corpCode != ''">corp_code = #{corpCode},</if>
            <if test="corpName != null and corpName != ''">corp_name = #{corpName},</if>
            <if test="cityNum != null">city_num = #{cityNum},</if>
            <if test="countyNum != null">county_num = #{countyNum},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateByCorpCode" parameterType="Company">
        update company
        <trim prefix="SET" suffixOverrides=",">
            <if test="corpCode != null and corpCode != ''">corp_code = #{corpCode},</if>
            <if test="corpName != null and corpName != ''">corp_name = #{corpName},</if>
            <if test="cityNum != null">city_num = #{cityNum},</if>
            <if test="countyNum != null">county_num = #{countyNum},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where corp_code =#{corpCode}
    </update>

    <delete id="deleteCompanyById" parameterType="String">
        delete from company where id = #{id}
    </delete>

    <delete id="deleteCompanyByIds" parameterType="String">
        delete from company where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
