<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.audit.mapper.AuditRecordMapper">

    <resultMap type="AuditRecord" id="AuditRecordResult">
        <result property="id"    column="id"    />
        <result property="auditId"    column="audit_id"    />
        <result property="auditPerson"    column="audit_person"    />
        <result property="time"    column="time"    />
        <result property="opinion"    column="opinion"    />
        <result property="auditResult"    column="audit_result"    />
        <result property="auditType"    column="audit_type"    />
        <result property="candidateType"    column="candidate_type"    />
        <result property="corpName"    column="corp_name"    />
        <result property="cityFile"    column="city_file"    />
        <result property="isSee"    column="is_see"    />
    </resultMap>

    <sql id="selectAuditRecordVo">
        select id, audit_id, audit_person, time, opinion, audit_result, audit_type,candidate_type,corp_name,city_file,is_see from audit_record
    </sql>

    <select id="selectAuditRecordList" parameterType="AuditRecord" resultMap="AuditRecordResult">
        select a.audit_id,a.time,a.opinion,a.audit_result,a.audit_type,a.corp_name,a.city_file,a.is_see,u.user_name as audit_person from audit_record a left join sys_user u on a.audit_person = u.user_id
        <where>
            <if test="auditId != null  and auditId != ''"> and a.audit_id = #{auditId}</if>
            <if test="auditPerson != null  and auditPerson != ''"> and audit_person = #{auditPerson}</if>
            <if test="time != null "> and a.time = #{time}</if>
            <if test="opinion != null  and opinion != ''"> and a.opinion = #{opinion}</if>
            <if test="auditResult != null "> and a.audit_result = #{auditResult}</if>
            <if test="auditType != null "> and a.audit_type = #{auditType}</if>
            <if test="corpName != null "> and a.corp_name = #{corpName}</if>
            <if test="isSee != null "> and a.is_see = #{isSee}</if>
        </where>
        order by time desc
    </select>

    <select id="selectAuditRecordById" parameterType="String" resultMap="AuditRecordResult">
        <include refid="selectAuditRecordVo"/>
        where id = #{id}
    </select>
    <select id="selectAuditRecordListByauditId"  resultMap="AuditRecordResult">
        <include refid="selectAuditRecordVo"/>
        where audit_id = #{auditId}
    </select>
    <select id="selectAuditRecordByAuditId" resultType="com.tzstcl.audit.domain.AuditRecord">
        <include refid="selectAuditRecordVo">
        </include>
        where audit_id = #{auditId} order by time desc limit 1
    </select>

    <insert id="insertAuditRecord" parameterType="AuditRecord">
        insert into audit_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="auditId != null and auditId != ''">audit_id,</if>
            <if test="auditPerson != null and auditPerson != ''">audit_person,</if>
            <if test="time != null">time,</if>
            <if test="opinion != null and opinion != ''">opinion,</if>
            <if test="auditResult != null">audit_result,</if>
            <if test="auditType != null">audit_type,</if>
            <if test="candidateType != null">candidate_type,</if>
            <if test="corpName != null">corp_name,</if>
            <if test="cityFile != null">city_file,</if>
            <if test="isSee != null">is_see,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="auditId != null and auditId != ''">#{auditId},</if>
            <if test="auditPerson != null and auditPerson != ''">#{auditPerson},</if>
            <if test="time != null">#{time},</if>
            <if test="opinion != null and opinion != ''">#{opinion},</if>
            <if test="auditResult != null">#{auditResult},</if>
            <if test="auditType != null">#{auditType},</if>
            <if test="candidateType != null">#{candidateType},</if>
            <if test="corpName != null">#{corpName},</if>
            <if test="cityFile != null">#{cityFile},</if>
            <if test="isSee != null">#{isSee},</if>
         </trim>
    </insert>

    <update id="updateAuditRecord" parameterType="AuditRecord">
        update audit_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="auditId != null and auditId != ''">audit_id = #{auditId},</if>
            <if test="auditPerson != null and auditPerson != ''">audit_person = #{auditPerson},</if>
            <if test="time != null">time = #{time},</if>
            <if test="opinion != null and opinion != ''">opinion = #{opinion},</if>
            <if test="auditResult != null">audit_result = #{auditResult},</if>
            <if test="auditType != null">audit_type = #{auditType},</if>
            <if test="candidateType != null">candidate_type = #{candidateType},</if>
            <if test="corpName != null">corp_name = #{corpName}</if>
            <if test="cityFile != null">city_file = #{cityFile}</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAuditRecordById" parameterType="String">
        delete from audit_record where id = #{id}
    </delete>

    <delete id="deleteAuditRecordByIds" parameterType="String">
        delete from audit_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
