<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.audit.mapper.AuditMapper">

    <resultMap type="Audit" id="AuditResult">
        <result property="id"    column="id"    />
        <result property="sponsor"    column="sponsor"    />
        <result property="time"    column="time"    />
        <result property="hanlder"    column="hanlder"    />
        <result property="number"    column="number"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="candidateType"    column="candidate_type"    />
        <result property="idCard"    column="id_card"    />
        <result property="auditNumber"    column="auditNumber"    />
    </resultMap>

    <resultMap type="Audit" id="AuditResult1">
        <result property="id"    column="id"    />
        <result property="sponsor"    column="sponsor"    />
        <result property="time"    column="time"    />
        <result property="hanlder"    column="hanlder"    />
        <result property="number"    column="number"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="candidateType"    column="candidate_type"    />
        <collection property="auditRecord" column="{id=audit_id,candidate_type=candidate_type}" select="com.tzstcl.audit.mapper.AuditRecordMapper.selectAuditRecordList"/>
    </resultMap>

    <sql id="selectAuditVo">
        select id, sponsor, time, hanlder, number, audit_status,candidate_type,id_card,auditNumber from audit
    </sql>

    <select id="selectAuditList" parameterType="Audit" resultMap="AuditResult">
        <include refid="selectAuditVo"/>
        <where>
            <if test="sponsor != null  and sponsor != ''"> and sponsor = #{sponsor}</if>
            <if test="time != null "> and time >= #{time}</if>
            <if test="hanlder != null  and hanlder != ''"> and hanlder = #{hanlder}</if>
            <if test="number != null  and number != ''"> and number = #{number}</if>
            <if test="auditStatus != null "> and audit_status = #{auditStatus}</if>
            <if test="candidateType != null "> and candidate_type = #{candidateType}</if>
            <if test="idCard != null "> and id_card = #{idCard}</if>
            <if test="auditNumber != null "> and auditNumber = #{auditNumber}</if>
        </where>
        Order by time desc
    </select>

    <select id="selectAuditById" parameterType="String" resultMap="AuditResult">
        <include refid="selectAuditVo"/>
        where id = #{id}
    </select>
    <select id="getOneAudit" resultMap="AuditResult">
        SELECT
            a.id,
            a.audit_status,
            a.number
        FROM
            audit a
        WHERE
            a.id_card = #{idCard}
          AND a.candidate_type = #{candidateType}
        ORDER BY
            a.time DESC
            LIMIT 1
    </select>
    <select id="selectAuditListByAuditPerson" resultType="com.tzstcl.person.domain.AuditPersonVo">
        SELECT
            s.user_name as idCard,
            s.nick_name as username,
            ar.time as time,
	ar.opinion as opinion,
	ar.audit_person as auditPerson,
	a1.candidate_type as candidateType
        FROM
            audit a1
            INNER JOIN ( SELECT MAX( time ) AS time FROM audit GROUP BY sponsor, candidate_type ) a2 ON a1.time = a2.time
            INNER JOIN sys_user s ON s.user_id = a1.sponsor
            LEFT JOIN (
            SELECT
            ar1.*
            FROM
            audit_record ar1
            INNER JOIN ( SELECT MAX( time ) AS time FROM audit_record GROUP BY audit_id ) ar2 ON ar1.time = ar2.time
            ) AS ar ON a1.id = ar.audit_id
        WHERE
            a1.audit_status = 3
        GROUP BY  idCard,candidateType
    </select>
    <select id="selectAuditListByIdCard" resultType="java.lang.Integer">
        SELECT
            MAX( auditNumber ) AS number
        FROM
            audit
        WHERE
            candidate_type = #{candidateType}
          AND id_card = #{idCard}
        <if test="time != null "> and time >= #{time}</if>

    </select>
    <select id="selectAuditListBySponson" resultType="java.lang.String">
        SELECT
            sponsor
        FROM
            audit
        GROUP BY
            sponsor
    </select>


    <!--    <select id="selectAuditListByIdCardandType" parameterType="Audit" resultMap="AuditResult1">-->
<!--        select * from audit a1-->
<!--                          LEFT JOIN sys_user s1 ON a1.sponsor=s1.user_id-->
<!--                          LEFT JOIN audit_record a2 ON a2.audit_id=a1.id-->
<!--        where s1.user_name=#{} and a1.candidate_type='2'-->
<!--    </select>-->

    <insert id="insertAudit" parameterType="Audit">
        insert into audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="sponsor != null and sponsor != ''">sponsor,</if>
            <if test="time != null">time,</if>
            <if test="hanlder != null and hanlder != ''">hanlder,</if>
            <if test="number != null and number != ''">number,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="candidateType != null">candidate_type,</if>
            <if test="idCard != null">id_card,</if>
            <if test="auditNumber != null">auditNumber,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="sponsor != null and sponsor != ''">#{sponsor},</if>
            <if test="time != null">#{time},</if>
            <if test="hanlder != null and hanlder != ''">#{hanlder},</if>
            <if test="number != null and number != ''">#{number},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="candidateType != null">#{candidateType},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="auditNumber != null">#{auditNumber},</if>
         </trim>
    </insert>

    <update id="updateAudit" parameterType="Audit">
        update audit
        <trim prefix="SET" suffixOverrides=",">
            <if test="sponsor != null and sponsor != ''">sponsor = #{sponsor},</if>
            <if test="time != null">time = #{time},</if>
            <if test="hanlder != null and hanlder != ''">hanlder = #{hanlder},</if>
            <if test="number != null and number != ''">number = #{number},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="candidateType != null">candidate_type = #{candidateType},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="auditNumber != null">auditNumber = #{auditNumber},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateAuditBysponsor">
        update audit
        set id_card = #{idCard}
        where sponsor = #{sponsor}
    </update>

    <delete id="deleteAuditById" parameterType="String">
        delete from audit where id = #{id}
    </delete>

    <delete id="deleteAuditByIds" parameterType="String">
        delete from audit where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
