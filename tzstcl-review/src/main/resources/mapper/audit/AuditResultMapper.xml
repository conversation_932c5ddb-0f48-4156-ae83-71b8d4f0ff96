<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.audit.mapper.AuditResultMapper">
    
    <resultMap type="AuditResult" id="AuditResultResult">
        <result property="id"    column="id"    />
        <result property="createTime"    column="create_time"    />
        <result property="cityFile"    column="city_file"    />
        <result property="auditPerson"    column="audit_person"    />
    </resultMap>

    <sql id="selectAuditResultVo">
        select id, create_time, city_file, audit_person from audit_result
    </sql>

    <select id="selectAuditResultList" parameterType="AuditResult" resultMap="AuditResultResult">
        <include refid="selectAuditResultVo"/>
        <where>  
            <if test="cityFile != null  and cityFile != ''"> and city_file = #{cityFile}</if>
            <if test="auditPerson != null  and auditPerson != ''"> and audit_person = #{auditPerson}</if>
        </where>
    </select>
    
    <select id="selectAuditResultById" parameterType="Long" resultMap="AuditResultResult">
        <include refid="selectAuditResultVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAuditResult" parameterType="AuditResult">
        insert into audit_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="cityFile != null">city_file,</if>
            <if test="auditPerson != null">audit_person,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="cityFile != null">#{cityFile},</if>
            <if test="auditPerson != null">#{auditPerson},</if>
         </trim>
    </insert>

    <update id="updateAuditResult" parameterType="AuditResult">
        update audit_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="cityFile != null">city_file = #{cityFile},</if>
            <if test="auditPerson != null">audit_person = #{auditPerson},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAuditResultById" parameterType="Long">
        delete from audit_result where id = #{id}
    </delete>

    <delete id="deleteAuditResultByIds" parameterType="String">
        delete from audit_result where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>