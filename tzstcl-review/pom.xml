<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>post-qualification-review</artifactId>
        <groupId>com.tzstcl</groupId>
        <version>3.8.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>post-qualification-review-review</artifactId>

    <description>
        注册类资格后审系统
    </description>

    <dependencies>
        <!-- json -->
<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>fastjson</artifactId>-->
<!--            <version>1.2.47</version>-->
<!--        </dependency>-->

        <!-- lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.24</version>
        </dependency>

        <!--通用工具-->
        <dependency>
            <groupId>com.tzstcl</groupId>
            <artifactId>post-qualification-review-common</artifactId>
        </dependency>


    </dependencies>

</project>