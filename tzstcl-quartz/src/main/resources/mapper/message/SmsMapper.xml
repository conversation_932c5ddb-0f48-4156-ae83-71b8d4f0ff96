<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.message.mapper.SmsMapper">

    <resultMap type="Sms" id="SmsResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
        <result property="userId"    column="user_id"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectSmsVo">
        select id, title, content, create_time, user_id, del_flag from sms
    </sql>

    <select id="selectSmsList" parameterType="Sms" resultMap="SmsResult">
        <include refid="selectSmsVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%',#{title},'%') </if>
            <if test="content != null  and content != ''"> and content like concat('%', #{content},'%') </if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectSmsById" parameterType="Integer" resultMap="SmsResult">
        <include refid="selectSmsVo"/>
        where id = #{id}
    </select>

    <insert id="insertSms" parameterType="Sms" useGeneratedKeys="true" keyProperty="id">
        insert into sms
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updateSms" parameterType="Sms">
        update sms
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSmsById" parameterType="Integer">
        delete from sms where id = #{id}
    </delete>

    <delete id="deleteSmsByIds" parameterType="String">
        delete from sms where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
