<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.message.mapper.SmsSendMapper">

    <resultMap type="SmsSend" id="SmsSendResult">
        <result property="id"    column="id"    />
        <result property="titleId"    column="title_id"    />
        <result property="content"    column="content"    />
        <result property="number"    column="number"    />
        <result property="phone"    column="phone"    />
        <result property="filePath"    column="file_path"    />
        <result property="sendType"    column="send_type"    />
        <result property="sendTime"    column="send_time"    />
        <result property="sendStatus"    column="send_status"    />
        <result property="userId"    column="user_id"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectSmsSendVo">
        select id, title_id, content, number, phone, file_path, send_type, send_time, send_status, user_id, del_flag from sms_send
    </sql>

    <select id="selectSmsSendList" parameterType="SmsSend" resultMap="SmsSendResult">
        <include refid="selectSmsSendVo"/>
        <where>
            <if test="titleId != null "> and title_id = #{titleId}</if>
            <if test="content != null  and content != ''"> and content like  concat('%', #{content},'%')</if>
            <if test="number != null "> and number = #{number}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="sendType != null "> and send_type = #{sendType}</if>
            <if test="sendTime != null "> and send_time = #{sendTime}</if>
            <if test="sendStatus != null "> and send_status = #{sendStatus}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectSmsSendById" parameterType="Integer" resultMap="SmsSendResult">
        <include refid="selectSmsSendVo"/>
        where id = #{id}
    </select>

    <insert id="insertSmsSend" parameterType="SmsSend" useGeneratedKeys="true" keyProperty="id">
        insert into sms_send
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="titleId != null">title_id,</if>
            <if test="content != null">content,</if>
            <if test="number != null">number,</if>
            <if test="phone != null">phone,</if>
            <if test="filePath != null">file_path,</if>
            <if test="sendType != null">send_type,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="sendStatus != null">send_status,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="titleId != null">#{titleId},</if>
            <if test="content != null">#{content},</if>
            <if test="number != null">#{number},</if>
            <if test="phone != null">#{phone},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="sendType != null">#{sendType},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="sendStatus != null">#{sendStatus},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updateSmsSend" parameterType="SmsSend">
        update sms_send
        <trim prefix="SET" suffixOverrides=",">
            <if test="titleId != null">title_id = #{titleId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="number != null">number = #{number},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="sendType != null">send_type = #{sendType},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="sendStatus != null">send_status = #{sendStatus},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSmsSendById" parameterType="Integer">
        delete from sms_send where id = #{id}
    </delete>

    <delete id="deleteSmsSendByIds" parameterType="String">
        delete from sms_send where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
