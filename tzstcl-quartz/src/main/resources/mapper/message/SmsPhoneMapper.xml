<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzstcl.message.mapper.SmsPhoneMapper">

    <resultMap type="SmsPhone" id="SmsPhoneResult">
        <result property="id"    column="id"    />
        <result property="sendId"    column="send_id"    />
        <result property="phone"    column="phone"    />
        <result property="jobId"    column="job_id"    />
    </resultMap>

    <sql id="selectSmsPhoneVo">
        select id, send_id, phone from sms_phone
    </sql>

    <select id="selectSmsPhoneList" parameterType="SmsPhone" resultMap="SmsPhoneResult">
        <include refid="selectSmsPhoneVo"/>
        <where>
            <if test="sendId != null  and sendId != ''"> and send_id = #{sendId}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="jobId != null  and jobId != ''"> and job_id = #{jobId}</if>
        </where>
    </select>

    <select id="selectSmsPhoneById" parameterType="String" resultMap="SmsPhoneResult">
        <include refid="selectSmsPhoneVo"/>
        where id = #{id}
    </select>
    <select id="selectAllSmsPhone" resultType="java.lang.String">
        select phone from sms_phone where send_id = #{sendId}
    </select>
    <select id="selectJobIdList" resultType="java.lang.Long">
        select job_id from sms_phone where send_id = #{sendId}
    </select>

    <insert id="insertSmsPhone" parameterType="SmsPhone">
        insert into sms_phone
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="sendId != null">send_id,</if>
            <if test="phone != null">phone,</if>
            <if test="jobId != null">job_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="sendId != null">#{sendId},</if>
            <if test="phone != null">#{phone},</if>
            <if test="jobId != null">#{jobId},</if>
        </trim>
    </insert>

    <update id="updateSmsPhone" parameterType="SmsPhone">
        update sms_phone
        <trim prefix="SET" suffixOverrides=",">
            <if test="sendId != null">send_id = #{sendId},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="jobId != null">job_id = #{jobId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSmsPhoneById" parameterType="String">
        delete from sms_phone where id = #{id}
    </delete>

    <delete id="deleteSmsPhoneByIds" parameterType="String">
        delete from sms_phone where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertSmsPhoneBatch">
        insert into sms_phone (send_id, phone, job_id)
        values
        <foreach item="phone" index="index" collection="list" separator=",">
            (#{phone.sendId}, #{phone.phone}, #{phone.jobId})
        </foreach>
    </insert>
</mapper>
