package com.tzstcl.quartz.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.tzstcl.common.utils.StringUtils;
import com.tzstcl.common.utils.http.HttpUtils;
import com.tzstcl.company.domain.Company;
import com.tzstcl.company.service.ICompanyService;
import com.tzstcl.quartz.service.UpdateCompanyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 公司：天筑科技股份有限公司
 * 作者：wzh
 * 日期：XX年XX月XX日
 * 说明：
 */
@Service
@Slf4j
public class UpdateCompanyServiceImpl implements UpdateCompanyService {
    @Value("${Skypt.port}")
    public String port;
    @Value(("${Skypt.host}"))
    public String host;

    @Autowired
    private ICompanyService companyService;

    @Override
    public void update() {
        //请求参数
        String param = "sysId=" + "20210123" + "&secretKey=" + "2f6d29d660d44ac78a264ca5c68381d0";
        //企业信息
        String url =" http://" + host + ":" + port + "/credit/corpinfo";
        String result = HttpUtils.sendGet(url, param);
        List<Company> companyList = JSONArray.parseArray(result).toList(Company.class);
        int insert = 0;
        int update =0;
        for (Company company : companyList) {
            try {
                company.setCorpCode(StringUtils.trim(company.getCorpCode()));
                company.setCorpName(StringUtils.trim(company.getCorpName()));
                //判断该企业是否存在
                Company companyInfo  = companyService.getByCorpCode(company.getCorpCode());
                if(companyInfo==null){
                    insert += companyService.insertCompany(company);
                }else {
                    if(!company.equals(companyInfo)){//如果不同则更新
                        update += companyService.updateByCorpCode(company);
                    }
                }
            } catch (Exception e) {
                log.info("{}===同步信息失败",company.getCorpCode());
            }
        }
        log.info("新增:{};更新:{}",insert,update);
    }
}
