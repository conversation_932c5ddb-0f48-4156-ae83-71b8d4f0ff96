package com.tzstcl.quartz.task;


import com.mascloud.sdkclient.Client;
import com.tzstcl.message.domain.Sms;
import com.tzstcl.message.domain.SmsSend;
import com.tzstcl.message.mapper.SmsMapper;
import com.tzstcl.message.service.ISmsSendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.UUID;


/**
 * <Description> <br>
 * 发送短信的定时任务
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @date 2023/08/24 <br>
 */
@Component("timedSend")
public class TimedSend {

    @Autowired
    private ISmsSendService smsSendService;


    public void updateSmsSendTaskStatus(String phone,Integer sendId) {

        SmsSend smsSend = smsSendService.selectSmsSendById(sendId);
        String[] phoneNum = phone.split(",");

        smsSendService.send(phoneNum,smsSend,1);
    }

}
