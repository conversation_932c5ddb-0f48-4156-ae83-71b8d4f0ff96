package com.tzstcl.quartz.task;


import com.tzstcl.audit.domain.Audit;
import com.tzstcl.audit.mapper.AuditMapper;
import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.person.domain.AuditPersonVo;
import com.tzstcl.person.domain.PersonInfo;
import com.tzstcl.person.mapper.PersonInfoMapper;
import com.tzstcl.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("updatePersonAuditPerson")
public class UpdatePersonAuditPerson {
    @Autowired
    private AuditMapper auditMapper;
    @Autowired
    private PersonInfoMapper personInfoMapper;
    @Autowired
    private SysUserMapper sysUserMapper;


    public void updatePersonAuditPerson(){
        List<AuditPersonVo> auditPersonVos = auditMapper.selectAuditListByAuditPerson();
        auditPersonVos.stream().forEach(auditPersonVo -> {
            Long auditPerson = auditPersonVo.getAuditPerson();
            if (auditPerson != null){
                SysUser sysUser = sysUserMapper.selectUserById(auditPerson);
                if(sysUser != null){
                    auditPersonVo.setAuditPersonName(sysUser.getUserName());
                }
                personInfoMapper.updatePersonInfoAuditPersonByIdCard(auditPersonVo);
            }
        });
    }

    public void updateAuditIdCard(){
        List<String> list = auditMapper.selectAuditListBySponson();
        list.stream().forEach(sponsor->{
            if (sponsor != null){
                SysUser sysUser = sysUserMapper.selectUserById(Long.valueOf(sponsor));
                if(sysUser != null){
                    auditMapper.updateAuditBysponsor(sponsor,sysUser.getUserName());
                }
            }
        });
    }
    public void updateAuditNumber(){
        Audit audit = new Audit();
        List<Audit> list = auditMapper.selectAuditList(audit);
        list.stream().forEach(item->{
            String number = item.getNumber();
            if("初次申请".equals(number)){
                item.setAuditNumber(1);
                auditMapper.updateAudit(item);
            }else if("二次申请".equals(number)) {
                item.setAuditNumber(2);
                auditMapper.updateAudit(item);
            }else if("三次申请".equals(number)) {
                item.setAuditNumber(3);
                auditMapper.updateAudit(item);
            }else if("四次申请".equals(number)) {
                item.setAuditNumber(4);
                auditMapper.updateAudit(item);
            }else if("五次申请".equals(number)) {
                item.setAuditNumber(5);
                auditMapper.updateAudit(item);
            }else if("六次申请".equals(number)) {
                item.setAuditNumber(6);
                auditMapper.updateAudit(item);
            }else if("七次申请".equals(number)) {
                item.setAuditNumber(7);
                auditMapper.updateAudit(item);
            }else if("八次申请".equals(number)) {
                item.setAuditNumber(8);
                auditMapper.updateAudit(item);
            }else if("九次申请".equals(number)) {
                item.setAuditNumber(9);
                auditMapper.updateAudit(item);
            }else if("十次申请".equals(number)) {
                item.setAuditNumber(10);
                auditMapper.updateAudit(item);
            } else if("十一次申请".equals(number)) {
                item.setAuditNumber(11);
                auditMapper.updateAudit(item);
            }else if("十二次申请".equals(number)) {
                item.setAuditNumber(12);
                auditMapper.updateAudit(item);
            }else if("十三次申请".equals(number)) {
                item.setAuditNumber(13);
                auditMapper.updateAudit(item);
            }else if("十四次申请".equals(number)) {
                item.setAuditNumber(14);
                auditMapper.updateAudit(item);
            }
        });
    }

    public void updateAuditStatus(){
        PersonInfo personInfo = new PersonInfo();
        List<PersonInfo> personInfos = personInfoMapper.selectPersonInfoList(personInfo);
        personInfos.stream().forEach(item->{
            String idCard = item.getIdCard();
            Integer candidateType = item.getCandidateType();
            Audit audit = auditMapper.getOneAudit(idCard,candidateType);
            if(audit != null){
                item.setAuditStatus(audit.getAuditStatus());
                item.setAuditResult(1);
                personInfoMapper.updatePersonInfo(item);
            }else {
                item.setAuditStatus(9);
                item.setAuditResult(0);
                personInfoMapper.updatePersonInfo(item);
            }
        });
    }




}
