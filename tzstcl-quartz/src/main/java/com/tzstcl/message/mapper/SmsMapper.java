package com.tzstcl.message.mapper;

import com.tzstcl.message.domain.Sms;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 发送短信模板Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-25
 */
@Mapper
public interface SmsMapper
{
    /**
     * 查询发送短信模板
     *
     * @param id 发送短信模板主键
     * @return 发送短信模板
     */
    public Sms selectSmsById(Integer id);

    /**
     * 查询发送短信模板列表
     *
     * @param sms 发送短信模板
     * @return 发送短信模板集合
     */
    public List<Sms> selectSmsList(Sms sms);

    /**
     * 新增发送短信模板
     *
     * @param sms 发送短信模板
     * @return 结果
     */
    public int insertSms(Sms sms);

    /**
     * 修改发送短信模板
     *
     * @param sms 发送短信模板
     * @return 结果
     */
    public int updateSms(Sms sms);

    /**
     * 删除发送短信模板
     *
     * @param id 发送短信模板主键
     * @return 结果
     */
    public int deleteSmsById(Integer id);

    /**
     * 批量删除发送短信模板
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSmsByIds(Integer[] ids);
}
