package com.tzstcl.message.mapper;

import com.tzstcl.message.domain.SmsSend;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 短信发送Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-25
 */
@Mapper
public interface SmsSendMapper
{
    /**
     * 查询短信发送
     *
     * @param id 短信发送主键
     * @return 短信发送
     */
    public SmsSend selectSmsSendById(Integer id);

    /**
     * 查询短信发送列表
     *
     * @param smsSend 短信发送
     * @return 短信发送集合
     */
    public List<SmsSend> selectSmsSendList(SmsSend smsSend);

    /**
     * 新增短信发送
     *
     * @param smsSend 短信发送
     * @return 结果
     */
    public int insertSmsSend(SmsSend smsSend);

    /**
     * 修改短信发送
     *
     * @param smsSend 短信发送
     * @return 结果
     */
    public int updateSmsSend(SmsSend smsSend);

    /**
     * 删除短信发送
     *
     * @param id 短信发送主键
     * @return 结果
     */
    public int deleteSmsSendById(Integer id);

    /**
     * 批量删除短信发送
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSmsSendByIds(Integer[] ids);
}
