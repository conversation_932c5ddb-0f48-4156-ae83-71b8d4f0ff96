package com.tzstcl.message.mapper;

import com.tzstcl.message.domain.SmsPhone;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 接收手机号记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-25
 */
@Mapper
public interface SmsPhoneMapper
{
    /**
     * 查询接收手机号记录
     *
     * @param id 接收手机号记录主键
     * @return 接收手机号记录
     */
    public SmsPhone selectSmsPhoneById(Integer id);

    /**
     * 查询接收手机号记录列表
     *
     * @param smsPhone 接收手机号记录
     * @return 接收手机号记录集合
     */
    public List<SmsPhone> selectSmsPhoneList(SmsPhone smsPhone);

    /**
     * 新增接收手机号记录
     *
     * @param smsPhone 接收手机号记录
     * @return 结果
     */
    public int insertSmsPhone(SmsPhone smsPhone);

    /**
     * 修改接收手机号记录
     *
     * @param smsPhone 接收手机号记录
     * @return 结果
     */
    public int updateSmsPhone(SmsPhone smsPhone);

    /**
     * 删除接收手机号记录
     *
     * @param id 接收手机号记录主键
     * @return 结果
     */
    public int deleteSmsPhoneById(Integer id);

    /**
     * 批量删除接收手机号记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSmsPhoneByIds(int[] ids);

    /**
     * 批量插入手机号

     * @param phones 手机号集合
     */
    void insertSmsPhoneBatch( List<SmsPhone> phones);

    /**
     * 根据发送id获取该id下所有的手机号
     * @param sendId 发送id
     * @return 结果
     */
    List<String> selectAllSmsPhone(Integer sendId);

    /**
     * 根据发送id查询出jobId
     * @param sendId  发送id
     * @return 结果
     */
    List<Long> selectJobIdList(Integer sendId);
}
