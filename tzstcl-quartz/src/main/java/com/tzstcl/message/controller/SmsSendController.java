package com.tzstcl.message.controller;

import com.tzstcl.common.annotation.Log;
import com.tzstcl.common.core.controller.BaseController;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.common.core.page.TableDataInfo;
import com.tzstcl.common.core.redis.RedisCache;
import com.tzstcl.common.enums.BusinessType;
import com.tzstcl.common.utils.SecurityUtils;
import com.tzstcl.common.utils.poi.ExcelUtil;
import com.tzstcl.message.domain.SmsSend;
import com.tzstcl.message.service.ISmsSendService;
import com.tzstcl.system.service.impl.SysUserServiceImpl;
import com.tzstcl.uploadFile.util.MinioUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.Security;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;

/**
 * 短信发送Controller
 *
 * <AUTHOR>
 * @date 2023-08-25
 */
@RestController
@RequestMapping("/message/send")
public class SmsSendController extends BaseController {
    @Autowired
    private ISmsSendService smsSendService;
    @Autowired
    private SysUserServiceImpl sysUserService;
    @Autowired
    MinioUtils minioUtils;
    @Autowired
    private RedisCache redisCache;
    //@Log(title = "发送短信模板", businessType = BusinessType.EXPORT)
    @PostMapping("/sendSms")
    public AjaxResult sendSms(@RequestBody SysUser sysUser) {
        String userName = sysUser.getUserName();
        SysUser sysUser1 = sysUserService.selectUserByUserName(userName);
        if (null == sysUser1) {
            return AjaxResult.error("请输入正确的考生身份证号！");
        }
        if (sysUser1.getStatus().equals("1")){
            return AjaxResult.error("核查已结束。");
        }
        logger.info("sysUser.getUserName{}",sysUser.getUserName());
        String checkCode = RandomStringUtils.randomAlphanumeric(6).toUpperCase();
        Integer count = redisCache.getCacheObject(userName + "Count");
        if (null == count) {
            redisCache.setCacheObject(userName + "Count", 1,  getRemainSecondsOneDay(new Date()), TimeUnit.SECONDS);
        } else if (count < 10 && count > 0) {
            redisCache.setCacheObject(userName + "Count", count + 1,  getRemainSecondsOneDay(new Date()), TimeUnit.SECONDS   );
        } else {
            return AjaxResult.error("今日登录已超三次，请明日再登录!");
        }
        redisCache.setCacheObject(userName + "phoneCode", checkCode, getRemainSecondsOneDay(new Date()), TimeUnit.SECONDS);
        String[] phoneNum = {sysUser1.getPhonenumber()};
        logger.info("短信开始发送{}",phoneNum);
        int result = smsSendService.sendSms(phoneNum, "您的验证码为:" + checkCode + ",只限本次登录有效,未登录成功前，本次验证码一直有效，请勿重复发送。本验证码将用于河南省建设类职业资格考试审核登陆操作,请勿泄露!");
        logger.error("验证码为：{}",checkCode);
        if (result == 0) {
            return AjaxResult.error("发送短信异常，请联系工作人员！");
        }
        return AjaxResult.success();
    }

    public static Integer getRemainSecondsOneDay(Date currentDate) {
//使用plusDays加传入的时间加1天，将时分秒设置成0
        LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
                        ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
                .withSecond(0).withNano(0);
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault());
//使用ChronoUnit.SECONDS.between方法，传入两个LocalDateTime对象即可得到相差的秒数
        long seconds = ChronoUnit.SECONDS.between(currentDateTime, midnight);
        return (int) seconds;
    }

    /**
     * 查询短信发送列表
     */
    @PreAuthorize("@ss.hasPermi('system:send:list')")
    @GetMapping("/list")
    public TableDataInfo list(SmsSend smsSend) {
        startPage();
        List<SmsSend> list = smsSendService.selectSmsSendList(smsSend);
        return getDataTable(list);
    }

    /**
     * 导出短信发送列表
     */
   /* @PreAuthorize("@ss.hasPermi('system:send:export')")
    @Log(title = "短信发送", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SmsSend smsSend) {
        List<SmsSend> list = smsSendService.selectSmsSendList(smsSend);
        ExcelUtil<SmsSend> util = new ExcelUtil<SmsSend>(SmsSend.class);
        util.exportExcel(response, list, "短信发送数据");
    }*/

    /**
     * 获取短信发送详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:send:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(smsSendService.selectSmsSendById(id));
    }

    /**
     * 新增短信发送
     */
//    @PreAuthorize("@ss.hasPermi('system:send:add')")
//    @Log(title = "短信发送", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody SmsSend smsSend) {
//        return toAjax(smsSendService.insertSmsSend(smsSend));
//    }

    /**
     * 修改短信发送
     */
    @PreAuthorize("@ss.hasPermi('system:send:edit')")
    @Log(title = "短信发送", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SmsSend smsSend) {
        return toAjax(smsSendService.updateSmsSend(smsSend));
    }

    /**
     * 删除短信发送
     */
    @PreAuthorize("@ss.hasPermi('system:send:remove')")
    @Log(title = "短信发送", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(smsSendService.deleteSmsSendByIds(ids));
    }


    /**
     * 新增短信发送
     */

//    @PostMapping("send")
//    public AjaxResult sendSms(
//            @RequestParam(name = "file",required = false) MultipartFile file,
//           SmsSend smsSend) {
//
//        smsSend.setUserId(getUserId().toString());
//        return success(smsSendService.smsSend(file, smsSend));
//
//
//    }
    @PostMapping()
    public AjaxResult sendSmsByFile(
            @RequestParam(value = "file", required = false) MultipartFile file,
            @RequestParam(value = "sendType") Long sendType,
            @RequestParam(value = "titleId") Integer titleId,
            @RequestParam(value = "phone", required = false) String phone,
            @RequestParam(value = "sendTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date sendTime
    ) {
        SmsSend smsSend = new SmsSend();
        smsSend.setSendTime(sendTime);
        smsSend.setSendType(sendType);
        smsSend.setTitleId(titleId);
        smsSend.setPhone(phone);
        smsSend.setUserId(getUserId().toString());
        return success(smsSendService.smsSend(file, smsSend));


    }


    /**
     * 上传考生相关资料
     */
    @PostMapping("/up")
    public AjaxResult up(MultipartFile file) throws Exception {
        //允许上传的文件格式
        String[] extensions = {"xls", "xlsx"};

        int flag = 0;
        for (String extension : extensions) {
            if (extension.equals(MinioUtils.getExtension(file))) {
                flag = 1;
            }
        }
        if (flag == 0) {
            return AjaxResult.error("只允许上传xls或xlsx文件");
        }

        if (file != null) {
            return AjaxResult.success(minioUtils.uploadFile(file));
        }
        return error("上传异常，请联系管理员");
    }


    /**
     * 下载模板
     */
    @PostMapping("importTemplate")
    public void downloadTemplate(@RequestParam("path") String path, HttpServletResponse response, HttpServletRequest request) {
        try {
            minioUtils.download(path, response, request);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

}
