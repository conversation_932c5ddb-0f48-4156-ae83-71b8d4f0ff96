package com.tzstcl.message.controller;

import com.tzstcl.common.annotation.Log;
import com.tzstcl.common.core.controller.BaseController;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.core.page.TableDataInfo;
import com.tzstcl.common.enums.BusinessType;
import com.tzstcl.common.utils.poi.ExcelUtil;
import com.tzstcl.message.service.ISmsService;
import com.tzstcl.message.domain.Sms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 发送短信模板Controller
 *
 * <AUTHOR>
 * @date 2023-08-25
 */
@RestController
@RequestMapping("/message/sms")
public class SmsController extends BaseController {

    @Autowired
    private ISmsService smsService;

    /**
     * 查询发送短信模板列表
     */
    @PreAuthorize("@ss.hasPermi('system:sms:list')")
    @GetMapping("/list")
    public TableDataInfo list(Sms sms) {
        startPage();
        List<Sms> list = smsService.selectSmsList(sms);
        return getDataTable(list);
    }
    /**
     * 查询发送短信模板列表
     */
    @PreAuthorize("@ss.hasPermi('system:sms:list')")
    @GetMapping("/listBySend")
    public AjaxResult list2(Sms sms) {
        List<Sms> list = smsService.selectSmsList(sms);
        return success(list);
    }

  /*  *//**
     * 导出发送短信模板列表
     *//*
    @PreAuthorize("@ss.hasPermi('system:sms:export')")
    @Log(title = "发送短信模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Sms sms) {
        List<Sms> list = smsService.selectSmsList(sms);
        ExcelUtil<Sms> util = new ExcelUtil<Sms>(Sms.class);
        util.exportExcel(response, list, "发送短信模板数据");
    }*/

    /**
     * 获取发送短信模板详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:sms:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(smsService.selectSmsById(id));
    }

    /**
     * 新增发送短信模板
     */
    @PreAuthorize("@ss.hasPermi('system:sms:add')")
    @Log(title = "发送短信模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Sms sms) {
        return toAjax(smsService.insertSms(sms));
    }

    /**
     * 修改发送短信模板
     */
    @PreAuthorize("@ss.hasPermi('system:sms:edit')")
    @Log(title = "发送短信模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Sms sms) {
        return toAjax(smsService.updateSms(sms));
    }

    /**
     * 删除发送短信模板
     */
    @PreAuthorize("@ss.hasPermi('system:sms:remove')")
    @Log(title = "发送短信模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(smsService.deleteSmsByIds(ids));
    }
}
