package com.tzstcl.message.controller;

import com.tzstcl.common.annotation.Log;
import com.tzstcl.common.core.controller.BaseController;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.core.page.TableDataInfo;
import com.tzstcl.common.enums.BusinessType;
import com.tzstcl.common.utils.poi.ExcelUtil;
import com.tzstcl.message.domain.SmsPhone;
import com.tzstcl.message.service.ISmsPhoneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 接收手机号记录Controller
 *
 * <AUTHOR>
 * @date 2023-08-25
 */
@RestController
@RequestMapping("/message/phone")
public class SmsPhoneController extends BaseController
{
    @Autowired
    private ISmsPhoneService smsPhoneService;

    /**
     * 查询接收手机号记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:phone:list')")
    @GetMapping("/list")
    public TableDataInfo list(SmsPhone smsPhone)
    {
        startPage();
        List<SmsPhone> list = smsPhoneService.selectSmsPhoneList(smsPhone);
        return getDataTable(list);
    }

    /**
     * 导出接收手机号记录列表
     */
 /*   @PreAuthorize("@ss.hasPermi('system:phone:export')")
    @Log(title = "接收手机号记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SmsPhone smsPhone)
    {
        List<SmsPhone> list = smsPhoneService.selectSmsPhoneList(smsPhone);
        ExcelUtil<SmsPhone> util = new ExcelUtil<SmsPhone>(SmsPhone.class);
        util.exportExcel(response, list, "接收手机号记录数据");
    }
*/
    /**
     * 获取接收手机号记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:phone:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(smsPhoneService.selectSmsPhoneById(id));
    }

    /**
     * 新增接收手机号记录
     */
    @PreAuthorize("@ss.hasPermi('system:phone:add')")
    @Log(title = "接收手机号记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SmsPhone smsPhone)
    {
        return toAjax(smsPhoneService.insertSmsPhone(smsPhone));
    }

    /**
     * 修改接收手机号记录
     */
    @PreAuthorize("@ss.hasPermi('system:phone:edit')")
    @Log(title = "接收手机号记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SmsPhone smsPhone)
    {
        return toAjax(smsPhoneService.updateSmsPhone(smsPhone));
    }

    /**
     * 删除接收手机号记录
     */
    @PreAuthorize("@ss.hasPermi('system:phone:remove')")
    @Log(title = "接收手机号记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable int[] ids)
    {
        return toAjax(smsPhoneService.deleteSmsPhoneByIds(ids));
    }
}
