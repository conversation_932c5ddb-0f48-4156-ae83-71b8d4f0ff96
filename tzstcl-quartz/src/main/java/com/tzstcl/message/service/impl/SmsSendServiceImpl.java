package com.tzstcl.message.service.impl;

import cn.hutool.poi.excel.ExcelReader;
import com.mascloud.sdkclient.Client;
import com.tzstcl.common.constant.ScheduleConstants;
import com.tzstcl.common.utils.DateUtils;
import com.tzstcl.common.utils.StringUtils;
import com.tzstcl.message.domain.Sms;
import com.tzstcl.message.domain.SmsPhone;
import com.tzstcl.message.domain.SmsSend;
import com.tzstcl.message.mapper.SmsMapper;
import com.tzstcl.message.mapper.SmsPhoneMapper;
import com.tzstcl.message.mapper.SmsSendMapper;
import com.tzstcl.message.service.ISmsSendService;
import com.tzstcl.quartz.domain.SysJob;
import com.tzstcl.quartz.mapper.SysJobMapper;
import com.tzstcl.quartz.service.ISysJobService;
import com.tzstcl.uploadFile.util.MinioUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 短信发送Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-25
 */

@Service
@Slf4j
public class SmsSendServiceImpl implements ISmsSendService {


    @Value("${mas.url}")
    String url;
    @Value("${mas.userAccount}")
    String userAccount;
    @Value("${mas.password}")
    String password;
    @Value("${mas.ecname}")
    String ecname;
    @Value("${mas.sign}")
    String sign;
    @Value("${mas.serviceNo}")
    String serviceNo;
    @Value("${mas.orderNo}")
    String orderNo;


    @Autowired
    private MinioUtils minioUtils;
    @Autowired
    private SmsPhoneMapper smsPhoneMapper;
    @Autowired
    private SmsMapper smsMapper;
    @Autowired
    private SmsSendMapper smsSendMapper;
    @Autowired
    private ISysJobService sysJobService;

    Client client;


    @PostConstruct
    public void clientInit() {
        client = Client.getInstance();
        client.login(url, userAccount, password, ecname);
    }

    @Override
    public int sendSms(String[] phoneNos, String content) {
        try {
            log.info("短信开始发送222{}",phoneNos);
            log.info("client{}",client);
            int result = client.sendDSMS(phoneNos, content, "", 5, sign, UUID.randomUUID().toString().replaceAll("-", ""), true);
            log.info("result{}",result);
            return result;
        } catch (Exception e) {
            log.error("短信发送异常",e);
            return 0;
        }
    }

    /**
     * 查询短信发送
     *
     * @param id 短信发送主键
     * @return 短信发送
     */
    @Override
    public SmsSend selectSmsSendById(Integer id) {
        SmsSend smsSend = smsSendMapper.selectSmsSendById(id);
        List<String> phones = smsPhoneMapper.selectAllSmsPhone(id);
        smsSend.setPhone(phones.toString());
        return smsSend;
    }

    /**
     * 查询短信发送列表
     *
     * @param smsSend 短信发送
     * @return 短信发送
     */
    @Override
    public List<SmsSend> selectSmsSendList(SmsSend smsSend) {
        return smsSendMapper.selectSmsSendList(smsSend);
    }

    /**
     * 新增短信发送
     *
     * @param smsSend 短信发送
     * @return 结果
     */
    @Override
    public int insertSmsSend(SmsSend smsSend) {
        return smsSendMapper.insertSmsSend(smsSend);
    }

    /**
     * 修改短信发送
     *
     * @param smsSend 短信发送
     * @return 结果
     */
    @Override
    public int updateSmsSend(SmsSend smsSend) {
        return smsSendMapper.updateSmsSend(smsSend);
    }

    /**
     * 批量删除短信发送
     *
     * @param ids 需要删除的短信发送主键
     * @return 结果
     */
    @Override
    public int deleteSmsSendByIds(Integer[] ids) {
        return smsSendMapper.deleteSmsSendByIds(ids);
    }

    /**
     * 删除短信发送信息
     *
     * @param id 短信发送主键
     * @return 结果
     */
    @Override
    public int deleteSmsSendById(Integer id) {
        return smsSendMapper.deleteSmsSendById(id);
    }


    /**
     * @param file    手机号码excle文件
     * @param smsSend 短信发送实体类信息
     * @return 返回结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String smsSend(MultipartFile file, SmsSend smsSend) {
        String result = "";

        smsSend.setSendStatus(1L);
        //判断上传文件为空返回
        if (file == null && smsSend.getPhone() == null) {
            return "上传文件和手机号不能为空";
        }
        //存读取到的内容
        List<List<Object>> readAll = new ArrayList<>();
        if (smsSend.getPhone() != null) {
            //处理用户手动输入的手机号
            //获取用户手动输入的手机号
            String[] split = smsSend.getPhone().split("，");
            //将用户输入的手机号与读取文件的手机号合并
            for (int i = 0; i < split.length; i++) {
                List<Object> list = Arrays.asList(split[i]);
                readAll.add(list);
            }
        }
        if (file != null) {
            //判断文件是否是excel文件
            String filename = file.getOriginalFilename();
            if (!StringUtils.isEmpty(filename) && (!filename.matches("^.+\\.(?i)(xls)$")
                    && !filename.matches("^.+\\.(?i)(xlsx)$"))) {
                return "上传文件格式错误，请上传后缀为.xls或.xlsx的文件";
            }
            //读取文件
            ExcelReader readerExcel = null;
            try {
//            readerExcel = ExcelUtil.getReader(file.getInputStream());
                readerExcel = cn.hutool.poi.excel.ExcelUtil.getReader(file.getInputStream());
            } catch (Exception e) {
                log.error("读取手机号文件异常:{ }", e);
                e.printStackTrace();
            }
            //读取表头 （可以用来判断是否是自己系统想要接收的excel）
            List<Object> firstRow = readerExcel.readRow(0);
            if (firstRow.size() > 1) {
                return "长度不匹配";
            }
            //为了防止文件过大读取时内存溢出我们可以分批次进行读取
            //获取总条数
            int total = readerExcel.getRowCount();
            //开始索引 一般第一行是表头 所以我们从1开始
            int startIndex = 0;
            //结束索引
            int endIndex = 500;
            //每次读取多少行
            int batchSize = 500;
            //记录批次
            int batch = 1;


            //开始读取 当结束索引大于总条数时结束
            while (endIndex < total) {
                //添加到处理集合中
                readAll.addAll(readerExcel.read(startIndex, endIndex));
                //定义一个方法做逻辑处理
                //读取规则刷新赋值
                startIndex = endIndex + 1;
                batch++;
                endIndex = batch * batchSize;
                if (endIndex > total) {
                    endIndex = total;
                }
            }
            //处理最后一页
            //添加到处理集合中
            readAll.addAll(readerExcel.read(startIndex, endIndex));

            //上传文件
            //保存路径
            smsSend.setFilePath(minioUtils.uploadFile(file));

        }

        //保存发送总条数
        smsSend.setNumber((long) readAll.size());


        //统一进行处理发送短信,如果是定时发送则返回jobid，如果是实时发送则返回发送结果
        Object res = readResult(readAll, smsSend);
        Long jobId = 0L;
        if (smsSend.getSendType() == 1) {
            jobId = (Long) res;
            result = jobId > 0 ? "定时发送任务创建成功" : "定时发送任务创建异常";
        } else if (smsSend.getSendType() == 0) {
            result = (String) res;
        }
        // 保存接收的手机号记录
        //查询保存后的发送记录id作为手机号码记录表的批次记录id，
        Integer sendId = smsSendMapper.selectSmsSendList(smsSend).get(0).getId();
        savePhone(jobId, sendId, readAll);

        return result;
    }

    private void savePhone(Long jobId, Integer sendId, List<List<Object>> readAll) {
        //处理手机号
        List<SmsPhone> resultPhone = new ArrayList<>(readAll.size());
        readAll.stream().forEach(r ->

        {
            SmsPhone smsPhone = new SmsPhone();
            smsPhone.setPhone(r.get(0).toString());
            smsPhone.setSendId(sendId);
            smsPhone.setJobId(jobId);
            resultPhone.add(smsPhone);
        });
        //批量保存手机号
        smsPhoneMapper.insertSmsPhoneBatch(resultPhone);
    }

    private Object readResult(List<List<Object>> readAll, SmsSend smsSend) {
        //定义接收结果
        final Object[] result = new Object[1];
        //分批发送
        int applyIdSelectSize = 500;
        int limit = (readAll.size() + applyIdSelectSize - 1) / applyIdSelectSize;
        //分成limit次发请求到数据库，in（）操作时 可以把多条数据分割成多组请求
        Stream.iterate(0, n -> n + 1).limit(limit).forEach(a -> {
            //获取后面1000条中的前500条
            // 拿到这个参数的流的 （a * applyIdSelectSize）后面的数据 .limit（applyIdSelectSize）->后面数据的500条 .collect(Collectors.toList()->组成一个toList
            List<List<Object>> iphone = readAll.stream().skip(a * applyIdSelectSize).limit(applyIdSelectSize).collect(Collectors.toList());

            String[] phoneNum = new String[iphone.size()];

            System.out.println("------------------------------------------" + phoneNum.length);
            for (int i = 0; i < iphone.size(); i++) {
                phoneNum[i] = iphone.get(i).get(0).toString();
                System.out.println(phoneNum[i]);
            }
            System.out.println("------------------------------------------");
            //发送短信
            //根据模板标题查询模板内容
            Sms sms = smsMapper.selectSmsById(smsSend.getTitleId());
            //获取短信内容模板
            String content = sms.getContent();
            //同时添加到记录表中
            smsSend.setContent(content);

            //判断定时发送还是实时发送
            if (smsSend.getSendType() == 0) {
                //实时发送
                int sendRes = send(phoneNum, smsSend, 0);
                //保存发送时间
                smsSend.setSendTime(DateUtils.getNowDate());
                //保存发送记录
                smsSendMapper.insertSmsSend(smsSend);

                result[0] = "实时发送成功";
            } else if (smsSend.getSendType() == 1) {
                //保存记录
                smsSend.setSendStatus(0L);
                //保存发送记录
                smsSendMapper.insertSmsSend(smsSend);
                result[0] = timeSend(phoneNum, smsSend);
            }
        });

        return result[0];
    }

    private Long timeSend(String[] phoneNum, SmsSend smsSend) {
        //定时发送

        //将手机号转换为字符串
        StringBuilder stringBuilder = new StringBuilder();
        for (String p : phoneNum) {
            stringBuilder.append(p).append(",");
        }

        //新建定时任务
        SysJob job = new SysJob();
        String invokeTarget = "timedSend.updateSmsSendTaskStatus"
                + "('" + stringBuilder.deleteCharAt(stringBuilder.length() - 1).toString() + "',"
                + smsSend.getId().toString() + ")";


        job.setInvokeTarget(invokeTarget);
        job.setJobName("发送短信定时任务" + DateUtils.getNowDate());
        job.setJobGroup("DEFAULT");
        job.setCreateBy(smsSend.getUserId());
        job.setCreateTime(DateUtils.getNowDate());
        job.setCronExpression(cron(smsSend.getSendTime()));
        job.setMisfirePolicy("3");
        job.setConcurrent("1");
        job.setStatus(ScheduleConstants.Status.NORMAL.getValue());

        try {
            sysJobService.insertJob(job);

        } catch (Exception e) {
            log.error("定时任务创建异常", e);
            return -1L;
        }
        return job.getJobId();
    }

    private String cron(Date date) {

        // 创建一个Calendar对象，并将Date设置为要转换的时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 获取年、月、日、小时、分钟的值
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1; // 注意：Calendar中的月份是从0开始计数的，所以要加1
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        int second = calendar.get(Calendar.SECOND);

        // 拼接Cron表达式

        return String.format("%d %d %d %d %d ? %d", second, minute, hour, day, month, year);
    }

    @Override
    public int send(String[] phoneNum, SmsSend smsSend, int isTime) {
        if (isTime != 0) {
            //查询定时任务id
            List<Long> jobIdList = smsPhoneMapper.selectJobIdList(smsSend.getId());

            try {
                if (jobIdList != null && jobIdList.get(0) != 0) {
                    SysJob sysJob = sysJobService.selectJobById(jobIdList.get(0));
                    sysJob.setStatus(ScheduleConstants.Status.PAUSE.getValue());
                    sysJobService.changeStatus(sysJob);
                }
            } catch (Exception e) {
                log.error("定时任务异常", e);
            }
        }

        log.info("发送成功....");
        //修改发送状态为发送成功
        smsSend.setSendStatus(1L);
        smsSendMapper.updateSmsSend(smsSend);
        //return 0;//测试时，屏蔽发送短信
        try {

            int result = client.sendDSMS(phoneNum, smsSend.getContent(), "", 5, sign, UUID.randomUUID().toString().replaceAll("-", ""), true);
            if (result == 1) {
                //修改发送状态为发送成功
                smsSend.setSendStatus(1L);
                smsSendMapper.updateSmsSend(smsSend);
                return result;
            } else {
                throw new Exception("发送失败，错误代码：" + result);
            }
        } catch (Exception e) {
            //log.error("短信发送异常",e);
            smsSend.setSendStatus(2L);
            //smsSendMapper.insertSmsSend(smsSend);
            return -1;
        }


    }

}

