package com.tzstcl.message.service;

import com.tzstcl.message.domain.SmsPhone;

import java.util.List;

/**
 * 接收手机号记录Service接口
 *
 * <AUTHOR>
 * @date 2023-08-25
 */
public interface ISmsPhoneService
{
    /**
     * 查询接收手机号记录
     *
     * @param id 接收手机号记录主键
     * @return 接收手机号记录
     */
    public SmsPhone selectSmsPhoneById(Integer id);

    /**
     * 查询接收手机号记录列表
     *
     * @param smsPhone 接收手机号记录
     * @return 接收手机号记录集合
     */
    public List<SmsPhone> selectSmsPhoneList(SmsPhone smsPhone);

    /**
     * 新增接收手机号记录
     *
     * @param smsPhone 接收手机号记录
     * @return 结果
     */
    public int insertSmsPhone(SmsPhone smsPhone);

    /**
     * 修改接收手机号记录
     *
     * @param smsPhone 接收手机号记录
     * @return 结果
     */
    public int updateSmsPhone(SmsPhone smsPhone);

    /**
     * 批量删除接收手机号记录
     *
     * @param ids 需要删除的接收手机号记录主键集合
     * @return 结果
     */
    public int deleteSmsPhoneByIds(int[] ids);

    /**
     * 删除接收手机号记录信息
     *
     * @param id 接收手机号记录主键
     * @return 结果
     */
    public int deleteSmsPhoneById(Integer id);
}
