package com.tzstcl.message.service.impl;

import com.tzstcl.common.utils.DateUtils;
import com.tzstcl.message.service.ISmsService;
import com.tzstcl.message.domain.Sms;
import com.tzstcl.message.mapper.SmsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 发送短信模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-25
 */
@Service
public class SmsServiceImpl implements ISmsService {


    @Autowired
    private SmsMapper smsMapper;

    /**
     * 查询发送短信模板
     *
     * @param id 发送短信模板主键
     * @return 发送短信模板
     */
    @Override
    public Sms selectSmsById(Integer id) {
        return smsMapper.selectSmsById(id);
    }

    /**
     * 查询发送短信模板列表
     *
     * @param sms 发送短信模板
     * @return 发送短信模板
     */
    @Override
    public List<Sms> selectSmsList(Sms sms) {
        return smsMapper.selectSmsList(sms);
    }

    /**
     * 新增发送短信模板
     *
     * @param sms 发送短信模板
     * @return 结果
     */
    @Override
    public int insertSms(Sms sms) {
        sms.setCreateTime(DateUtils.getNowDate());
        return smsMapper.insertSms(sms);
    }

    /**
     * 修改发送短信模板
     *
     * @param sms 发送短信模板
     * @return 结果
     */
    @Override
    public int updateSms(Sms sms) {
        return smsMapper.updateSms(sms);
    }

    /**
     * 批量删除发送短信模板
     *
     * @param ids 需要删除的发送短信模板主键
     * @return 结果
     */
    @Override
    public int deleteSmsByIds(Integer[] ids) {
        return smsMapper.deleteSmsByIds(ids);
    }

    /**
     * 删除发送短信模板信息
     *
     * @param id 发送短信模板主键
     * @return 结果
     */
    @Override
    public int deleteSmsById(Integer id) {
        return smsMapper.deleteSmsById(id);
    }
}
