package com.tzstcl.message.service.impl;

import com.tzstcl.message.domain.SmsPhone;
import com.tzstcl.message.mapper.SmsPhoneMapper;
import com.tzstcl.message.service.ISmsPhoneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 接收手机号记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-25
 */
@Service
public class SmsPhoneServiceImpl implements ISmsPhoneService
{
    @Autowired
    private SmsPhoneMapper smsPhoneMapper;

    /**
     * 查询接收手机号记录
     *
     * @param id 接收手机号记录主键
     * @return 接收手机号记录
     */
    @Override
    public SmsPhone selectSmsPhoneById(Integer id)
    {
        return smsPhoneMapper.selectSmsPhoneById(id);
    }

    /**
     * 查询接收手机号记录列表
     *
     * @param smsPhone 接收手机号记录
     * @return 接收手机号记录
     */
    @Override
    public List<SmsPhone> selectSmsPhoneList(SmsPhone smsPhone)
    {
        return smsPhoneMapper.selectSmsPhoneList(smsPhone);
    }

    /**
     * 新增接收手机号记录
     *
     * @param smsPhone 接收手机号记录
     * @return 结果
     */
    @Override
    public int insertSmsPhone(SmsPhone smsPhone)
    {
        return smsPhoneMapper.insertSmsPhone(smsPhone);
    }

    /**
     * 修改接收手机号记录
     *
     * @param smsPhone 接收手机号记录
     * @return 结果
     */
    @Override
    public int updateSmsPhone(SmsPhone smsPhone)
    {
        return smsPhoneMapper.updateSmsPhone(smsPhone);
    }

    /**
     * 批量删除接收手机号记录
     *
     * @param ids 需要删除的接收手机号记录主键
     * @return 结果
     */
    @Override
    public int deleteSmsPhoneByIds(int[] ids)
    {
        return smsPhoneMapper.deleteSmsPhoneByIds(ids);
    }

    /**
     * 删除接收手机号记录信息
     *
     * @param id 接收手机号记录主键
     * @return 结果
     */
    @Override
    public int deleteSmsPhoneById(Integer id)
    {
        return smsPhoneMapper.deleteSmsPhoneById(id);
    }
}
