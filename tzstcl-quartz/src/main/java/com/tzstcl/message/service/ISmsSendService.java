package com.tzstcl.message.service;

import com.tzstcl.message.domain.SmsSend;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 短信发送Service接口
 *
 * <AUTHOR>
 * @date 2023-08-25
 */
public interface ISmsSendService {
    public int sendSms(String[] phoneNos, String content);

    /**
     * 查询短信发送
     *
     * @param id 短信发送主键
     * @return 短信发送
     */
    public SmsSend selectSmsSendById(Integer id);

    /**
     * 查询短信发送列表
     *
     * @param smsSend 短信发送
     * @return 短信发送集合
     */
    public List<SmsSend> selectSmsSendList(SmsSend smsSend);

    /**
     * 新增短信发送
     *
     * @param smsSend 短信发送
     * @return 结果
     */
    public int insertSmsSend(SmsSend smsSend);

    /**
     * 修改短信发送
     *
     * @param smsSend 短信发送
     * @return 结果
     */
    public int updateSmsSend(SmsSend smsSend);

    /**
     * 批量删除短信发送
     *
     * @param ids 需要删除的短信发送主键集合
     * @return 结果
     */
    public int deleteSmsSendByIds(Integer[] ids);

    /**
     * 删除短信发送信息
     *
     * @param id 短信发送主键
     * @return 结果
     */
    public int deleteSmsSendById(Integer id);

    /**
     * 发送短信
     *
     * @param file
     * @param smsSend 发送短信实体
     * @return 结果
     */
    public String smsSend(MultipartFile file, SmsSend smsSend);

    /**
     * 短信发送
     *
     * @param phoneNum 需要的手机号
     * @param smsSend  发送类
     * @return 结果
     */
    public int send(String[] phoneNum, SmsSend smsSend, int isTime);
}
