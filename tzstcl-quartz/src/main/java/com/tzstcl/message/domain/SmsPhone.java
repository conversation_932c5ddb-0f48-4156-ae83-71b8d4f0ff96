package com.tzstcl.message.domain;

import com.tzstcl.common.annotation.Excel;
import com.tzstcl.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 接收手机号记录对象 sms_phone
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
public class SmsPhone extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 短信发送的id */
    @Excel(name = "短信发送的id")
    private Integer sendId;

    /** 接受短信的手机号 */
    @Excel(name = "接受短信的手机号")
    private String phone;

    /** 定时任务的id  */
    @Excel(name = "定时任务id")
    private Long jobId;

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }
    public void setSendId(Integer sendId)
    {
        this.sendId = sendId;
    }

    public Integer getSendId()
    {
        return sendId;
    }
    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getPhone()
    {
        return phone;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sendId", getSendId())
            .append("phone", getPhone())
            .append("jobId", getJobId())
            .toString();
    }
}
