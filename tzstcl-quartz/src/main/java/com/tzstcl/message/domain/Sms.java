package com.tzstcl.message.domain;

import com.tzstcl.common.annotation.Excel;
import com.tzstcl.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 发送短信模板对象 sms
 *
 * <AUTHOR>
 * @date 2023-08-25
 */
public class Sms extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 模板标题 */
    @Excel(name = "模板标题")
    private String title;

    /** 模板内容 */
    @Excel(name = "模板内容")
    private String content;

    /** 创建人id */
    @Excel(name = "创建人id")
    private String userId;

    /** 删除标记(0未删除 1已删除) */
    private Long delFlag;

    public void setId(Integer id)
    {
        this.id = id;
    }

    public Integer getId()
    {
        return id;
    }
    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setUserId(String userId)
    {
        this.userId = userId;
    }

    public String getUserId()
    {
        return userId;
    }
    public void setDelFlag(Long delFlag)
    {
        this.delFlag = delFlag;
    }

    public Long getDelFlag()
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("title", getTitle())
                .append("content", getContent())
                .append("createTime", getCreateTime())
                .append("userId", getUserId())
                .append("delFlag", getDelFlag())
                .toString();
    }
}
