package com.tzstcl.message.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tzstcl.common.annotation.Excel;
import com.tzstcl.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 短信发送对象 sms_send
 *
 * <AUTHOR>
 * @date 2023-08-25
 */
public class SmsSend extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 标题id */
    @Excel(name = "标题id")
    private Integer titleId;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 发送条数 */
    @Excel(name = "发送条数")
    private Long number;

    /** 手动输入的手机号 */
    @Excel(name = "手动输入的手机号")
    private String phone;

    /** 手机号文件存储路径 */
    @Excel(name = "手机号文件存储路径")
    private String filePath;

    /** 发送方式：0实时；1定时 */
    @Excel(name = "发送方式：0实时；1定时")
    private Long sendType;

    /** 发送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sendTime;

    /** 发送状态：0待发送；1发送成功；2发送失败 */
    @Excel(name = "发送状态：0待发送；1发送成功；2发送失败")
    private Long sendStatus;

    /** 发送人 */
    @Excel(name = "发送人")
    private String userId;

    /** 删除标记(0未删除 1已删除) */
    private Long delFlag;

    public void setId(Integer id)
    {
        this.id = id;
    }

    public Integer getId()
    {
        return id;
    }
    public void setTitleId(Integer titleId)
    {
        this.titleId = titleId;
    }

    public Integer getTitleId()
    {
        return titleId;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setNumber(Long number)
    {
        this.number = number;
    }

    public Long getNumber()
    {
        return number;
    }
    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getPhone()
    {
        return phone;
    }
    public void setFilePath(String filePath)
    {
        this.filePath = filePath;
    }

    public String getFilePath()
    {
        return filePath;
    }
    public void setSendType(Long sendType)
    {
        this.sendType = sendType;
    }

    public Long getSendType()
    {
        return sendType;
    }
    public void setSendTime(Date sendTime)
    {
        this.sendTime = sendTime;
    }

    public Date getSendTime()
    {
        return sendTime;
    }
    public void setSendStatus(Long sendStatus)
    {
        this.sendStatus = sendStatus;
    }

    public Long getSendStatus()
    {
        return sendStatus;
    }
    public void setUserId(String userId)
    {
        this.userId = userId;
    }

    public String getUserId()
    {
        return userId;
    }
    public void setDelFlag(Long delFlag)
    {
        this.delFlag = delFlag;
    }

    public Long getDelFlag()
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("titleId", getTitleId())
                .append("content", getContent())
                .append("number", getNumber())
                .append("phone", getPhone())
                .append("filePath", getFilePath())
                .append("sendType", getSendType())
                .append("sendTime", getSendTime())
                .append("sendStatus", getSendStatus())
                .append("userId", getUserId())
                .append("delFlag", getDelFlag())
                .toString();
    }
}
