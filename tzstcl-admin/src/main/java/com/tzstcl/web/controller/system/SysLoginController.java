package com.tzstcl.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.tzstcl.common.constant.Constants;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.core.domain.entity.SysMenu;
import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.common.core.domain.model.LoginBody;
import com.tzstcl.common.core.domain.model.ResetBody;
import com.tzstcl.common.core.domain.model.ResetPwdBody;
import com.tzstcl.common.utils.PasswordUtils;
import com.tzstcl.common.utils.SecurityUtils;
import com.tzstcl.framework.web.service.SysLoginService;
import com.tzstcl.framework.web.service.SysPermissionService;
import com.tzstcl.person.service.IPersonInfoService;
import com.tzstcl.system.service.ISysMenuService;
import com.tzstcl.system.service.ISysRoleService;
import com.tzstcl.system.service.ISysUserService;
import com.tzstcl.web.controller.tool.MD5Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController {
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private IPersonInfoService personInfoService;

    @Value("${Skypt.host}")
    private String host;
    @Value("${Skypt.port}")
    private String port;
    @Value("${Skypt.appid}")
    private String appid;
    @Value("${Skypt.appkey}")
    private String appkey;
    @Value("${Skypt.targetUrl}")
    private String targetUrl;


    private static final Logger logger = LoggerFactory.getLogger(SysLoginController.class);

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) {
        SysUser sysUser = sysUserService.selectUserByUserName(loginBody.getUsername());
        if (sysUser.getUserType().equals("03") && "" == loginBody.getPhoneCode()) {
            return AjaxResult.error("请考生从考生入口登录!");
        }
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid(), loginBody.getPhoneCode());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 登录方法
     *
     * @param resetBody 弱密码重置信息
     * @return 结果
     */
    @PostMapping("/weakResetPwd")
    public AjaxResult weakResetPwd(@RequestBody ResetBody resetBody) {

        //判断是否是弱密码
        if (PasswordUtils.isWeakPassword(resetBody.getNewPassword())) {
            return AjaxResult.error("新密码强度太弱，密码必须包含大写字母、小写字母、特殊符号和数字，长度在 8 到 16 个字符之间");
        }
        String msg = loginService.resetPwd(resetBody);
        return "重置成功".equals(msg) ? AjaxResult.success(msg) : AjaxResult.error(msg);
    }


    /**
     * 重置密码
     *
     * @param resetPwdBody
     * @return
     */
    @PostMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody ResetPwdBody resetPwdBody) {
        AjaxResult ajaxResult = AjaxResult.success("重置密码成功!");
        if (PasswordUtils.isWeakPassword(resetPwdBody.getPassword())) {
            //判断是否是弱密码
            return AjaxResult.error("重置密码失败，密码强度太弱，密码必须包含大写字母、小写字母、特殊符号和数字，长度在 8 到 16 个字符之间");
        }
        try {
            // 生成令牌
            personInfoService.checkResetInfo(resetPwdBody);
            sysUserService.resetUserPwd(resetPwdBody.getUserName(), SecurityUtils.encryptPassword(resetPwdBody.getPassword()));
        } catch (Exception e) {
            ajaxResult = AjaxResult.error(e.getMessage());
        }
        return ajaxResult;
    }

    @RequestMapping("/verifyToken")
    public AjaxResult verifyToken(String token, HttpServletResponse response) throws Exception {
        logger.info("verifyToken()方法！！！！！！！！！");
        System.out.println("verifyToken进来了！！！！！！");
        if (!verify(token)) {
            //跳转到四库一平台登陆页面
            response.sendRedirect(targetUrl);
            return AjaxResult.error("token失效");
        }
        // 1.回调获取用户信息
        String url = "http://" + host + ":" + port + "/auth/getUserInfo?token={token}&appid={appid}&sign={sign}&timestamp={timestamp}";
        // 生成签名
        long timestamp = System.currentTimeMillis();
        String signature = MD5Utils.getMD5String(appkey + "&" + token + "&" + timestamp);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("token", token);
        paramMap.put("appid", appid);
        paramMap.put("sign", signature);
        paramMap.put("timestamp", String.valueOf(timestamp));

        JSONObject result = restTemplate.getForObject(url, JSONObject.class, paramMap);
        String code = result.get("code").toString();
        // 取出Ajax中的数据
        JSONObject data = result.getJSONObject("data");
        JSONObject userInfo = data.getJSONObject("user");
        String userName = userInfo.getString("userName");
        String phoneNumber = userInfo.getString("userMobile");
        String userType = userInfo.getString("userType");
        String socialCreditCode = userInfo.getString("socialCreditCode");
        String certNo = userInfo.getString("certNo");
        String cityCode = userInfo.getString("cityCode");

        //主管部门
        if (userType.equals("04")) {
            SysUser sysUser = sysUserService.selectUserByUserName(userName);
            if (sysUser != null) {
                String password = SecurityUtils.encryptPassword(cityCode + "@Tzkj.");
                System.out.println(cityCode + "@Tzkj.");
                sysUser.setPassword(password);
                sysUserService.resetUserPwd(sysUser.getUserName(), sysUser.getPassword());
                LoginBody loginBody = new LoginBody();
                loginBody.setUsername(userName);
                loginBody.setPassword(cityCode + "@Tzkj.");
                return AjaxResult.success(skyptlogin(loginBody));
            } else {
                SysUser newUser = new SysUser();
                newUser.setUserName(userName);
                newUser.setPassword(cityCode + "@Tzkj.");
                newUser.setPhonenumber(phoneNumber);
                newUser.setDeptId(Long.valueOf(cityCode));
                //默认为个人类型
                //sysUser.setUserType("02");
                newUser.setCreateBy("0");
                newUser.setCreateTime(new Date());
                newUser.setUpdateBy("0");
                newUser.setUpdateTime(new Date());
                try {
                    int i = sysUserService.insertUser(newUser);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                LoginBody loginBody = new LoginBody();
                loginBody.setUsername(userName);
                loginBody.setPassword(cityCode + "@Tzkj.");
                return AjaxResult.success(skyptlogin(loginBody));
            }

        }
        //个人用户
        if (userType.equals("05")) {
            //response.sendRedirect();
            return AjaxResult.error("当前用户是个人用户，请前往学生端登录");
        }
        //注册用户 企业用户
        if (userType.equals("06")) {
            SysUser sysUser = sysUserService.selectUserByUserName(socialCreditCode);
            //企业存在
            if (sysUser != null) {
                String password = SecurityUtils.encryptPassword(userName.substring(userName.length() - 6) + "@Tzkj.");
                sysUser.setPassword(password);
                sysUserService.resetUserPwd(sysUser.getUserName(), sysUser.getPassword());
                LoginBody loginBody = new LoginBody();
                loginBody.setUsername(sysUser.getUserName());
                loginBody.setPassword(userName.substring(userName.length() - 6) + "@Tzkj.");
                return AjaxResult.success(skyptlogin(loginBody));
            } else {
                //先判断是不是建筑业或监理企业，是的话插入数据，返回登录信息，不是的话登录失败

                //不是建筑业监理企业
                //企业不存在
                //如果不存在则新建一个用户
                SysUser newUser = new SysUser();
                newUser.setUserName(socialCreditCode);
                newUser.setNickName(userName);
                //默认密码为统一社会信用代码后六位加上@Tzkj.
                newUser.setPassword(SecurityUtils.encryptPassword(userName.substring(userName.length() - 6) + "@Tzkj."));
                newUser.setPhonenumber(phoneNumber);
                newUser.setDeptId(50000L);
                Long[] roles = {3L};
                newUser.setRoleIds(roles);
                //默认为个人类型
                newUser.setUserType("02");
                newUser.setCreateBy("0");
                newUser.setCreateTime(new Date());
                newUser.setUpdateBy("0");
                newUser.setUpdateTime(new Date());
                try {
                    sysUserService.insertUser(newUser);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                LoginBody loginBody = new LoginBody();
                loginBody.setUsername(userName);
                loginBody.setPassword(userName.substring(userName.length() - 6) + "@Tzkj.");
                return AjaxResult.success(skyptlogin(loginBody));
            }
        }
        //管理员
        if (userType.equals("07")) {
            SysUser sysUser = sysUserService.selectUserByUserName(userName);
            sysUser.setPassword(SecurityUtils.encryptPassword(userName + "@Tzkj."));
            sysUserService.resetUserPwd(sysUser.getUserName(), sysUser.getPassword());
            LoginBody loginBody = new LoginBody();
            loginBody.setUsername(userName);
            loginBody.setPassword(userName + "@Tzkj.");
            return AjaxResult.success(skyptlogin(loginBody));
        }
        return AjaxResult.error();
    }


        /*//根据统一用户传来的token获取user信息，先存储到数据库中,然后让本系统shiro按照原有的逻辑处理token
        SysUser user = loginService.login(token);
        //调用本系统shiro验证信息
        UsernamePasswordToken shiroToken = new UsernamePasswordToken(user.getLoginName(), user.getLoginName());
        Subject subject = SecurityUtils.getSubject();
        try {
            subject.login(shiroToken);
            return "redirect:/index";
        } catch (AuthenticationException e) {
            String msg = "用户或密码错误";
            if (StringUtils.isNotEmpty(e.getMessage())) {
                msg = e.getMessage();
            }
            return "redirect:/unauth";
        }*/

    public AjaxResult skyptlogin(LoginBody loginBody) {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.zwlogin(loginBody.getUsername(), loginBody.getPassword());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    public Boolean verify(String token) {
        String url = "http://" + host + ":" + port + "/auth/verifyToken?token={token}&appid={appid}&sign={sign}&timestamp={timestamp}";
        // 生成签名
        long timestamp = System.currentTimeMillis();
        String signature = MD5Utils.getMD5String(appkey + "&" + token + "&" + timestamp);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("token", token);
        paramMap.put("appid", appid);
        paramMap.put("sign", signature);
        paramMap.put("timestamp", String.valueOf(timestamp));
        logger.info(JSON.toJSONString(paramMap) + "<====请求参数");
        JSONObject result = restTemplate.getForObject(url, JSONObject.class, paramMap);
        logger.info(JSON.toJSONString(result) + "<====响应结果");
        String code = result.get("code").toString();
        if (!"0".equals(code)) {
            return false;
        }
        return true;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters() {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
