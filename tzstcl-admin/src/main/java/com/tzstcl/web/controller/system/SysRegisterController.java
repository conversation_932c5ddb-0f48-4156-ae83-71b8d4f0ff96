package com.tzstcl.web.controller.system;

import com.alibaba.fastjson2.JSONObject;
import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.person.domain.PersonInfo;
import com.tzstcl.person.service.IPersonInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.tzstcl.common.core.controller.BaseController;
import com.tzstcl.common.core.domain.AjaxResult;
import com.tzstcl.common.core.domain.model.RegisterBody;
import com.tzstcl.common.utils.StringUtils;
import com.tzstcl.framework.web.service.SysRegisterService;
import com.tzstcl.system.service.ISysConfigService;

/**
 * 注册验证
 * 
 * <AUTHOR>
 */
@RestController
public class SysRegisterController extends BaseController
{
    @Autowired
    private SysRegisterService registerService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private IPersonInfoService personInfoService;

    @PostMapping("/register")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult register(@RequestBody SysUser user)
    {
//        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser"))))
//        {
//            return error("当前系统没有开启注册功能！");
//        }
        PersonInfo personInfo = personInfoService.checkRegister(user.getUserName());
        if(personInfo==null){
            return AjaxResult.success("该用户非考生，不可注册");
        }
        String msg = registerService.register(user);
        return StringUtils.isEmpty(msg) ? success() : error(msg);
    }
}
