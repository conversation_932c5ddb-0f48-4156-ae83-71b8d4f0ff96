<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 日志存放路径 -->
    <property name="log.path" value="D:/data/logs/review" />
    <!-- 日志输出格式 -->
    <property name="log.pattern" value="%d{HH:mm:ss.SSS} [%thread] %-5level %logger{20} - [%method,%line] - %msg%n" />
    <!--<conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    &lt;!&ndash; 彩色日志格式 &ndash;&gt;
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(-&#45;&#45;){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
-->
    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- 系统日志输出 -->
    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/review-info.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <!--        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
        <!--            &lt;!&ndash; 日志文件名格式 &ndash;&gt;-->
        <!--            <fileNamePattern>${log.path}/sys-info.%d{yyyy-MM-dd}.log</fileNamePattern>-->
        <!--            &lt;!&ndash; 日志最大的历史 60天 &ndash;&gt;-->
        <!--            <maxHistory>60</maxHistory>-->
        <!--        </rollingPolicy>-->
        <!--        <encoder>-->
        <!--            <pattern>${log.pattern}</pattern>-->
        <!--        </encoder>-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件的名字会根据fileNamePattern的值，每隔一段时间改变一次 -->
            <!-- 文件名示例：logs/project_info.2017-12-05.0.log -->
            <!-- 注意：SizeAndTimeBasedRollingPolicy中 ％i和％d令牌都是强制性的，必须存在，否则报错 -->
            <fileNamePattern>${log.path}/review-info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 每产生一个日志文件，该日志文件的保存期限为30天 -->
            <!-- 注：maxHistory的单位是根据fileNamePattern中的翻转策略自动推算出来的，例如上面选用了yyyy-MM-dd，则单位为天，如果上面选用了yyyy-MM，则单位为月。另外上面的单位默认为yyyy-MM-dd-->
            <maxHistory>180</maxHistory>
            <!-- 每个日志文件到100MB的时候开始切分，最多保留30天，但最大到20GB，哪怕没到30天也要删除多余的日志 -->
            <totalSizeCap>200GB</totalSizeCap>
            <!-- maxFileSize:这是活动文件的大小，默认值是100MB，测试时可改成100MB看效果 -->
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <!--编码器-->
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>INFO</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="file_warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/review-warn.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <!--        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
        <!--            &lt;!&ndash; 日志文件名格式 &ndash;&gt;-->
        <!--            <fileNamePattern>${log.path}/sys-info.%d{yyyy-MM-dd}.log</fileNamePattern>-->
        <!--            &lt;!&ndash; 日志最大的历史 60天 &ndash;&gt;-->
        <!--            <maxHistory>60</maxHistory>-->
        <!--        </rollingPolicy>-->
        <!--        <encoder>-->
        <!--            <pattern>${log.pattern}</pattern>-->
        <!--        </encoder>-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件的名字会根据fileNamePattern的值，每隔一段时间改变一次 -->
            <!-- 文件名示例：logs/project_info.2017-12-05.0.log -->
            <!-- 注意：SizeAndTimeBasedRollingPolicy中 ％i和％d令牌都是强制性的，必须存在，否则报错 -->
            <fileNamePattern>${log.path}/review-warn.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 每产生一个日志文件，该日志文件的保存期限为30天 -->
            <!-- 注：maxHistory的单位是根据fileNamePattern中的翻转策略自动推算出来的，例如上面选用了yyyy-MM-dd，则单位为天，如果上面选用了yyyy-MM，则单位为月。另外上面的单位默认为yyyy-MM-dd-->
            <maxHistory>180</maxHistory>
            <!-- 每个日志文件到100MB的时候开始切分，最多保留30天，但最大到20GB，哪怕没到30天也要删除多余的日志 -->
            <totalSizeCap>200GB</totalSizeCap>
            <!-- maxFileSize:这是活动文件的大小，默认值是100MB，测试时可改成100MB看效果 -->
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <!--编码器-->
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>warn</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/review-error.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件的名字会根据fileNamePattern的值，每隔一段时间改变一次 -->
            <!-- 文件名示例：logs/project_info.2017-12-05.0.log -->
            <!-- 注意：SizeAndTimeBasedRollingPolicy中 ％i和％d令牌都是强制性的，必须存在，否则报错 -->
            <fileNamePattern>${log.path}/review-error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 每产生一个日志文件，该日志文件的保存期限为30天 -->
            <!-- 注：maxHistory的单位是根据fileNamePattern中的翻转策略自动推算出来的，例如上面选用了yyyy-MM-dd，则单位为天，如果上面选用了yyyy-MM，则单位为月。另外上面的单位默认为yyyy-MM-dd-->
            <maxHistory>180</maxHistory>
            <!-- 每个日志文件到100MB的时候开始切分，最多保留30天，但最大到20GB，哪怕没到30天也要删除多余的日志 -->
            <totalSizeCap>200GB</totalSizeCap>
            <!-- maxFileSize:这是活动文件的大小，默认值是100MB，测试时可改成100MB看效果 -->
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <!--编码器-->
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>ERROR</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="file_code" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/review-code.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件的名字会根据fileNamePattern的值，每隔一段时间改变一次 -->
            <!-- 文件名示例：logs/project_info.2017-12-05.0.log -->
            <!-- 注意：SizeAndTimeBasedRollingPolicy中 ％i和％d令牌都是强制性的，必须存在，否则报错 -->
            <fileNamePattern>${log.path}/review-code.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 每产生一个日志文件，该日志文件的保存期限为30天 -->
            <!-- 注：maxHistory的单位是根据fileNamePattern中的翻转策略自动推算出来的，例如上面选用了yyyy-MM-dd，则单位为天，如果上面选用了yyyy-MM，则单位为月。另外上面的单位默认为yyyy-MM-dd-->
            <maxHistory>180</maxHistory>
            <!-- 每个日志文件到100MB的时候开始切分，最多保留30天，但最大到20GB，哪怕没到30天也要删除多余的日志 -->
            <totalSizeCap>200GB</totalSizeCap>`
            <!-- maxFileSize:这是活动文件的大小，默认值是100MB，测试时可改成100MB看效果 -->
            <maxFileSize>200MB</maxFileSize>
        </rollingPolicy>
        <!--编码器-->
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>


    <!-- 用户访问日志输出  -->
    <appender name="sys-user" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/review-user.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件的名字会根据fileNamePattern的值，每隔一段时间改变一次 -->
            <!-- 文件名示例：logs/project_info.2017-12-05.0.log -->
            <!-- 注意：SizeAndTimeBasedRollingPolicy中 ％i和％d令牌都是强制性的，必须存在，否则报错 -->
            <fileNamePattern>${log.path}review-user.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 每产生一个日志文件，该日志文件的保存期限为30天 -->
            <!-- 注：maxHistory的单位是根据fileNamePattern中的翻转策略自动推算出来的，例如上面选用了yyyy-MM-dd，则单位为天，如果上面选用了yyyy-MM，则单位为月。另外上面的单位默认为yyyy-MM-dd-->
            <maxHistory>180</maxHistory>
            <!-- 每个日志文件到100MB的时候开始切分，最多保留30天，但最大到20GB，哪怕没到30天也要删除多余的日志 -->
            <totalSizeCap>200GB</totalSizeCap>
            <!-- maxFileSize:这是活动文件的大小，默认值是100MB，测试时可改成100MB看效果 -->
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <!--编码器-->
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- 系统模块日志级别控制  -->
    <logger name="com.tzstcl" level="info" />
    <!-- Spring日志级别控制  -->
    <logger name="org.springframework" level="warn" />

    <root level="info">
        <appender-ref ref="console"/>
    </root>
    <!--系统操作日志-->
    <root level="info">
        <appender-ref ref="file_info" />
        <appender-ref ref="file_error" />
        <appender-ref ref="file_warn"/>
        <appender-ref ref="file_code"/>
    </root>

    <!--系统用户操作日志-->
    <logger name="sys-user" level="info">
        <appender-ref ref="sys-user"/>
    </logger>
</configuration>
