import request from '@/utils/request'

// 查询考生信息列表
export function listInfo(query) {
  return request({
    url: '/person/info/list',
    method: 'get',
    params: query
  })
}

// 查询考生信息详细
export function getInfo(id) {
  return request({
    url: '/person/info/' + id,
    method: 'get'
  })
}

// 新增考生信息
export function addInfo(data) {
  return request({
    url: '/person/info',
    method: 'post',
    data: data
  })
}

// 修改考生信息
export function updateInfo(data) {
  return request({
    url: '/person/info',
    method: 'put',
    data: data
  })
}

// 删除考生信息
export function delInfo(id) {
  return request({
    url: '/person/info/' + id,
    method: 'delete'
  })
}
