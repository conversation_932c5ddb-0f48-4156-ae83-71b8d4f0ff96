/*
?   // get 请求示例
    export function getExample(params) {
        return request({ url: '/example/get', method: 'get', params })
    }
?   // post 请求示例
    export function postExample(data) {
        return request({ url: '/example/post', method: 'post', data })
    }
*/
import request from "@/utils/request";

// 首页数据
export function getData(params) {
  return request({ url: "/department/getData", method: "get", params });
}
// 考生列表
export function getPersonInfo(params) {
  return request({ url: "/department/getPersonInfo", method: "get", params });
}
// 考生导入
export function importData(data) {
  return request({ url: "/person/info/importData", method: "post", data });
}
// 审核列表
export function auditList(params) {
  return request({ url: "/department/auditList", method: "get", params });
}
// 主管部门端审核
export function audit(data) {
  return request({ url: "/department/audit", method: "post", data });
}
// 审核数据导出
export function exportData(data) {
  return request({ url: "/department/exportData", method: "post", data });
}

// 还原按钮
export function reduction(data) {
  return request({ url: "/department/reduction", method: "post", data });
}

// 信息提交
export function clickInfoBtn(data) {
  return request({ url: "/department/adminaudit", method: "post", data });
}

// 禁用注册
export function banRegisterStatus(params) {
  return request({
    url: "/department/banRegisterStatus",
    method: "get",
    params,
  });
}

// 多个禁用注册
export function banRegister(data) {
  return request({ url: "/department/banRegister", method: "post", data });
}

//
export function getPersonInfoByType(params) {
  return request({
    url: "/person/info/getPersonInfoByType",
    method: "get",
    params,
  });
}

// 查询发送短信模板列表
export function listSms(query) {
  return request({ url: "/message/sms/list", method: "get", params: query });
}
export function listBySend(query) {
  return request({
    url: "/message/sms/listBySend",
    method: "get",
    params: query,
  });
}

// 查询发送短信模板详细
export function getSms(id) {
  return request({ url: "/message/sms/" + id, method: "get" });
}

// 新增发送短信模板
export function addSms(data) {
  return request({ url: "/message/sms", method: "post", data: data });
}

// 修改发送短信模板
export function updateSms(data) {
  return request({ url: "/message/sms", method: "put", data: data });
}

// 删除发送短信模板
export function delSms(id) {
  return request({ url: "/message/sms/" + id, method: "delete" });
}

// 查询短信发送列表
export function listSend(query) {
  return request({ url: "/message/send/list", method: "get", params: query });
}

// 查询短信发送详细
export function getSend(id) {
  return request({ url: "/message/send/" + id, method: "get" });
}

// 新增短信发送
export function addSend(data) {
  return request({
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    url: "/message/send",
    method: "post",
    data: data,
  });
}

// 修改短信发送
export function updateSend(data) {
  return request({ url: "/message/send", method: "put", data: data });
}

// 删除短信发送
export function delSend(id) {
  return request({ url: "/message/send/" + id, method: "delete" });
}
