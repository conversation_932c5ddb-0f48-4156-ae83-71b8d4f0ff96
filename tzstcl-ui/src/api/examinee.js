/*
?   // get 请求示例
    export function getExample(params) {
        return request({ url: '/example/get', method: 'get', params })
    }
?   // post 请求示例
    export function postExample(data) {
        return request({ url: '/example/post', method: 'post', data })
    }
*/
import request from "@/utils/request";

// 考生端首页数据反馈
export function getData(params) {
  return request({ url: "/person/info/getData", method: "get", params });
}
// 企业信息列表
export function getCompanyInfoList(params) {
  return request({ url: "/company/info/list", method: "get", params });
}
// 获取考生信息
export function getInfoByIdCard(params) {
  return request({
    url: "/person/info/getInfoByIdCard",
    method: "get",
    params,
  });
}
// 获取考生信息
export function getInfoById(params) {
  return request({ url: "/person/info/getInfoById", method: "get", params });
}
// 修改考生信息
export function editPersonInfo(data) {
  return request({ url: "/person/info/edit", method: "post", data });
}
// 文件上传删除
export function delUp(params) {
  return request({ url: "/uploadFile/delUp", method: "get", params });
}
// 资料上传
export function addData(data) {
  return request({ url: "/person/data/addData", method: "post", data });
}
// 查看资料
export function getPersonData(params) {
  return request({ url: "/person/data/getByIdCard", method: "get", params });
}
// 获取审核记录
export function getAuditLog(params) {
  return request({ url: "/audit/info/getByIdCard", method: "get", params });
}
// 撤销按钮
export function delQuash(params) {
  return request({ url: "/audit/info/revokeAudit", method: "put", params });
}
// 返回信息
export function back(id) {
  return request({ url: `/person/info/back/${id}`, method: "put" });
}