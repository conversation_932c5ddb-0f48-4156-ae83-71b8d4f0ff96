import request from "@/utils/request";

// 查询人员审核全部完成后提交的审核结果列表
export function listResult(query) {
  return request({
    url: "/audit/result/list",
    method: "get",
    params: query,
  });
}

// 查询人员审核全部完成后提交的审核结果详细
export function getResult(id) {
  return request({
    url: "/audit/result/" + id,
    method: "get",
  });
}

// 新增人员审核全部完成后提交的审核结果
export function addResult(data) {
  return request({
    url: "/audit/result",
    method: "post",
    data: data,
  });
}

// 修改人员审核全部完成后提交的审核结果
export function updateResult(data) {
  return request({
    url: "/audit/result",
    method: "put",
    data: data,
  });
}

// 删除人员审核全部完成后提交的审核结果
export function delResult(id) {
  return request({
    url: "/audit/result/" + id,
    method: "delete",
  });
}
