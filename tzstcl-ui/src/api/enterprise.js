/*
?   // get 请求示例
    export function getExample(params) {
        return request({ url: '/example/get', method: 'get', params })
    }
?   // post 请求示例
    export function postExample(data) {
        return request({ url: '/example/post', method: 'post', data })
    }
*/
import request from '@/utils/request'

// 企业信息列表
export function getCompanyInfoList(params) {
    return request({ url: '/company/info/list', method: 'get', params })
}
// 企业审核列表
export function getAuditList(params) {
    return request({ url: '/company/info/getAuditList', method: 'get', params })
}
// 企业审核
export function enterpriseAudit(data) {
    return request({ url: '/company/info/audit', method: 'post', data })
}
// 企业审核统计
export function getData(params) {
    return request({ url: '/company/info/getData', method: 'get', params })
}
// 获取主管部门审核通过考生信息
export function getPassData(params) {
    return request({ url: '/company/info/getPassData', method: 'get', params })
}
