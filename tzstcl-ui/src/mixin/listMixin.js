import { mapGetters } from "vuex";
import { getDicts } from "@/api/system/dict/data.js";
const listMixin = {
  data() {
    return {
      // 列表数据
      list: [],
      candidate: [],
      // 列表遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      daterangeTime: null,
    };
  },
  computed: {
    ...mapGetters(["name", "userId"]),
  },
  created() {
    this.type();
    this.$nextTick(() => {
      this.getList();
    });
  },
  methods: {
    // 获取列表数据
    getList() {
      this.loading = true;
      this.listAPI(this.addDateRange(this.queryParams, this.daterangeTime))
        .then((res) => {
          this.list = res.rows || res.data;
          this.total = res.total || res.rows?.length || res.data?.length || 0;
          // console.log("this.list", this.list);
          this.list.forEach((v) => {
            this.candidate.forEach((item) => {
              if (v.candidateType == item.dictValue) {
                v.candidateType = item.dictLabel;
              }
            });
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    type() {
      getDicts("candidate_type").then((res) => {
        this.candidate.push(...res.data);
      });
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮操作
    resetQuery() {
      const filterList = ["pageNum", "pageSize"].concat(this.resetIgnore);
      for (let key in this.queryParams) {
        if (!filterList.includes(key)) {
          this.queryParams[key] = undefined;
        }
      }
      this.daterangeTime = [];
      this.handleQuery();
    },
  },
};
export default listMixin;
