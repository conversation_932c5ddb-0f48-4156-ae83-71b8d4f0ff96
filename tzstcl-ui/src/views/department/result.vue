<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="审核部门" prop="auditPerson">
        <el-input
          v-model="queryParams.auditPerson"
          placeholder="请输入审核部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" v-if="user == '04'">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:result:edit']"
        >
          修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:result:remove']"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:result:export']"
        >
          导出
        </el-button>
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="resultList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="序号" type="index" align="center" width="55" />

      <el-table-column label="审核部门" align="center" prop="auditPerson" />
      <el-table-column label="附件" align="center" prop="cityFile">
        <template slot-scope="scope">
          <div class="uploadBox">
            <FileUpload v-model="scope.row.cityFile" :disabled="true" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        v-if="user == '04'"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 导入对话框 -->
    <resultFile
      :show="open"
      :importData="importData"
      @close="open = false"
      @getList="getList"
    />
  </div>
</template>

<script>
import {
  listResult,
  getResult,
  delResult,
  addResult,
  updateResult,
} from "@/api/system/result";
import resultFile from "@/views/department/resultFile";
import { getToken } from "@/utils/auth";
import FileUpload from "@/views/components/FileUpload/FileUpload";
export default {
  components: { resultFile, FileUpload },
  name: "Result",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 人员审核全部完成后提交的审核结果表格数据
      resultList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        cityFile: null,
        auditPerson: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      headers: { Authorization: "Bearer " + getToken() },
      isUploading: false,

      // 导入数据
      importData: {
        // 弹出层标题
        title: "新增审核附件",
        // 模板下载地址
        templateUrl: "/uploadFile/importTemplate",
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/audit/result",
      },
      user: "",
    };
  },
  created() {
    this.getList();
    this.user = sessionStorage.getItem("user");
  },
  methods: {
    /** 查询人员审核全部完成后提交的审核结果列表 */
    getList() {
      this.loading = true;
      listResult(this.queryParams).then((response) => {
        this.resultList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        createTime: null,
        cityFile: null,
        auditPerson: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加人员审核全部完成后提交的审核结果";
    },
    // 下载模板操作
    importTemplate() {
      const nowDate = this.parseTime(new Date(), "{y}-{m}-{d}_{h}-{i}-{s}");
      this.download(
        this.importData.templateUrl,
        { path: "手机号导入模板.xlsx" },
        `${this.importData.title}模板_${nowDate}.xlsx`
      );
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.dialogVisible = false;
      this.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.$emit("importResult", response);
      this.$emit("getList");
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          `是否确认删除审核部门为${row.auditPerson}创建时间为${row.createTime}的数据项？`
        )
        .then(function () {
          return delResult(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.uploadBox {
  display: flex;
  align-items: center;
}
::v-deep .el-upload-list__item {
  margin-top: 20px;
  margin-right: 0;
}
::v-deep .el-upload-list__item-name {
  margin: 0;
}
::v-deep .el-upload-list__item-status-label {
  display: none !important;
}
.fileUpload {
  width: 100%;
}
::v-deep .el-upload-list {
  width: 100% !important;
}
</style>
