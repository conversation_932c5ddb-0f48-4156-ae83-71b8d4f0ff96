<template>
  <div style="padding: 20px">
    <!-- 搜索 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="地市" prop="cityName">
        <el-input
          v-model="queryParams.cityName"
          placeholder="输入地市搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报考专业" prop="professional">
        <el-input
          v-model="queryParams.professional"
          placeholder="输入报考专业搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考生姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="输入考生姓名搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所在企业" prop="work">
        <el-input
          v-model="queryParams.work"
          placeholder="输入所在企业搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考生身份证号" prop="idCard">
        <el-input
          v-model="queryParams.idCard"
          placeholder="输入考生身份证号搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报考资质类别" prop="idCard">
        <el-select
          v-model="queryParams.candidateType"
          placeholder="请选择报考资质类别"
        >
          <el-option
            v-for="dict in dict.type.candidate_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="考试科目" prop="kemu">
        <el-input
          v-model="queryParams.kemu"
          placeholder="输入考试科目搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="考生年份" prop="yearInfo">
        <div class="block">
          <el-date-picker
            v-model="queryParams.yearInfo"
            type="year"
            clearable
            placeholder="选择年"
            format="yyyy 年"
            value-format="yyyy"
          ></el-date-picker>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <!-- 操作 -->
    <div>
      <el-button
        type="primary"
        plain
        icon="el-icon-plus"
        size="mini"
        @click="importShow = true"
        class="mb10"
      >
        上传
      </el-button>
      <el-button
        v-if="user == '00'"
        type="primary"
        plain
        size="mini"
        @click="open(1)"
        class="mb10"
      >
        禁用注册
      </el-button>
      <el-button
        v-if="user == '00'"
        type="primary"
        plain
        size="mini"
        @click="open(0)"
        class="mb10"
      >
        解开注册
      </el-button>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
        :columns="columns"
        style="float: right"
      />
    </div>
    <!-- 表格 -->
    <el-table v-loading="loading" :data="list">
      <!-- 序号 -->
      <el-table-column type="index" width="50" label="序号" align="center" />
      <!-- 考试省份 -->
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[0].label"
        :prop="columns[0].prop"
        v-if="columns[0].visible"
      ></el-table-column>
      <!-- 姓名 -->
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[2].label"
        :prop="columns[2].prop"
        v-if="columns[2].visible"
      ></el-table-column>
      <!-- 身份证号 -->
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[3].label"
        :prop="columns[3].prop"
        v-if="columns[3].visible"
      />
      <!-- 手机号 -->
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[4].label"
        :prop="columns[4].prop"
        v-if="columns[4].visible"
      />
      <!-- 报考专业 -->
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[1].label"
        :prop="columns[1].prop"
        v-if="columns[1].visible"
      >
        <template slot-scope="scope">
          {{ scope.row.professional }}
          <!-- {{
            selectDictLabel(dict.type.exam_subject, scope.row.examSubject) ||
            scope.row.examSubject
          }} -->
        </template>
      </el-table-column>
      <!-- 报考资质类别 -->
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[5].label"
        :prop="columns[5].prop"
        v-if="columns[5].visible"
      >
        <!-- <template slot-scope="scope">
          <div v-for="item in scope.row">
            {{ item.examSubject }}({{ item.examCode }})
          </div>
        </template> -->
      </el-table-column>
      <!-- 上传时间 -->
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[6].label"
        :prop="columns[6].prop"
        v-if="columns[6].visible"
      />
      <!-- 操作 -->
      <el-table-column
        v-if="user == '00'"
        label="是否禁用注册"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.registerStatus"
            :active-value="1"
            :inactive-value="0"
            active-color="#ff4949"
            @change="switchChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <!-- 操作 -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="openForm(scope.row)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 导入对话框 -->
    <ImportTemplate
      :show="importShow"
      :importData="importData"
      @close="importShow = false"
      @getList="getList"
    />
  </div>
</template>

<script>
import {
  getPersonInfo,
  banRegisterStatus,
  banRegister,
} from "@/api/department";

import listMixin from "@/mixin/listMixin";

import ImportTemplate from "@/views/department/ImportTemplate";

export default {
  dicts: ["exam_subject", "candidate_type"],
  mixins: [listMixin],
  components: { ImportTemplate },
  data() {
    return {
      options: [],
      // 列表数据接口
      listAPI: getPersonInfo,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 隐藏显示列数据
      columns: [
        { key: 0, label: `考试省市`, prop: "cityName", visible: true },
        { key: 1, label: `报考专业`, prop: "professional", visible: true },
        { key: 2, label: `姓名`, prop: "name", visible: true },
        { key: 3, label: `身份证号`, prop: "idCard", visible: true },
        { key: 4, label: `手机号`, prop: "phoneNumber", visible: true },
        { key: 5, label: `报考资质类别`, prop: "candidateType", visible: true },
        { key: 6, label: `上传时间`, prop: "createTime", visible: true },
      ],
      // 导入数据
      importShow: false,
      importData: {
        // 弹出层标题
        title: "考生台账导入",
        // 模板下载地址
        templateUrl: "/person/info/getImport",
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/person/info/importData",
      },
      yearInfo: "",
      user: null,
    };
  },

  methods: {
    openForm(row) {
      this.$router.push({
        name: "AuditForm",
        query: {
          id: row?.auditList[0]?.id,
          examineeId: row.id,
          isDetail: true,
          isInfo: true,
          name: "CandidateAccount",
        },
      });
    },
    switchChange(e) {
      const { id, registerStatus } = e;
      banRegisterStatus({ id, registerStatus });
    },
    open(e) {
      this.$confirm(
        e == 0 ? "此操作将解开注册, 是否继续?" : "此操作将禁用注册, 是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          banRegister({
            candidateType: this.queryParams.candidateType,
            registerStatus: e,
          }).then((res) => {
            this.getList();
          });
        })
        .catch(() => {});
    },
  },
  mounted() {
    this.user = sessionStorage.getItem("user");
  },
};
</script>
