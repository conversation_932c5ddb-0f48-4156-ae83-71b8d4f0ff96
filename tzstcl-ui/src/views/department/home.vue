<template>
  <div class="app-container">
    <div class="box">
      <div>
        <p>数据统计</p>
        <p class="boxBtn">
          <el-button
            type="primary"
            class="examine"
            @click="$router.push({ name: 'ReviewList' })"
          >
            去审核
          </el-button>
          <el-button type="primary" @click="sx">刷新</el-button>
        </p>
        <p style="flex: 1"></p>
        <p style="margin-right: 10px">考试类型:</p>
        <p style="margin-right: 20px">
          <el-select
            v-model="value"
            placeholder="请选择考试类型"
            @change="selectChange"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </p>
        <p style="margin-right: 10px">年份:</p>
        <div class="block">
          <el-date-picker
            v-model="yearInfo"
            type="year"
            clearable
            placeholder="选择年份"
            format="yyyy 年"
            value-format="yyyy"
            @change="clickYear"
          ></el-date-picker>
        </div>
      </div>
      <div class="overview">
        <el-card
          v-for="item in overview"
          :key="item.icon"
          style="width: 20%; margin: 5px"
          :body-style="bodyStyle"
        >
          <i :class="item.icon" style="font-size: 50px"></i>
          <div class="ml20">
            <p class="overview-item overview-item-num">{{ item.num }}</p>
            <p class="overview-item">{{ item.label }}</p>
          </div>
        </el-card>
      </div>
    </div>
    <el-card v-if="chartShow" header="地市考生数据">
      <BarChart ref="barChart" />
    </el-card>
  </div>
</template>

<script>
import { getData } from "@/api/department";
import { getDicts } from "@/api/system/dict/data.js";
import BarChart from "@/views/components/BarChart/BarChart";

export default {
  components: { BarChart },
  data() {
    return {
      overview: [
        { label: "总考生", num: 0, icon: "el-icon-s-custom" },
        { label: "待审核", num: 0, icon: "el-icon-s-check" },
        { label: "审核通过", num: 0, icon: "el-icon-document-checked" },
        { label: "审核不通过", num: 0, icon: "el-icon-error" },
        // { label: "待企业审核", num: 0, icon: "el-icon-more" },
        // { label: "已参加审核", num: 0, icon: "el-icon-warning" },
        { label: "未参加审核", num: 0, icon: "el-icon-remove" },
      ],
      chartShow: false,
      bodyStyle: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      },
      options: [],
      value: "",
      yearInfo: "",
    };
  },
  mounted() {
    this.getInfo();
    this.type();
  },
  methods: {
    sx() {
      this.value = "";
      this.yearInfo = "";
      this.getInfo();
    },
    clickYear(e) {
      this.getInfo();
    },
    selectChange(e) {
      this.getInfo();
    },
    getInfo() {
      getData({ yearInfo: this.yearInfo, candidateType: this.value }).then(
        (res) => {
          this.overview[0].num = res.data.sum || 0;
          this.overview[1].num = res.data.noAudit || 0;
          this.overview[2].num = res.data.yesAudit || 0;
          this.overview[3].num = res.data.auditnotpassed || 0;
          // this.overview[4].num = res.data.waitcorpaudit || 0;
          // this.overview[5].num = res.data.otaddaudither || 0;
          this.overview[4].num = res.data.noAdd || 0;
          if (res.data.cityData) {
            const chartData = {
              cityName: [],
              sum: [],
              noAdd: [],
              noAudit: [],
              yesAudit: [],
            };
            res.data.cityData.map((item) => {
              chartData.cityName.push(item.cityName);
              chartData.sum.push(item.sum);
              chartData.noAdd.push(item.noAdd);
              chartData.noAudit.push(item.noAudit);
              chartData.yesAudit.push(item.yesAudit);
            });
            this.chartShow = true;
            this.$nextTick(() => {
              this.$refs.barChart.initChart(chartData);
            });
          }
        }
      );
    },
    type() {
      getDicts("candidate_type").then((res) => {
        res.data.forEach((item) => {
          this.options.push({
            label: item.dictLabel,
            value: item.dictValue,
          });
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.overview {
  margin: -5px;
  margin-bottom: 10px;
  display: flex;
  .overview-item {
    margin: 0;
  }
  .overview-item-num {
    font-size: 25px;
    font-weight: bold;
  }
  .examine {
    margin: 5px;
    width: 20%;
    font-size: 30px;
  }
}
.box {
  width: 100%;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  div {
    display: flex;
    align-items: center;
    .boxBtn {
      margin-left: 120px;
    }
    button {
      height: 40px;
      width: 100px;
    }
  }
}
</style>
