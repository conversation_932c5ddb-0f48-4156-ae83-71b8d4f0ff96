<template>
  <div style="padding: 20px">
    <PageTitle title="考后审核" />
    <!-- 搜索 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="地市" prop="cityName">
        <el-input
          v-model="queryParams.cityName"
          placeholder="输入地市搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考生姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="输入考生姓名搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所在企业" prop="work">
        <el-input
          v-model="queryParams.work"
          placeholder="输入所在企业搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考生身份证号" prop="idCard">
        <el-input
          v-model="queryParams.idCard"
          placeholder="输入考生身份证号搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报考资质类别" prop="idCard">
        <el-select v-model="queryParams.candidateType" placeholder="请选择">
          <el-option
            v-for="dict in dict.type.candidate_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="考生年份" prop="yearInfo">
        <div class="block">
          <el-date-picker
            v-model="queryParams.yearInfo"
            type="year"
            clearable
            placeholder="选择年"
            format="yyyy 年"
            value-format="yyyy"
          ></el-date-picker>
        </div>
      </el-form-item>
      <!-- <el-form-item label="考试科目" prop="kemu">
        <el-input
          v-model="queryParams.kemu"
          placeholder="输入考试科目搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->

      <el-form-item label="审核人" prop="auditPerson">
        <el-input
          v-model="queryParams.auditPerson"
          placeholder="查询审核人"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="报考类型" prop="auditPerson">
        <el-select v-model="queryParams.type" placeholder="请选择">
          <el-option
            v-for="(item, index) in getCandidateTypeList"
            :key="index"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <!-- 操作 -->
    <el-button
      type="primary"
      icon="el-icon-upload2"
      size="mini"
      @click="handleExport"
      class="mb10"
    >
      导出
    </el-button>
    <div class="operation">
      <el-tabs v-model="queryParams.select" type="card" @tab-click="getList">
        <el-tab-pane label="全部" name="0"></el-tab-pane>
        <el-tab-pane label="待审核" name="1"></el-tab-pane>
        <el-tab-pane label="审核通过" name="2"></el-tab-pane>
        <el-tab-pane label="审核不通过" name="3"></el-tab-pane>
        <el-tab-pane label="待企业验证" name="4"></el-tab-pane>
        <el-tab-pane label="已参加审核" name="5"></el-tab-pane>
        <el-tab-pane label="未参加审核" name="6"></el-tab-pane>
      </el-tabs>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
        :columns="columns"
        class="right"
      />
    </div>
    <!-- 表格 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column type="index" width="50" label="序号" align="center" />
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[0].label"
        :prop="columns[0].prop"
        v-if="columns[0].visible"
      ></el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[1].label"
        :prop="columns[1].prop"
        v-if="columns[1].visible"
      ></el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[2].label"
        :prop="columns[2].prop"
        v-if="columns[2].visible"
      ></el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[3].label"
        :prop="columns[3].prop"
        v-if="columns[3].visible"
      />
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[4].label"
        :prop="columns[4].prop"
        v-if="columns[4].visible"
      >
        <!-- <template slot-scope="scope">
          <div v-for="item in scope.row">
            {{ item.examSubject}}({{ item.examCode }})
          </div>
        </template> -->
      </el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[5].label"
        :prop="columns[5].prop"
        v-if="columns[5].visible"
      ></el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[6].label"
        :prop="columns[6].prop"
        v-if="columns[6].visible"
      ></el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[7].label"
        :prop="columns[7].prop"
        v-if="columns[7].visible"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.audit_status"
            :value="scope.row.auditStatus"
          />
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[8].label"
        :prop="columns[8].prop"
        v-if="columns[8].visible"
      ></el-table-column>

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="openForm(scope.row, true, false)"
          >
            查看
          </el-button>
          <div v-if="deptNameType">
            <div v-if="!emailValue">
              <el-button
                v-if="[2, 5].includes(scope.row.auditStatus)"
                size="mini"
                type="text"
                icon="el-icon-notebook-2"
                @click="openForm(scope.row, false, true)"
              >
                审核
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { auditList, getPersonInfoByType } from "@/api/department";

import listMixin from "@/mixin/listMixin";

export default {
  dicts: ["audit_status", "candidate_type"],
  mixins: [listMixin],
  data() {
    return {
      options: [],
      // 列表数据接口
      listAPI: auditList,
      // 查询参数
      queryParams: {
        select: 0,
        pageNum: 1,
        pageSize: 10,
      },
      resetIgnore: ["select"],
      // 隐藏显示列数据
      columns: [
        { key: 0, label: `考试地市`, prop: "cityName", visible: true },
        { key: 1, label: `姓名`, prop: "name", visible: true },
        { key: 2, label: `身份证号`, prop: "idCard", visible: true },
        { key: 3, label: `所在企业`, prop: "work", visible: true },
        { key: 4, label: `报考资质类别`, prop: "candidateType", visible: true },
        {
          key: 5,
          label: `考生年份`,
          prop: "createTime",
          visible: true,
        },
        {
          key: 6,
          label: `提交时间`,
          prop: "updateTime",
          visible: true,
        },
        { key: 7, label: `审核状态`, prop: "auditStatus", visible: true },
        { key: 8, label: `审核人`, prop: "auditPerson", visible: true },
      ],
      // 导出参数
      exportData: {
        // 导出URL
        url: "/department/exportData",
        // 导出文件名
        fileName: "考后审核",
      },
      getCandidateTypeList: [],
      deptName: "",
      deptNameType: true,
      // 判断人员审核
      emailValue: false,
    };
  },
  created() {
    this.queryParams.select = this.$route.query.selectName;
    this.getCandidateType();
    console.log(123, JSON.parse(sessionStorage.getItem("dept"))?.deptName);
    this.deptName = JSON.parse(sessionStorage.getItem("dept"))?.deptName;
    console.log(sessionStorage.getItem("email"));
    if (
      sessionStorage.getItem("email") &&
      sessionStorage.getItem("email").indexOf("everycitychoucha") != -1
    ) {
      this.emailValue = true;
      console.log("this.emailValue", this.emailValue);
    }

    if (this.deptName.includes("审查部门")) {
      console.log(" 已找到！");
      this.deptNameType = false;
    } else {
      console.log(" 未找到！");
      this.deptNameType = true;
    }
  },
  methods: {
    async getCandidateType() {
      getPersonInfoByType().then((res) => {
        console.log(res);
        this.getCandidateTypeList = res.data;
      });
    },
    openForm(row, isDetail, look) {
      this.$router.push({
        name: "AuditForm",
        query: {
          look: look,
          id: row.id,
          examineeId: row.id,
          type: 0,
          isDetail: isDetail || ![2, 5].includes(row.auditStatus),
          isInfo: false,
          name: "ReviewList",
          select: this.queryParams.select,
        },
      });
    },
    // 导出按钮操作
    handleExport() {
      const nowDate = this.parseTime(new Date(), "{y}-{m}-{d}_{h}-{i}-{s}");
      this.download(
        this.exportData.url,
        { ...this.queryParams },
        `${this.exportData.fileName}_${nowDate}.xlsx`
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.operation {
  position: relative;
  margin-bottom: -15px;
  .right {
    position: absolute;
    right: 0;
    top: 0;
  }
}
</style>
