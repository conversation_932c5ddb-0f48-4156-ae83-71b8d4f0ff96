<template>
  <el-dialog
    :title="importData.title"
    :visible.sync="dialogVisible"
    width="400px"
    append-to-body
  >
    <el-form :model="formData" ref="elForm" :rules="rules">
      <!-- <el-form-item label="报考资质类别" prop="candidateType">
          <el-select v-model="formData.candidateType" placeholder="报考资质类别">
            <el-option
              v-for="dict in dict.type.candidate_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item> -->
    </el-form>

    <el-upload
      ref="upload"
      :limit="1"
      accept=".xlsx, .xls"
      :headers="headers"
      :data="formData"
      :action="importData.url"
      :disabled="isUploading"
      :on-progress="handleFileUploadProgress"
      :on-success="handleFileSuccess"
      :auto-upload="false"
      drag
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">
        将文件拖到此处，或
        <em>点击上传</em>
      </div>
    </el-upload>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitFileForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  dicts: ["candidate_type"],
  props: {
    // 导入数据
    importData: {
      require: true,
      type: Object,
      // dialog标题
      title: {
        require: true,
        type: String,
      },
      // 模板下载地址
      templateUrl: {
        require: true,
        type: String,
      },
      // 上传地址(导入接口)
      url: {
        require: true,
        type: String,
      },
      // 上传附带参数
      // data: {
      //   // candidate_type:0,
      //   type: Object,
      //   default: {}
      // }
    },
    // 控制显示隐藏
    show: {
      require: true,
      type: Boolean,
    },
  },
  data() {
    return {
      dialogVisible: false,
      isUploading: false,
      headers: { Authorization: "Bearer " + getToken() },
      formData: {},
      rules: {
        //   candidateType: [
        //     {
        //       required: true,
        //       message: "请选择报考资质类别",
        //       trigger: "change",
        //     },
        //   ],
      },
    };
  },
  watch: {
    // 观察父级组件的show，并将showFlag的最新值设置给dialogVisible
    show(newVal, oldVal) {
      this.dialogVisible = newVal;
    },
    // 子组件关闭dialog时，通知父组件同步
    dialogVisible(newVal, oldVal) {
      if (!newVal) this.$emit("close");
    },
  },
  methods: {
    // 下载模板操作
    importTemplate() {
      const nowDate = this.parseTime(new Date(), "{y}-{m}-{d}_{h}-{i}-{s}");
      this.download(
        this.importData.templateUrl,
        {},
        `${this.importData.title}模板_${nowDate}.xlsx`
      );
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.dialogVisible = false;
      this.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.$emit("importResult", response);
      this.$emit("getList");
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.elForm.validate();
      this.$refs.upload.submit();
    },
  },
};
</script>
