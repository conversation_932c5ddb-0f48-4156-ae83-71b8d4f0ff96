<template>
  <div class="app-container">
    <div class="mainConItem">
      <div class="mainConItemHeader">
        <div class="mainConItemHeaderImg">
          <img src="@/assets/images/infor.png" />
        </div>
        <div class="mainConItemHeaderName" style="width: 100%">
          <span>修改考生手机号</span>
        </div>
      </div>
      <div class="mainConItemBot">
        <el-form label-width="130px" ref="form" :model="form" :rules="rules">
          <el-form-item
            label="考生身份证号："
            prop="idCard"
            style="width: 450px"
          >
            <el-input v-model="form.idCard"></el-input>
          </el-form-item>
          <el-form-item label="更改手机号：" prop="phoneNumber">
            <el-input v-model="form.phoneNumber"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="resetForm('form')">重置</el-button>
            <el-button type="primary" @click="submitForm('form')">
              提交
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="mainConItem">
      <div class="mainConItemHeader">
        <div class="mainConItemHeaderImg">
          <img src="@/assets/images/infor.png" />
        </div>
        <div class="mainConItemHeaderName" style="width: 100%">
          <span>修改考生考试类型</span>
        </div>
      </div>
      <div class="mainConItemBot">
        <el-form label-width="130px" ref="form" :model="form" :rules="rules">
          <el-form-item label="文件：" style="width: 450px">
            <el-button type="primary" @click="importShow = true">
              上传
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!-- 导入对话框 -->
    <ImportTemplate
      :show="importShow"
      :importData="importData"
      @close="importShow = false"
    />
  </div>
</template>

<script>
import { idCardValid } from "@/utils/validate";
import { updatePhoneNum } from "@/api/candidatePhone";
import ImportTemplate from "@/views/department/candidateType";

export default {
  components: { ImportTemplate },
  data() {
    return {
      form: {},
      rules: {
        idCard: [
          { required: true, message: "请输入您的身份证号" },
          { validator: idCardValid },
        ],
        phoneNumber: [
          { required: true, message: "请输入您的手机号" },
          { pattern: /^1\d{10}$/, message: "手机号格式不正确" },
        ],
      },

      importShow: false,
      importData: {
        // 弹出层标题
        title: "修改考生考试类型",
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/person/info/updateOldType",
      },
    };
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const h = this.$createElement;
          this.$msgbox({
            title: "提示",
            message: h("p", null, [
              h("span", null, "确认将考生身份证 "),
              h("br", null),
              h("b", { style: "color: teal" }, this.form.idCard),
              h("br", null),
              h("span", null, "的手机号替换为 "),
              h("br", null),
              h("b", { style: "color: teal" }, this.form.phoneNumber),
            ]),
            showCancelButton: true,
            confirmButtonText: "确定",
            cancelButtonText: "取消",
          }).then(() => {
            updatePhoneNum(this.form).then((res) => {
              if (res.code == 200) {
                this.$message({
                  type: "success",
                  message: res.msg,
                });
                this.resetForm("form");
              }
            });
          });
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
.mainConItem {
  width: 100%;

  margin-bottom: 27px;
  background: #ffffff;
  .mainConItemHeader {
    height: 60px;
    width: 100%;
    display: flex;

    .mainConItemHeaderImg {
      width: 32px;
      height: 31px;
      img {
        width: 32px;
        height: 31px;
      }
      //background: #008BE3;
    }
    .mainConItemHeaderName {
      font-size: 20px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
      line-height: 34px;
      margin-left: 21px;
    }
  }
  .mainConItemBot {
    width: 100%;
    display: flex;
    justify-content: space-between;

    font-size: 16px;
    line-height: 34px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    .searchBoxTitle {
      font-size: 18px;
      font-weight: 700;
      margin-bottom: 15px;
    }
  }
}
</style>
