<template>
  <el-dialog
    :title="importData.title"
    :visible.sync="dialogVisible"
    width="600px"
    append-to-body
  >
    <el-form ref="elForm" :model="formData" :rules="rules" label-width="150px">
      <!-- 模板 -->
      <el-form-item label="选择发送短信模板" prop="titleId">
        <el-select v-model="formData.titleId" placeholder="请选择" size="small">
          <el-option
            v-for="item in listSmsInfo"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="接收短信手机号">
        <el-radio-group v-model="phoneType">
          <el-radio-button label="批量"></el-radio-button>
          <el-radio-button label="手动"></el-radio-button>
        </el-radio-group>
        <!-- 批量 -->
        <el-upload
          v-show="phoneType == '批量'"
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="headers"
          :action="importData.url"
          :data="formData"
          :disabled="isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
          <div class="el-upload__tip text-center" slot="tip">
            <span>请使用模板导入，点击</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
            >
              下载模板
            </el-link>
          </div>
        </el-upload>
        <!-- 手动 -->
        <el-input
          v-show="phoneType == '手动'"
          v-model="formData.phone"
          type="textarea"
          :autosize="{ minRows: 2 }"
          placeholder="可手动输入手机号，手机号之间用逗号隔开，单次最多可输入5000个"
        />
      </el-form-item>
      <!-- 发送方式 -->
      <el-form-item label="发送方式" prop="sendType">
        <el-radio-group v-model="formData.sendType">
          <el-radio :label="1">定时</el-radio>
          <el-radio :label="0">实时</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 日期 -->
      <el-form-item v-show="formData.sendType == 1">
        <el-date-picker
          v-model="formData.sendTime"
          type="datetime"
          placeholder="选择日期时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          size="small"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitFileForm">确 定</el-button>
      <el-button @click="closeClick">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getToken } from "@/utils/auth";
import { listSms, listBySend } from "@/api/department";
import { getSend, addSend, updateSend } from "@/api/department";
export default {
  props: {
    // 导入数据
    importData: {
      require: true,
      type: Object,
      // dialog标题
      title: {
        require: true,
        type: String,
      },
      // 模板下载地址
      templateUrl: {
        require: true,
        type: String,
      },
      // 上传地址(导入接口)
      url: {
        require: true,
        type: String,
      },
    },
    // 控制显示隐藏
    show: {
      require: true,
      type: Boolean,
    },
    parentId: {
      require: true,
      type: Number,
    },
  },
  data() {
    return {
      dialogVisible: false,
      isUploading: false,
      headers: { Authorization: "Bearer " + getToken() },
      formData: { sendType: 1 },
      rules: {},
      // 发送短信模板
      listSmsInfo: [],
      phoneType: "批量",
      parentId: "",
    };
  },
  watch: {
    // 观察父级组件的show，并将showFlag的最新值设置给dialogVisible
    show(newVal, oldVal) {
      this.dialogVisible = newVal;
    },
    // 子组件关闭dialog时，通知父组件同步
    dialogVisible(newVal, oldVal) {
      if (!newVal) this.$emit("close");
    },
    parentId(newVal, oldVal) {
      this.parentId = newVal;
      getSend(newVal).then((response) => {
        response.data.phone = JSON.parse(response.data.phone).join(",");
        this.formData = response.data;
        this.phoneType = "手动";
      });
    },
  },
  created() {
    this.getListSms();
  },
  methods: {
    // 下载模板操作
    importTemplate() {
      const nowDate = this.parseTime(new Date(), "{y}-{m}-{d}_{h}-{i}-{s}");
      this.download(
        this.importData.templateUrl,
        { path: "手机号导入模板.xlsx" },
        `${this.importData.title}模板_${nowDate}.xlsx`
      );
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.dialogVisible = false;
      this.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.$emit("importResult", response);
      this.$emit("getList");
    },
    // 提交上传文件
    submitFileForm() {
      // 批量
      if (this.phoneType == "批量") {
        this.$refs.upload.submit();
      }
      // 手动
      else {
        let form = new FormData();
        for (const key in this.formData) {
          form.append(key, this.formData[key]);
        }
        addSend(form).then((response) => {
          this.dialogVisible = false;
          this.isUploading = false;
          this.$alert(
            "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
              response.msg +
              "</div>",
            "导入结果",
            { dangerouslyUseHTMLString: true }
          );
          this.$emit("getList");
        });
      }
      this.formData = { sendType: 1 };
      this.phoneType = "批量";
    },
    getListSms() {
      listBySend().then((res) => {
        this.listSmsInfo = res.data;
      });
    },
    // 取消
    closeClick() {
      this.dialogVisible = false;
      this.formData = { sendType: 1 };
      this.phoneType = "批量";
    },
  },
};
</script>

<style>
.text-center {
  text-align: left;
}
</style>
