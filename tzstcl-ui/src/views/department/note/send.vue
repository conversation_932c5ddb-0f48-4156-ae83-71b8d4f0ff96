<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="发送时间" prop="sendTime">
        <el-date-picker
          clearable
          v-model="queryParams.sendTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择发送时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="importShow = true"
          v-hasPermi="['system:send:add']"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:send:remove']"
        >
          删除
        </el-button>
      </el-col>

      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="sendList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="短信内容" align="center" prop="content" />
      <el-table-column label="发送条数" align="center" prop="number" />
      <el-table-column label="发送方式" align="center" prop="sendType">
        <template slot-scope="scope">
          <span v-text="scope.row.sendType == 0 ? '实时' : '定时'"></span>
        </template>
      </el-table-column>
      <el-table-column
        label="发送时间"
        align="center"
        prop="sendTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.sendTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发送状态" align="center" prop="sendStatus">
        <template slot-scope="scope">
          {{ dataStatus(scope.row.sendStatus) }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="
              (scope.row.sendType =
                '定时' && [0, 2].includes(scope.row.sendStatus))
            "
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:send:edit']"
          >
            重新发送
          </el-button>
          <el-button
            v-if="
              (scope.row.sendType =
                '定时' && [0, 2].includes(scope.row.sendStatus))
            "
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:send:remove']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 导入对话框 -->
    <ImportTemplate
      :parentId="parentId"
      :show="importShow"
      :importData="importData"
      @close="importShow = false"
      @getList="getList"
    />
  </div>
</template>

<script>
import {
  listSend,
  getSend,
  delSend,
  addSend,
  updateSend,
} from "@/api/department";
import ImportTemplate from "@/views/department/note/ImportSendTemplate";
export default {
  name: "Send",
  components: { ImportTemplate },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 短信发送表格数据
      sendList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      importShow: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        titleId: null,
        content: null,
        number: null,
        phone: null,
        filePath: null,
        sendType: null,
        sendTime: null,
        sendStatus: null,
        userId: null,
      },
      // 表单参数
      form: {},

      // 导入数据
      importData: {
        // 弹出层标题
        title: "短信",
        // 模板下载地址
        templateUrl: "/uploadFile/importTemplate",
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/message/send",
      },
      parentId: "",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    dataStatus(e) {
      switch (e) {
        case 0:
          return "待发送";
        case 1:
          return "发送成功";
        case 2:
          return "发送失败";
      }
    },
    /** 查询短信发送列表 */
    getList() {
      this.loading = true;
      listSend(this.queryParams).then((response) => {
        this.sendList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    // reset() {
    //   this.form = {
    //     id: null,
    //     titleId: null,
    //     content: null,
    //     number: null,
    //     phone: null,
    //     filePath: null,
    //     sendType: null,
    //     sendTime: null,
    //     sendStatus: 0,
    //     userId: null,
    //     delFlag: null,
    //   };
    //   this.resetForm("form");
    // },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 重新发送按钮操作 */
    handleUpdate(row) {
      this.importShow = true;
      this.parentId = row.id;
      //   this.reset();
      //   const id = row.id || this.ids;
      //   getSend(id).then((response) => {
      //     this.form = response.data;
      //     this.open = true;
      //   });
    },
    /** 提交按钮 */
    // async submitForm() {
    //   if (this.$refs.ImportRef) {
    //     this.$refs.ImportRef.submitFileForm();
    //   }
    //   await this.$refs["form"].validate((valid) => {
    //     if (valid) {
    //       if (this.form.id != null) {
    //         updateSend(this.form).then((response) => {
    //           this.$modal.msgSuccess("修改成功");
    //           this.open = false;
    //           this.getList();
    //         });
    //       } else {
    //         addSend(this.form).then((response) => {
    //           this.$modal.msgSuccess("新增成功");
    //           this.open = false;
    //           this.getList();
    //         });
    //       }
    //     }
    //   });
    // },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除短信发送编号为"' + ids + '"的数据项？')
        .then(function () {
          return delSend(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    // handleExport() {
    //   this.download(
    //     "system/send/export",
    //     {
    //       ...this.queryParams,
    //     },
    //     `send_${new Date().getTime()}.xlsx`
    //   );
    // },
    // importResult(e) {
    //   this.form.file = e;
    // },
  },
};
</script>
