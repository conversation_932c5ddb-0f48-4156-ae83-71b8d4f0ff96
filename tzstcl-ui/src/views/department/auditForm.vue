<template>
  <div style="padding: 10px; background-color: #fff">
    <ExamineeInfo @clickType="clickType" @clickState="clickState" />
    <FileTable
      v-if="!isInfo"
      @getAuditStatus="getAuditStatus"
      :idCard="idCard"
      :candidateType="candidateType"
    />
    <PageTitle v-if="!isInfo" title="审核日志"></PageTitle>
    <AuditLog v-if="!isInfo" @getAuditId="getAuditId" />
    <PageTitle v-if="!isDetail" title="审核"></PageTitle>
    <el-form
      v-if="!isDetail"
      ref="elForm"
      :model="formData"
      :rules="rules"
      size="medium"
      label-width="auto"
    >
      <el-form-item label="审核结果" prop="auditResult">
        <el-radio-group v-model="formData.auditResult">
          <el-radio :label="0">通过</el-radio>
          <el-radio :label="1">不通过</el-radio>
          <el-radio :label="4">需补交资料</el-radio>
          <el-radio :label="2">需现场核实</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- <el-form-item v-if="this.userType == '04'" label="附件:" prop="cityFile">
        <FileUpload v-model="formData.cityFile" />
      </el-form-item> -->
      <el-form-item
        v-if="this.userType != '02' || this.userType != '03'"
        label="附件:"
        prop="cityFile"
      >
        <FileUpload v-model="formData.cityFile" />
      </el-form-item>

      <el-form-item label="审核意见" prop="opinion">
        <el-input
          v-model="formData.opinion"
          type="textarea"
          placeholder="请输入办理意见"
          :autosize="{ minRows: 4, maxRows: 4 }"
          :style="{ width: '100%' }"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="是否隐藏该条审核"
        prop="opinion"
        v-if="typeId != '02'"
      >
        <el-radio-group v-model="formData.isSee">
          <el-radio label="是">隐藏</el-radio>
          <el-radio label="否">不隐藏</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <!-- 管理员审核 -->
    <PageTitle
      v-if="look == false && typeId == '00'"
      title="管理员审核"
    ></PageTitle>
    <el-form
      v-if="look == false && typeId == '00'"
      ref="lookInfo"
      :model="lookInfoFrom"
      size="medium"
      label-width="auto"
      :rules="rules"
    >
      <el-form-item label="当前状态">{{ state }}</el-form-item>
      <el-form-item label="更改状态" prop="auditResult">
        <el-select v-model="lookInfoFrom.auditResult" placeholder="请选择">
          <el-option label="待主管部门审核" value="3"></el-option>
          <el-option label="审核通过" value="0"></el-option>
          <el-option label="审核驳回" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核意见" prop="opinion">
        <el-input
          type="textarea"
          v-model="lookInfoFrom.opinion"
          placeholder="请输入办理意见"
          :autosize="{ minRows: 4, maxRows: 4 }"
        ></el-input>
      </el-form-item>
    </el-form>

    <!-- 点击按钮 -->
    <div class="center mb20 mt20">
      <div v-if="deptNameType">
        <el-button
          type="danger"
          v-if="TId == 3 || TId == 4"
          :style="{ marginRight: '20px' }"
          @click="dialogVisible = true"
        >
          还原
        </el-button>
      </div>
      <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
        <span>是否确认还原?</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="(dialogVisible = false), handle()">
            确 定
          </el-button>
        </span>
      </el-dialog>
      <el-button
        :type="isDetail ? 'primary' : ''"
        @click="
          $tab.closeOpenPage({
            name: $route.query.name,
            query: {
              selectName: $route.query.select,
            },
          })
        "
      >
        返回
      </el-button>
      <!-- 审核提交 -->
      <el-button v-if="!isDetail" type="primary" @click="submitForm">
        提交
      </el-button>
      <!-- 管理员提交 -->
      <el-button
        v-if="look == false && typeId == '00'"
        type="primary"
        @click="submitFormLook"
      >
        提交
      </el-button>
    </div>
  </div>
</template>

<script>
import { audit, reduction, clickInfoBtn } from "@/api/department";
import { getInfo } from "@/api/login";
import ExamineeInfo from "@/views/components/ExamineeInfo/ExamineeInfo";
import FileTable from "@/views/components/FileTable/FileTable";
import AuditLog from "@/views/components/AuditLog/AuditLog";
import FileUpload from "@/views/components/FileUpload/FileUpload";
export default {
  components: { ExamineeInfo, FileTable, AuditLog, FileUpload },
  data() {
    return {
      dialogVisible: false,
      isInfo: true,
      isDetail: true,
      look: true,
      formData: { candidateType: "", isSee: "否" },
      TId: "",
      sIf: false,
      rules: {
        auditResult: [
          {
            required: true,
            message: "请选择审核结果",
            trigger: "blur",
          },
        ],
        opinion: [
          {
            required: true,
            message: "请输入审核意见",
            trigger: "blur",
          },
        ],
        // cityFile: [
        //   {
        //     required: true,
        //     message: "请上传附件",
        //     trigger: "blur",
        //   },
        // ],
      },

      lookInfo: "",
      lookInfoFrom: {
        auditResult: "",
        opinion: "",
        candidateType: "",
      },
      state: "",
      typeId: "",
      auditId: "",
      idCard: "",
      candidateType: "",
      deptName: "",
      deptNameType: null,
      userType: "",
    };
  },
  created() {
    this.isDetail = this.$route.query.isDetail;
    this.isInfo = this.$route.query.isInfo;
    this.look = this.$route.query.look;
    this.userType = sessionStorage.getItem("user");

    getInfo().then((res) => {
      this.typeId = res.user.userType;
    });
    // console.log(JSON.parse(sessionStorage.getItem("dept")).deptName);
    this.deptName = JSON.parse(sessionStorage.getItem("dept")).deptName;
    if (this.deptName.includes("审查部门")) {
      this.deptNameType = false;
    } else {
      this.deptNameType = true;
    }
  },
  methods: {
    submitForm() {
      console.log(this.formData);

      this.$refs["elForm"].validate((valid) => {
        if (valid) {
          this.formData.id = this.auditId;
          this.formData.examineeId = this.$route.query.examineeId;
          audit(this.formData).then((res) => {
            this.$message.success(res.msg);
            this.$tab.closeOpenPage({
              name: this.$route.query.name,
              query: {
                selectName: this.$route.query.select,
              },
            });
          });
        }
      });
    },
    submitFormLook() {
      this.$refs["lookInfo"].validate((valid) => {
        if (valid) {
          this.lookInfoFrom.id = this.auditId;
          this.lookInfoFrom.examineeId = this.$route.query.examineeId;
          clickInfoBtn(this.lookInfoFrom).then((res) => {
            this.$message.success(res.msg);
            console.log();
            this.$tab.closeOpenPage({
              name: this.$route.query.name,
              query: {
                selectName: this.$route.query.select,
              },
            });
          });
        }
      });
    },
    getAuditStatus(AId) {
      this.TId = AId;
    },
    getAuditId(e) {
      this.auditId = e;
    },
    async handle() {
      const res = await reduction({
        id: this.auditId,
        examineeId: this.$route.query.examineeId,
        candidateType: this.formData.candidateType,
      });
      this.TId = 0;
    },
    clickType(e, id) {
      if (e && id) {
        this.formData.candidateType = e;
        this.lookInfoFrom.candidateType = e;
        this.idCard = id;
        this.candidateType = e;
      }
    },
    clickState(e) {
      this.state = e;
    },
  },
};
</script>

<style lang="scss" scoped>
.center {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
</style>
