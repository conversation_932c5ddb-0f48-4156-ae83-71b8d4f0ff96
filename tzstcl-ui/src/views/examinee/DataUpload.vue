<template>
  <el-card>
    <div slot="header" class="clearfix">
      <span>资料上传</span>
      <el-button
        v-if="!isFinish"
        style="padding: 3px 0; margin-left: 20px"
        type="text"
        @click="getBack"
      >
        返回
      </el-button>
    </div>
    <el-form :model="formData" :rules="rules" ref="elform" label-width="220px">
      <el-form-item>
        <span style="color: #45a1f9; font-weight: bold">
          请上传 jpg、jpeg、png、pdf 格式文件
        </span>
      </el-form-item>
      <el-form-item label="报考资质类别:" prop="">
        <el-select
          v-model="formData.candidateType"
          placeholder="请选择"
          @change="change"
        >
          <el-option
            v-for="dict in dict.type.candidate_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="身份证复印件:" prop="idcarda">
        <FileUpload v-model="formData.idcarda" :disabled="isFinish" />
      </el-form-item>
      <!-- <el-form-item label="身份证反面:" prop="idcardb">
        <FileUpload v-model="formData.idcardb" :disabled="isFinish" />
      </el-form-item> -->
      <el-form-item
        :label="type == 1 ? '学历/学位证书:' : '学历/学位证书:'"
        :prop="type == 1 ? 'diploma' : 'graduation'"
      >
        <FileUpload
          class="box"
          v-if="type == 1"
          v-model="formData.diploma"
          :disabled="isFinish"
        />
        <FileUpload
          class="box"
          v-if="type != 1"
          v-model="formData.graduation"
          :disabled="isFinish"
        />
        <span class="boxFont" v-show="type == 1 ? true : false">
          此项可上传职称证明
        </span>
      </el-form-item>
      <el-form-item
        :label="
          type == 1
            ? '学历/学位在线验证/认证报告:'
            : '学历/学位在线验证/认证报告:'
        "
        :prop="type == 1 ? 'education' : 'education'"
      >
        <FileUpload v-model="formData.education" :disabled="isFinish" />
      </el-form-item>
      <el-form-item
        label="从业年限证明（单位盖章）:"
        :prop="type == 1 ? 'work' : 'work'"
      >
        <FileUpload v-model="formData.work" :disabled="isFinish" />
        <span class="boxFont" v-show="type == 1 ? true : false">
          此项可上传设计成绩突出证明
        </span>
      </el-form-item>
      <el-form-item
        label="其它可证明工作年限的材料:"
        prop="social"
        v-if="hand == 1"
      >
        <div class="helpBox">
          <FileUpload v-model="formData.social" :disabled="isFinish" />
          <span class="helpImg">
            <el-tooltip
              class="item"
              effect="dark"
              content="如社保证明、工资流水等"
              placement="top-start"
            >
              <img src="@/assets/images/help.png" alt="" />
              >
            </el-tooltip>
          </span>
        </div>
      </el-form-item>
      <el-form-item label="其他:">
        <FileUpload v-model="formData.other" :disabled="isFinish" />
      </el-form-item>

      <!-- <div class="ziLiao" v-show="type == 1 ? true : false">
        资料审查规则请参考：
        <span @click="clickHandle">
          http://www.cpta.com.cn/testCondition/1091.html
        </span>
      </div> -->

      <el-form-item v-if="!isFinish">
        <el-button type="primary" @click="debounceClick">提交审核</el-button>
        <span class="sp1" v-show="sp1">请勿重复点击</span>
        <!-- <span class="sp2" v-show="sp2">提交成功</span> -->
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import { getInfoByIdCard, addData, back } from "@/api/examinee";
import FileUpload from "@/views/components/FileUpload/FileUpload";
import _ from "lodash";

export default {
  components: { FileUpload },
  dicts: ["candidate_type"],
  data() {
    return {
      hand: "",
      sp1: true,
      sp2: false,
      options: [],
      type: "",
      isFinish: true,
      formData: {
        candidateType: "",
        idcarda: "",
        idcardb: "",
        graduation: "",
        education: "",
        work: "",
        other: "",
        social: "",
        diploma: "",
      },
      pdfUrl: "",
      dialogVisible: false,
      labelList: [
        "idcarda",
        "graduation",
        "education",
        "work",
        "social",
        "other",
      ],
      labelList1: [
        "idcarda",
        "diploma",
        "education",
        "work",
        "social",
        "other",
      ],
      labelList2: ["idcarda", "diploma", "education", "work", "other"],
      labelList3: ["idcarda", "graduation", "education", "work", "other"],
      rules: {
        candidateType: [{ required: true, message: "未选择报考资质类别" }],
        idcarda: [{ required: true, message: "未上传身份复印件面" }],
        // idcardb: [{ required: true, message: "未上传身份证反面" }],
        graduation: [{ required: true, message: "未上传学历/学位证书" }],
        education: [
          { required: true, message: "未上传学历/学位在线验证/认证报告" },
        ],
        work: [{ required: true, message: "未上传从业年限证明（单位盖章）" }],
        diploma: [{ required: true, message: "未上传学历/学位证书" }],
        social: [{ required: true, message: "未上传社保证明" }],
      },
      peopleId: "",
    };
  },
  created() {
    this.getData();
  },
  methods: {
    getData() {
      getInfoByIdCard({
        // IdCard: this.$store.getters.name,
        candidateType: sessionStorage.getItem("candidateType"),
      }).then((res) => {
        console.log("res", res);
        this.hand = res.data.hand;
        this.type = res.data.candidateType;
        this.peopleId = res.data.id;
        this.$nextTick(() => {
          this.formData.candidateType = res.data.candidateType + "";
        });

        if (!res.data) {
          this.$message.error(res.msg);
        }
        if (!res.data?.flag) {
          this.$message.error("请先完善考生信息");
          this.$router.push({ name: "ImproveInformation" });
        }
        if (res.data.personDataList) {
          // 一级建筑师
          if (this.type == 1) {
            // 存在公司
            if (this.hand == 0) {
              res.data.personDataList.map((item) => {
                this.formData[this.labelList2[item.type]] = item.path;
              });
            }
            // 不存在公司
            else {
              res.data.personDataList.map((item) => {
                this.formData[this.labelList1[item.type]] = item.path;
              });
            }
          }
          // 其他建筑师
          else {
            // 存在公司
            if (this.hand == 0) {
              res.data.personDataList.map((item) => {
                this.formData[this.labelList3[item.type]] = item.path;
              });
            }
            // 不存在公司
            else {
              res.data.personDataList.map((item) => {
                this.formData[this.labelList[item.type]] = item.path;
              });
            }
          }
        }
        // if ([0, 2, 3].includes(res.data?.auditList[0]?.auditStatus)) {
        //   this.$message.success("已提交审核");
        //   this.isFinish = true;
        // } else {
        //   this.isFinish = false;
        // }
        if ([1, 6, 7, 9].includes(res.data?.auditStatus)) {
          this.isFinish = false;
        } else {
          this.$message.success("已提交审核");
          this.isFinish = true;
        }
      });
    },
    submitForm() {
      if (this.type == 1) {
        this.$refs.elform.validate((valid) => {
          if (valid) {
            const params = {
              array: [
                this.formData.idcarda,
                this.formData.diploma,
                this.formData.education,
                this.formData.work,
                this.formData.social,
                this.formData.other,
              ],
              candidateType: this.type,
            };
            params.array = params.array.filter((file) => file);
            addData(params).then((res) => {
              this.$message.success("提交审核成功");
              this.$router.push({ name: "ExamineeHome" });
            });
          }
        });
      } else {
        this.$refs.elform.validate((valid) => {
          if (valid) {
            const params = {
              array: [
                this.formData.idcarda,
                this.formData.graduation,
                this.formData.education,
                this.formData.work,
                this.formData.social,
                this.formData.other,
              ],
              candidateType: this.type,
            };
            params.array = params.array.filter((file) => file);
            addData(params).then((res) => {
              this.$message.success("提交审核成功");
              this.$router.push({ name: "ExamineeHome" });
            });
          }
        });
      }
    },
    debounceClick: _.debounce(function () {
      this.submitForm();
    }, 1000),
    change(e) {
      this.type = e;
    },
    clickHandle() {
      window.open("http://www.cpta.com.cn/testCondition/1091.html");
    },
    getBack() {
      console.log(this.peopleId);
      back(this.peopleId).then(() => {
        this.$router.push({ path: "/examinee/improveInformation" });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.sp1 {
  color: red;
  margin-left: 20px;
}
.sp2 {
  color: green;
}
.ziLiao {
  margin-bottom: 20px;
  span {
    color: rgb(69, 161, 249);
    border-bottom: rgb(69, 161, 249) 1px solid;
    cursor: pointer;
  }
}
.box {
  display: inline-block;
}
.boxFont {
  position: absolute;
  left: 320px;
  color: rgb(131, 130, 128);
  top: 0;
}
.helpBox {
  display: flex;
  height: 25px;
  .helpImg {
    height: 100%;
    margin-top: 0.13%;
    margin-left: 0.1%;
    img {
      height: 60%;
    }
  }
}
</style>
