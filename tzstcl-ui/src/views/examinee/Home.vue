<template>
  <el-card>
    <div>
      <p class="welcome">
        您好，{{ nickName }}（{{ idCard }}），欢迎进入
        <span style="font-weight: bold">{{ dictLabel }}</span>
        资格后审系统！
        <el-button type="primary" style="margin-left: 30px" @click="back">
          返回
        </el-button>
      </p>
      <el-steps align-center>
        <el-step
          @click.native="
            !isFinish && $router.push({ name: 'ImproveInformation' })
          "
          title="完善基础资料"
          :description="`您的资料${isFinish ? '已' : '还未'}完善`"
          :status="isFinish ? 'success' : 'finish'"
          style="cursor: pointer"
        ></el-step>
        <el-step
          @click.native="
            isFinish &&
              dataNum < 5 &&
              $router.push({ name: 'ExamineeDataUpload' })
          "
          title="上传审核资料"
          :description="dataDescription"
          :status="dataStatus"
          style="cursor: pointer"
        ></el-step>
        <el-step
          @click.native="
            auditStatus != '未提交审核' &&
              $router.push({ name: 'ExamineeResult' })
          "
          title="企业验证"
          :description="enterpriseAuditDesc"
          :status="enterpriseAuditStatus"
          style="cursor: pointer"
        ></el-step>
        <el-step
          @click.native="
            auditStatus != '未提交审核' &&
              $router.push({ name: 'ExamineeResult' })
          "
          title="主管部门审核"
          :description="deptAuditDesc"
          :status="deptAuditStatus"
          style="cursor: pointer"
        ></el-step>
      </el-steps>
      <div class="warm">
        <p>温馨提示</p>
        <p>1.在【完善基础资料】里查看并确认个人资料填写无误。</p>
        <p>
          2.完善基础资料后，在【上传审核资料】中上传文件。上传完成后可以撤回并重新编辑【考生信息完善】页面相关信息。
        </p>
        <p>
          3.申请提交后，由企业及主管部门进行审查，审查最终结果可在系统中查看。
        </p>
        <p style="color: red">
          4.提交后请考生告知所在企业，通过河南省建筑市场监管公共服务平台（http://hngcjs.hnjs.henan.gov.cn/）登录企业账号后在【业务管理-河南省建设类职业资格考试审核】中进行审核。
        </p>

        <!-- <p style="color: red">
          5.关于做好2023年度全国一、二级注册建筑师资格考试考务工作的通知（http://www.hnrsks.com/sitesources/hnsrskszx/page_pc/ksxxnew/article73bed614373c4c07939797b77bd29770.html）
        </p> -->
        <p>备注</p>
        <p>
          1.完善考生信息里选择企业信息时，如所在企业是省四库内查询不到信息的企业或外省企业时，需要选择“手动录入”企业名称和信用代码。
        </p>
        <p>
          2.完善考生信息里确认信息后，在提交审核之前可以点击资料上传界面的“返回”按钮，可重新完善考生信息。
        </p>
        <p>
          3.上传资料界面的“其他”项为非必填项，如该项未上传附件，在首页会提示有一项资料未上传，为正常现象。
        </p>
        <p>
          4.提交审核后，在企业审核通过之前，可以在查看审核结果中点击“撤回申请”，撤回后可重新完善考生信息和修改上传的附件。
        </p>
      </div>
    </div>
  </el-card>
</template>
<script>
import { getData } from "@/api/examinee";
import { getDicts } from "@/api/system/dict/data.js";
import { getInfoByIdCard } from "@/api/examinee";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      auditStatus: "未提交审核",
      dataNum: 0,
      isFinish: 0,
      dictLabel: "",
      nickName: "",
      idCard: "",
    };
  },
  computed: {
    // ...mapGetters(["nickName", "name"]),
    dataDescription() {
      if (
        ["需补交资料", "企业驳回", "未提交审核", "未参加审核"].includes(
          this.auditStatus
        )
      ) {
        return "补交资料";
      } else {
        return this.dataNum >= 5
          ? "资料已上传"
          : `还差${5 - this.dataNum}个资料未上传`;
      }
    },
    dataStatus() {
      if (
        ["需补交资料", "企业驳回", "未提交审核", "未参加审核"].includes(
          this.auditStatus
        )
      ) {
        return "process";
      } else {
        return this.dataNum >= 4
          ? "success"
          : this.isFinish
          ? "finish"
          : "process";
      }
    },
    enterpriseAuditStatus() {
      if (
        ["考生撤回", "需补交资料", "企业驳回", "未参加审核"].includes(
          this.auditStatus
        )
      ) {
        this.auditStatus = "未提交审核";
      }
      return this.auditStatus == "未提交审核"
        ? "process"
        : this.auditStatus == "待企业审核"
        ? "finish"
        : this.auditStatus == "企业驳回"
        ? "error"
        : "success";
    },
    enterpriseAuditDesc() {
      return this.enterpriseAuditStatus == "success"
        ? "企业审核通过"
        : this.auditStatus;
    },
    deptAuditStatus() {
      return this.auditStatus == "待主管部门审核"
        ? "finish"
        : this.auditStatus == "主管部门驳回" || this.auditStatus == "需现场核实"
        ? "error"
        : this.auditStatus == "主管部门审核通过"
        ? "success"
        : "process";
    },
    deptAuditDesc() {
      return this.deptAuditStatus == "process" ? "" : this.auditStatus;
    },
  },

  created() {
    this.getData();
    this.getInfo();
    if (!sessionStorage.getItem("isShow")) {
      this.$router.go(0);
    }
  },
  methods: {
    getData() {
      console.log(this.$store.getters);
      getData({ candidateType: sessionStorage.getItem("candidateType") }).then(
        (res) => {
          this.auditStatus = res.data.auditStatus;
          this.dataNum = res.data.dataNum;
          this.isFinish = res.data.isFinish;
        }
      );
      getInfoByIdCard({
        // IdCard: this.$store.getters.name,
        candidateType: sessionStorage.getItem("candidateType"),
      }).then((res) => {
        this.nickName = res.data.name;
        this.idCard = res.data.idCard;
      });
    },
    getInfo() {
      getDicts("candidate_type").then((res) => {
        res.data.forEach((item) => {
          if (item.dictValue == sessionStorage.getItem("candidateType")) {
            this.dictLabel = item.dictLabel;
          }
        });
      });
    },
    back() {
      this.$router.push("/waitLogin");
    },
  },
};
</script>

<style lang="scss" scoped>
.welcome {
  text-align: center;
  padding-top: 30px;
}
.warm {
  padding-top: 150px;
  padding-bottom: 50px;
}
.el-steps {
  padding-top: 80px;
}
</style>
