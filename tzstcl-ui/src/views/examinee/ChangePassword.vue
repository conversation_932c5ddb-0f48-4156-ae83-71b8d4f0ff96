<template>
  <el-card header="修改密码">
    <el-form ref="form" :model="user" :rules="rules" label-width="100px">
      <el-row>
        <el-col :span="9">
          <el-form-item label="原始密码" prop="oldPassword">
            <el-input
              v-model="user.oldPassword"
              placeholder="请输入原始密码"
              type="password"
              show-password
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="9">
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="user.newPassword"
              placeholder="请输入新密码"
              type="password"
              show-password
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="9">
          <el-form-item label="确认新密码" prop="confirmPassword">
            <el-input
              v-model="user.confirmPassword"
              placeholder="请确认新密码"
              type="password"
              show-password
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="9">
          <el-form-item>
            <el-button type="primary" @click="submit">确认修改</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
import { updateUserPwd } from "@/api/system/user";

export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      user: {
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined,
      },
      // 表单校验
      rules: {
        oldPassword: [
          { required: true, message: "旧密码不能为空", trigger: "blur" },
        ],
        newPassword: [
          { required: true, message: "新密码不能为空", trigger: "blur" },
          {
            pattern:
              /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&.])[A-Za-z\d@$!%*?&.]{8,20}$/,
            message:
              "密码必须包含（大小写字母，数字，特殊字符@$!%*?&.），长度在 8 到 20 个字符之间",
            trigger: "blur",
          },
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { required: true, validator: equalToPassword, trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    submit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          updateUserPwd(this.user.oldPassword, this.user.newPassword).then(
            (response) => {
              this.$modal.msgSuccess("修改成功");
            }
          );
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 30px;
}
</style>
