<template>
  <div class="app-container">
    <div class="menu">
      <div
        v-for="item in menu"
        :key="item.label"
        class="menu-item"
        :class="`menu-item${$route.name == item.name ? '-active' : ''}`"
        @click="$router.push(item)"
      >
        <p>{{ item.label }}</p>
      </div>
    </div>
    <router-view />
  </div>
</template>

<script>
export default {
  data() {
    return {
      menu: [
        { label: "首页", name: "ExamineeHome" },
        { label: "考生信息完善", name: "ImproveInformation" },
        { label: "资料上传", name: "ExamineeDataUpload" },
        { label: "查看审核结果", name: "ExamineeResult" },
        { label: "修改密码", name: "ExamineeChangePassword" },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  min-height: calc(100vh - 70px);
  background-color: #f5f7f9;
  padding: 10px;
  .menu {
    margin: -5px;
    margin-bottom: 5px;
    display: flex;
    cursor: pointer;
    &-item {
      background-color: #fff;
      text-align: center;
      margin: 5px;
      width: 20%;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      &-active {
        color: #2491fc;
        background-color: #effaff;
      }
    }
  }
}
</style>
