<template>
  <div>
    <el-card header="信息完善">
      <div v-if="formData" style="width: 80%">
        <!-- <span style="color: #45a1f9; font-weight: bold">
          如发现信息有误，请联系考试中心
        </span> -->
        <el-descriptions
          border
          :column="2"
          :labelStyle="{ textAlign: 'right', width: '110px' }"
        >
          <el-descriptions-item label="姓名">
            <div style="width: 200px">
              {{ formData.name }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="性别">
            <div style="width: 200px">
              <dict-tag
                :options="dict.type.sys_user_sex"
                :value="formData.sex"
              />
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="国籍">
            {{ formData.nationality }}
          </el-descriptions-item>
          <!-- <el-descriptions-item label="民族">
            <dict-tag
              v-if="formData.flag"
              :options="dict.type.nation"
              :value="formData.nation"
            />
            <el-select v-else v-model="formData.nation" placeholder="请选择">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-descriptions-item> -->
          <el-descriptions-item label="身份证号" :span="2">
            {{ formData.idCard }}
          </el-descriptions-item>
          <!-- <el-descriptions-item label="政治面貌">
            <dict-tag
              :options="dict.type.political_outlook"
              :value="formData.politicalOutlook"
            />
          </el-descriptions-item> -->
          <el-descriptions-item>
            <span slot="label" :class="formData.flag ? '' : 'require'">
              手机号
            </span>
            <span v-if="formData.flag">{{ formData.phoneNumber }}</span>
            <el-input v-else v-model="formData.phoneNumber"></el-input>
          </el-descriptions-item>
          <el-descriptions-item label="毕业院校">
            {{ formData.school }}
            <!-- <span v-if="formData.flag">{{ formData.school }}</span>
            <el-input
              v-else
              v-model="formData.school"
              style="width: 70%"
            ></el-input> -->
          </el-descriptions-item>
          <el-descriptions-item label="学历">
            <dict-tag
              :options="dict.type.education"
              :value="formData.education"
            />
          </el-descriptions-item>
          <el-descriptions-item label="所学专业">
            {{ formData.specializedSubject }}
            <!-- <dict-tag
              :options="dict.type.specialized_subject"
              :value="formData.specializedSubject"
            /> -->
          </el-descriptions-item>
          <el-descriptions-item label="毕业时间">
            {{ formData.graduationTime }}
            <!-- {{ parseTime(formData.graduationTime, "{y}-{m}-{d}") }} -->
            <!-- <span v-if="formData.flag">{{ formData.graduationTime }}</span>
            <el-input
              v-else
              v-model="formData.graduationTime"
              style="width: 70%"
            ></el-input> -->
          </el-descriptions-item>
          <el-descriptions-item label="专业工作年限">
            <!-- {{ formData.professionalWorkTime }} -->

            <span v-if="formData.flag">
              {{ formData.professionalWorkTime }}
            </span>
            <el-input
              v-else
              v-model="formData.professionalWorkTime"
              style="width: 70%"
            ></el-input>
          </el-descriptions-item>
          <el-descriptions-item label="通讯地址">
            <!-- {{ formData.address }} -->
            <span v-if="formData.flag">{{ formData.address }}</span>
            <el-input
              v-else
              v-model="formData.address"
              style="width: 70%"
            ></el-input>
          </el-descriptions-item>
          <el-descriptions-item label=""></el-descriptions-item>
          <el-descriptions-item :span="2">
            <span slot="label" :class="formData.flag ? '' : 'require'">
              工作单位
            </span>
            <span v-if="formData.flag">{{ formData.work }}</span>
            <el-input
              v-else
              v-model="formData.work"
              readonly
              style="width: 70%"
            ></el-input>
            <el-button
              v-if="!formData.flag"
              type="primary"
              plain
              @click="enterpriseShow = true"
            >
              选择企业信息
            </el-button>
            <span v-if="!formData.flag" class="box">
              工作单位为平台内企业时，请认真核对企业名称是否正确，后续需企业网上确认；工作单位非平台内企业时，请手动填写工作单位名称并上传其它工作年限证明材料
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="报考地市">
            {{ formData.cityName }}
          </el-descriptions-item>
          <el-descriptions-item label="报考专业">
            {{ formData.professional }}
            <!-- {{
              selectDictLabel(dict.type.exam_subject, formData.examSubject) ||
              formData.examSubject
            }} -->
          </el-descriptions-item>
          <el-descriptions-item label="档案号">
            {{ formData.archivesNo }}
          </el-descriptions-item>
          <el-descriptions-item label="报考类型">
            {{ formData.type }}
          </el-descriptions-item>
        </el-descriptions>
        <el-table :data="formData.examInfoList" border stripe>
          <el-table-column
            label="科目"
            prop="examSubject"
            align="center"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            label="考号"
            prop="examCode"
            align="center"
            show-overflow-tooltip
          ></el-table-column>
        </el-table>
        <el-button
          v-if="!formData.flag"
          class="mt10 ml20"
          type="primary"
          @click="submitForm()"
        >
          确认信息
        </el-button>
      </div>
      <el-empty v-else />
    </el-card>
    <EnterpriseDialog
      :show.sync="enterpriseShow"
      @confirm="enterpriseConfirm"
    />
  </div>
</template>

<script>
import { getInfoByIdCard, editPersonInfo } from "@/api/examinee";
import EnterpriseDialog from "@/views/components/EnterpriseDialog/EnterpriseDialog";
export default {
  dicts: [
    "sys_user_sex",
    "nation",
    "political_outlook",
    "education",
    "specialized_subject",
    "exam_subject",
  ],
  components: { EnterpriseDialog },
  data() {
    return {
      enterpriseShow: false,
      formData: {
        work: "",
        phoneNumber: "",
      },
      options: [
        {
          value: "1",
          label: "汉族",
        },
        {
          value: "2",
          label: "少数民族",
        },
        {
          value: "3",
          label: "其他",
        },
      ],
      value: "",
    };
  },
  created() {
    this.getData();
  },
  methods: {
    getData() {
      getInfoByIdCard({
        // IdCard: this.$store.getters.name,
        candidateType: sessionStorage.getItem("candidateType"),
      }).then((res) => {
        this.formData = res.data;
        // console.log(1, this.formData.graduationTime);
        // this.formData.graduationTime = this.parseTime(
        //   this.formData.graduationTime,
        //   "{y}-{m}-{d}"
        // );
        // console.log(2, this.formData.graduationTime);
        if (!res.data) {
          this.$message.error(res.msg);
        }
        if (res.data?.flag) {
          this.$message.success("信息已完善");
        }
      });
    },
    enterpriseConfirm(val) {
      if (val) {
        this.formData.work = val.corpName;
        this.formData.corpCode = val.corpCode;
        this.formData.hand = val.hand;
      }
    },
    submitForm() {
      if (!this.formData.hand) {
        this.formData.hand = 0;
      }
      if (this.formData.phoneNumber && this.formData.work) {
        editPersonInfo(this.formData).then((res) => {
          console.log(res);
          this.$message.success(res.msg);
          this.$router.push({ name: "ExamineeDataUpload" });
        });
      } else {
        return this.$message.error("请完善信息");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.el-table {
  border: 1px solid #ccc;
  text-align: center;
}
.require::before {
  content: "*";
  color: red;
}
.box {
  color: red;
  float: right;
  margin-top: 10px;
  margin-right: 5px;
}
</style>
