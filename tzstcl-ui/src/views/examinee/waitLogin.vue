<template>
  <div class="box">
    <header>
      您好，考生{{ name }}&emsp;({{ peopleId }})，欢迎您登录本系统
    </header>
    <div class="title">请选择报考资质类别</div>
    <el-table :data="tableData" style="width: 100%" border>
      <el-table-column label="序号" type="index" width="100"></el-table-column>
      <el-table-column label="报考资质类别" width="180" prop="candidateType">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.candidate_type"
            :value="scope.row.candidateType"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="信息录入时间"
        width="180"
        prop="createTime"
      ></el-table-column>
      <el-table-column label="审核状态" width="200" prop="auditStatus">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.audit_status"
            :value="scope.row.auditStatus"
          />
        </template>
      </el-table-column>

      <el-table-column label="信息填报">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleEdit(scope.$index, scope.row)"
          >
            开始填报
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { loginInfo, login } from "@/api/waitLogin.js";

export default {
  data() {
    return {
      tableData: [],
      list: [],
      name: "",
      peopleId: "",
    };
  },
  dicts: ["audit_status", "candidate_type"],
  mounted() {
    this.getInfo();
  },
  methods: {
    getInfo() {
      loginInfo().then((res) => {
        this.name = res.data[0].name;
        this.peopleId = res.data[0].idCard;
        res.data.forEach((item) => {
          this.tableData.push({
            candidateType: item.candidateType,
            createTime: item.createTime,
            auditStatus: item.auditStatus,
            type: item.candidateType,
          });
        });
      });
    },
    handleEdit(v, item) {
      login({ candidateType: item.type }).then((res) => {
        this.$router.push({ path: "/examinee/home" });
        sessionStorage.setItem("isShow", true);
        sessionStorage.setItem("candidateType", item.type);
        // this.$alert(
        //   "",
        //   "提示",
        //   {
        //     confirmButtonText: "确定",
        //   }
        // );
        const h = this.$createElement;
        this.$msgbox({
          title: "提示",
          message: h("p", null, [
            h(
              "div",
              "1.考后资格核查不委托任何中介或代办机构，请考生按照报名证明事项告知承诺上传资料，诚信参加。 "
            ),
            h(
              "div",
              "2.切勿采信非官方信息，委托他人代办进行资料造假，凡是提供虚假证明材料将不予通过，并按有关规定将其违纪违规行为记入专业技术人员资格考试诚信档案库。"
            ),
            h(
              "div",
              "3.如资料存疑或缺失情况补充核查期间将有机会进行补交进一步核实。"
            ),
            h(
              "div",
              "4.核查通知请至官网https://hnjs.henan.gov.cn/zwdt/gsgg/查看。"
            ),
          ]),
          confirmButtonText: "确定",
        });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.box {
  width: 800px;
  margin: 0 auto;
}
.title {
  width: 100%;
  text-align: center;
  height: 70px;
  line-height: 70px;
}
header {
  width: 100%;
  text-align: center;
  margin-top: 40px;
}
</style>
