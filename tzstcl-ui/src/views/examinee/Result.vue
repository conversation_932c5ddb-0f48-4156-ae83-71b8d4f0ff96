<template>
  <el-card header="审核结果" v-loading="loading">
    <el-tabs v-if="auditData.length > 0" type="card">
      <el-tab-pane
        v-for="(item, index) in auditData"
        :key="item.id"
        :label="item.number"
      >
        <el-descriptions
          :column="2"
          border
          :labelStyle="{ width: '120px', textAlign: 'right' }"
        >
          <el-descriptions-item label="申请次数" class="head">
            <div style="width: 200px">
              {{ item.number }}
            </div>

            <el-button
              class="btn"
              v-if="item.auditStatus == 0"
              type="primary"
              @click="centerDialogVisible = true"
            >
              撤回申请
            </el-button>

            <el-dialog
              title="提示"
              :visible.sync="centerDialogVisible"
              width="30%"
              center
              :modal="false"
            >
              <span>是否撤销当前申请（企业验证阶段可撤销）</span>
              <span slot="footer" class="dialog-footer">
                <el-button @click="centerDialogVisible = false">
                  取 消
                </el-button>
                <el-button
                  type="primary"
                  @click="(centerDialogVisible = false), clickHandle(item.id)"
                >
                  确 定
                </el-button>
              </span>
            </el-dialog>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">
            <div style="width: 200px">
              {{ item.time }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          :column="2"
          border
          :labelStyle="{ width: '120px', textAlign: 'right' }"
          v-for="record in item.auditRecord"
          :key="record.id"
        >
          <el-descriptions-item
            :label="
              selectDictLabel(dict.type.audit_type, record.auditType) + '时间'
            "
          >
            <div style="width: 200px">
              {{ record.time }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="审核结果">
            <div style="width: 200px">
              {{ selectDictLabel(dict.type.audit_result, record.auditResult) }}
              <!-- <el-button
                type="primary"
                size="mini"
                v-if="record.auditResult == 1 && index == 0"
                @click="$router.push({ name: 'ImproveInformation' })"
              >
                重新填写
              </el-button> -->
            </div>
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="审核意见">
            {{ record.opinion }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="审核附件">
            <div class="uploadBox">
              <FileUpload
                v-model="record.cityFile"
                :disabled="true"
                :noUpload="true"
              />
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-tab-pane>
    </el-tabs>
    <el-empty v-else></el-empty>
  </el-card>
</template>

<script>
import { mapGetters } from "vuex";
import { getAuditLog, delQuash } from "@/api/examinee";
import FileUpload from "@/views/components/FileUpload/FileUpload";
export default {
  dicts: ["audit_result", "audit_type"],
  components: { FileUpload },
  data() {
    return {
      loading: false,
      auditData: [],
      centerDialogVisible: false,
    };
  },
  computed: {
    ...mapGetters(["nickName", "name"]),
  },
  created() {
    this.getAuditLog();
  },
  methods: {
    getAuditLog() {
      this.loading = true;
      getAuditLog({
        // IdCard: this.name,
        candidateType: sessionStorage.getItem("candidateType"),
      }).then((res) => {
        this.auditData = res.data;

        this.loading = false;
      });
    },
    clickHandle(val) {
      delQuash({ id: val }).then((res) => {
        this.$message({
          message: res.msg,
          type: "success",
        });
        this.getAuditLog();
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-descriptions-row {
  position: relative;
}
</style>
<style lang="scss" scoped>
.descriptionsContent {
  width: calc(50% - 120px);
}
.btn {
  position: absolute;
  left: 250px;
  top: 3px;
}
.uploadBox {
  display: flex;
  align-items: center;
}
::v-deep .el-upload-list__item:first-child {
  margin-top: 20px;
}
</style>
