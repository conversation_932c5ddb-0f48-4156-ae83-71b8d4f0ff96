<template>
  <div class="login">
    <el-form
      ref="loginForm"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
    >
      <h3 class="title">河南省建设类职业资格考试审核</h3>
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          type="text"
          auto-complete="off"
          placeholder="身份证号"
        >
          <svg-icon
            slot="prefix"
            icon-class="user"
            class="el-input__icon input-icon"
          />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          placeholder="密码"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon
            slot="prefix"
            icon-class="password"
            class="el-input__icon input-icon"
          />
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          placeholder="请输入计算结果"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon
            slot="prefix"
            icon-class="validCode"
            class="el-input__icon input-icon"
          />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img" />
        </div>
      </el-form-item>
      <el-checkbox
        v-model="loginForm.rememberMe"
        style="margin: 0px 0px 25px 0px"
      >
        记住密码
      </el-checkbox>
      <el-button
        type="text"
        style="float: right; padding: 0"
        @click="$router.push('/resetPwd')"
      >
        忘记密码?
      </el-button>
      <el-form-item style="width: 100%">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width: 100%"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
      <el-form-item style="width: 100%">
        <el-button
          size="medium"
          style="width: 100%"
          @click="$router.push('/register')"
        >
          立即注册
        </el-button>
      </el-form-item>
      <div class="box">
        <span>1.请考生在审核期限内完成注册，完善个人资料后参与审核。</span>
        <br />

        <!-- <span>2.联系我们：0371-55673016/0371-63284897</span> -->
        <el-popover
          placement="top-start"
          style="width: 200px; height: 200px"
          trigger="hover"
        >
          <el-image
            style="width: 150px; height: 150px"
            :src="urlImg"
            :preview-src-list="srcList"
          ></el-image>
          <span style="cursor: pointer" slot="reference">
            <div style="display: flex; align-items: center">
              2.
              <img
                style="width: 20px; height: 20px; margin: 0 3px"
                src="@/assets/images/wx.png"
              />
              <span>技术运维群</span>
            </div>
          </span>
        </el-popover>
        <span>
          3.
          <el-link type="primary" href="https://kdocs.cn/l/cjZvle6uRxvx">
            操作手册
          </el-link>
        </span>
        <br />
        <span>
          4.
          <el-link type="primary" href="https://kdocs.cn/l/cqzCR0IUDMsB">
            常见问题
          </el-link>
        </span>
        <br />
      </div>
      <div style="text-align: center; margin-bottom: 22px">
        <!-- 技术支持QQ群 341618691 -->
      </div>
    </el-form>
    <div class="qrSty">
      <el-image
        style="width: 80px; height: 80px"
        :src="urlImg"
        :preview-src-list="srcList"
      ></el-image>
      <span>技术运维群</span>
    </div>

    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 天筑科技股份有限公司 All Rights Reserved.</span>
    </div>
  </div>
</template>

<script></script>

<style rel="stylesheet/scss" lang="scss"></style>
