<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="国籍" prop="nationality">
        <el-input
          v-model="queryParams.nationality"
          placeholder="请输入国籍"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="民族" prop="nation">
        <el-input
          v-model="queryParams.nation"
          placeholder="请输入民族"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="身份证号" prop="idCard">
        <el-input
          v-model="queryParams.idCard"
          placeholder="请输入身份证号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="政治面貌" prop="politicalOutlook">
        <el-input
          v-model="queryParams.politicalOutlook"
          placeholder="请输入政治面貌"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phoneNumber">
        <el-input
          v-model="queryParams.phoneNumber"
          placeholder="请输入手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="毕业院校" prop="school">
        <el-input
          v-model="queryParams.school"
          placeholder="请输入毕业院校"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学历" prop="education">
        <el-input
          v-model="queryParams.education"
          placeholder="请输入学历"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所学专业" prop="specializedSubject">
        <el-input
          v-model="queryParams.specializedSubject"
          placeholder="请输入所学专业"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="毕业时间" prop="graduationTime">
        <el-date-picker clearable
          v-model="queryParams.graduationTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择毕业时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="参加工作年限" prop="workTime">
        <el-input
          v-model="queryParams.workTime"
          placeholder="请输入参加工作年限"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="专业工作年限" prop="professionalWorkTime">
        <el-input
          v-model="queryParams.professionalWorkTime"
          placeholder="请输入专业工作年限"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="专业职称" prop="professionalTitle">
        <el-input
          v-model="queryParams.professionalTitle"
          placeholder="请输入专业职称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="专业职务" prop="professionalPosition">
        <el-input
          v-model="queryParams.professionalPosition"
          placeholder="请输入专业职务"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="专业职务聘任时间" prop="professionalPositionTime">
        <el-input
          v-model="queryParams.professionalPositionTime"
          placeholder="请输入专业职务聘任时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="通讯地址" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入通讯地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工作单位" prop="work">
        <el-input
          v-model="queryParams.work"
          placeholder="请输入工作单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单位性质(0-民营企业，1-国有企业)" prop="workNature">
        <el-input
          v-model="queryParams.workNature"
          placeholder="请输入单位性质(0-民营企业，1-国有企业)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="照片" prop="photo">
        <el-input
          v-model="queryParams.photo"
          placeholder="请输入照片"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['person:info:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['person:info:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['person:info:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['person:info:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="infoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="性别" align="center" prop="sex" />
      <el-table-column label="国籍" align="center" prop="nationality" />
      <el-table-column label="民族" align="center" prop="nation" />
      <el-table-column label="身份证号" align="center" prop="idCard" />
      <el-table-column label="政治面貌" align="center" prop="politicalOutlook" />
      <el-table-column label="手机号" align="center" prop="phoneNumber" />
      <el-table-column label="毕业院校" align="center" prop="school" />
      <el-table-column label="学历" align="center" prop="education" />
      <el-table-column label="所学专业" align="center" prop="specializedSubject" />
      <el-table-column label="毕业时间" align="center" prop="graduationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.graduationTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="参加工作年限" align="center" prop="workTime" />
      <el-table-column label="专业工作年限" align="center" prop="professionalWorkTime" />
      <el-table-column label="专业职称" align="center" prop="professionalTitle" />
      <el-table-column label="专业职务" align="center" prop="professionalPosition" />
      <el-table-column label="专业职务聘任时间" align="center" prop="professionalPositionTime" />
      <el-table-column label="通讯地址" align="center" prop="address" />
      <el-table-column label="工作单位" align="center" prop="work" />
      <el-table-column label="单位性质(0-民营企业，1-国有企业)" align="center" prop="workNature" />
      <el-table-column label="照片" align="center" prop="photo" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['person:info:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['person:info:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改考生信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="国籍" prop="nationality">
          <el-input v-model="form.nationality" placeholder="请输入国籍" />
        </el-form-item>
        <el-form-item label="民族" prop="nation">
          <el-input v-model="form.nation" placeholder="请输入民族" />
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="政治面貌" prop="politicalOutlook">
          <el-input v-model="form.politicalOutlook" placeholder="请输入政治面貌" />
        </el-form-item>
        <el-form-item label="手机号" prop="phoneNumber">
          <el-input v-model="form.phoneNumber" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="毕业院校" prop="school">
          <el-input v-model="form.school" placeholder="请输入毕业院校" />
        </el-form-item>
        <el-form-item label="学历" prop="education">
          <el-input v-model="form.education" placeholder="请输入学历" />
        </el-form-item>
        <el-form-item label="所学专业" prop="specializedSubject">
          <el-input v-model="form.specializedSubject" placeholder="请输入所学专业" />
        </el-form-item>
        <el-form-item label="毕业时间" prop="graduationTime">
          <el-date-picker clearable
            v-model="form.graduationTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择毕业时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="参加工作年限" prop="workTime">
          <el-input v-model="form.workTime" placeholder="请输入参加工作年限" />
        </el-form-item>
        <el-form-item label="专业工作年限" prop="professionalWorkTime">
          <el-input v-model="form.professionalWorkTime" placeholder="请输入专业工作年限" />
        </el-form-item>
        <el-form-item label="专业职称" prop="professionalTitle">
          <el-input v-model="form.professionalTitle" placeholder="请输入专业职称" />
        </el-form-item>
        <el-form-item label="专业职务" prop="professionalPosition">
          <el-input v-model="form.professionalPosition" placeholder="请输入专业职务" />
        </el-form-item>
        <el-form-item label="专业职务聘任时间" prop="professionalPositionTime">
          <el-input v-model="form.professionalPositionTime" placeholder="请输入专业职务聘任时间" />
        </el-form-item>
        <el-form-item label="通讯地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入通讯地址" />
        </el-form-item>
        <el-form-item label="工作单位" prop="work">
          <el-input v-model="form.work" placeholder="请输入工作单位" />
        </el-form-item>
        <el-form-item label="单位性质(0-民营企业，1-国有企业)" prop="workNature">
          <el-input v-model="form.workNature" placeholder="请输入单位性质(0-民营企业，1-国有企业)" />
        </el-form-item>
        <el-form-item label="照片" prop="photo">
          <el-input v-model="form.photo" placeholder="请输入照片" />
        </el-form-item>
        <el-form-item label="删除标记(0-未删除，1-已删除)" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标记(0-未删除，1-已删除)" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInfo, getInfo, delInfo, addInfo, updateInfo } from "@/api/person/info";

export default {
  name: "Info",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 考生信息表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        sex: null,
        nationality: null,
        nation: null,
        idCard: null,
        politicalOutlook: null,
        phoneNumber: null,
        school: null,
        education: null,
        specializedSubject: null,
        graduationTime: null,
        workTime: null,
        professionalWorkTime: null,
        professionalTitle: null,
        professionalPosition: null,
        professionalPositionTime: null,
        address: null,
        work: null,
        workNature: null,
        photo: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "姓名不能为空", trigger: "blur" }
        ],
        sex: [
          { required: true, message: "性别不能为空", trigger: "change" }
        ],
        nationality: [
          { required: true, message: "国籍不能为空", trigger: "blur" }
        ],
        nation: [
          { required: true, message: "民族不能为空", trigger: "blur" }
        ],
        idCard: [
          { required: true, message: "身份证号不能为空", trigger: "blur" }
        ],
        politicalOutlook: [
          { required: true, message: "政治面貌不能为空", trigger: "blur" }
        ],
        phoneNumber: [
          { required: true, message: "手机号不能为空", trigger: "blur" }
        ],
        school: [
          { required: true, message: "毕业院校不能为空", trigger: "blur" }
        ],
        education: [
          { required: true, message: "学历不能为空", trigger: "blur" }
        ],
        specializedSubject: [
          { required: true, message: "所学专业不能为空", trigger: "blur" }
        ],
        graduationTime: [
          { required: true, message: "毕业时间不能为空", trigger: "blur" }
        ],
        workTime: [
          { required: true, message: "参加工作年限不能为空", trigger: "blur" }
        ],
        professionalWorkTime: [
          { required: true, message: "专业工作年限不能为空", trigger: "blur" }
        ],
        address: [
          { required: true, message: "通讯地址不能为空", trigger: "blur" }
        ],
        createBy: [
          { required: true, message: "创建人不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建人时间不能为空", trigger: "blur" }
        ],
        updateBy: [
          { required: true, message: "更新人不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "更新时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询考生信息列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then(response => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        sex: null,
        nationality: null,
        nation: null,
        idCard: null,
        politicalOutlook: null,
        phoneNumber: null,
        school: null,
        education: null,
        specializedSubject: null,
        graduationTime: null,
        workTime: null,
        professionalWorkTime: null,
        professionalTitle: null,
        professionalPosition: null,
        professionalPositionTime: null,
        address: null,
        work: null,
        workNature: null,
        photo: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加考生信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改考生信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除考生信息编号为"' + ids + '"的数据项？').then(function() {
        return delInfo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('person/info/export', {
        ...this.queryParams
      }, `info_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
