<template>
  <div style="padding: 10px; background-color: #fff">
    <ExamineeInfo @clickType="clickType" />
    <FileTable />
    <PageTitle v-if="userId != '02'" title="审核日志"></PageTitle>
    <AuditLog v-if="userId != '02'" />
    <PageTitle v-if="!isDetail" title="审核"></PageTitle>
    <el-form
      v-if="!isDetail"
      ref="elForm"
      :model="formData"
      :rules="rules"
      size="medium"
      label-width="auto"
    >
      <el-form-item label="审核结果" prop="auditResult">
        <el-radio-group v-model="formData.auditResult">
          <el-radio :label="0">通过</el-radio>
          <el-radio :label="1">不通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核意见" prop="opinion">
        <el-input
          v-model="formData.opinion"
          type="textarea"
          placeholder="请输入办理意见"
          :autosize="{ minRows: 4, maxRows: 4 }"
          :style="{ width: '100%' }"
        ></el-input>
      </el-form-item>
    </el-form>
    <div class="center mb20 mt20">
      <el-button
        :type="isDetail ? 'primary' : ''"
        @click="$tab.closeOpenPage({ name: 'Enterprise' })"
      >
        返回
      </el-button>
      <el-button v-if="!isDetail" type="primary" @click="submitForm">
        提交
      </el-button>
    </div>
  </div>
</template>

<script>
import { enterpriseAudit } from "@/api/enterprise";

import ExamineeInfo from "@/views/components/ExamineeInfo/ExamineeInfo";
import FileTable from "@/views/components/FileTable/FileTable";
import AuditLog from "@/views/components/AuditLog/AuditLog";
export default {
  components: { ExamineeInfo, FileTable, AuditLog },
  data() {
    return {
      userId: "",
      isDetail: true,
      formData: { candidateType: "" },
      rules: {
        auditResult: [
          {
            required: true,
            message: "请选择审核结果",
            trigger: "blur",
          },
        ],
        opinion: [
          {
            required: true,
            message: "请输入审核意见",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.isDetail = this.$route.query.detail;
    this.clickType();
    this.userId = sessionStorage.getItem("user");
  },
  methods: {
    submitForm() {
      this.$refs["elForm"].validate((valid) => {
        if (valid) {
          this.formData.id = this.$route.query.id;

          enterpriseAudit(this.formData).then((res) => {
            this.$message.success(res.msg);
            this.$tab.closeOpenPage({ name: "Enterprise" });
          });
        }
      });
    },
    clickType(e) {
      if (e) {
        this.formData.candidateType = e;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.center {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
</style>
