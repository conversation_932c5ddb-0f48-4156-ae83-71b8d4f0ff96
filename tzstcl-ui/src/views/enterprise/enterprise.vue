<template>
  <div>
    <el-row style="padding: 5px">
      <el-col :span="18" style="padding: 5px">
        <el-card>
          <div slot="header" class="clearfix">
            <span>考生信息审核</span>
          </div>
          <ExamineList />
        </el-card>
      </el-col>
      <el-col
        :span="6"
        style="padding: 5px; display: flex; flex-direction: column"
      >
        <el-card header="数据统计">
          <DataStatistics />
        </el-card>
        <el-card
          header="审核通过人员名单"
          style="margin-top: 10px"
          :body-style="{ padding: '5px', paddingBottom: '15px' }"
        >
          <ApprovedList />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import ExamineList from "@/views/enterprise/components/ExamineList";
import DataStatistics from "@/views/enterprise/components/DataStatistics";
import ApprovedList from "@/views/enterprise/components/ApprovedList";

export default {
  components: { ExamineList, DataStatistics, ApprovedList },
  data() {
    return {};
  },
  created() {
    if (!sessionStorage.getItem("message")) {
      sessionStorage.setItem("message", 1);
      this.$alert(
        "如有提供虚假工作年限证明、劳动合同等其他不实审核材料的，经发现将计入建筑市场不良行为记录，并列为市场监管和资质动态核查重点对象，按照相关法律法规追究相应责任。",
        "提示",
        {
          confirmButtonText: "确定",
        }
      );
    }
  },
};
</script>
