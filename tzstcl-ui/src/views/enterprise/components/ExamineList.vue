<template>
  <div>
    <!-- 搜索 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="auto"
    >
      <el-form-item label="考生姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="输入考生姓名搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phoneNumber">
        <el-input
          v-model="queryParams.phoneNumber"
          placeholder="输入手机号搜索"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <!-- 操作 -->
    <div class="operation">
      <el-tabs v-model="tab" type="card" @tab-click="getList">
        <el-tab-pane label="全部" name="0" :disabled="loading"></el-tab-pane>
        <el-tab-pane label="待审核" name="1" :disabled="loading"></el-tab-pane>
        <el-tab-pane label="已审核" name="2" :disabled="loading"></el-tab-pane>
      </el-tabs>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
        :columns="columns"
        class="right"
      />
    </div>
    <!-- 表格 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column type="index" width="50" label="序号" align="center" />
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[0].label"
        :prop="columns[0].prop"
        v-if="columns[0].visible"
      ></el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[1].label"
        :prop="columns[1].prop"
        v-if="columns[1].visible"
      ></el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[2].label"
        :prop="columns[2].prop"
        v-if="columns[2].visible"
      ></el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[3].label"
        :prop="columns[3].prop"
        v-if="columns[3].visible"
      />
      <!-- <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[4].label"
        :prop="columns[4].prop"
        v-if="columns[4].visible"
      /> -->
      <!-- <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[5].label"
        :prop="columns[5].prop"
        v-if="columns[5].visible"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.audit_status"
            :value="scope.row.auditList[0].auditStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[6].label"
        :prop="columns[6].prop"
        v-if="columns[6].visible"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.audit_result"
            :value="scope.row.auditList[0].auditResult"
          />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[7].label"
        :prop="columns[7].prop"
        v-if="columns[7].visible"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.candidate_type"
            :value="scope.row.auditList[0].candidateType"
          />
        </template>
      </el-table-column> -->
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[5].label"
        :prop="columns[5].prop"
        v-if="columns[5].visible"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.audit_status"
            :value="scope.row.auditStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[6].label"
        :prop="columns[6].prop"
        v-if="columns[6].visible"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.audit_result"
            :value="scope.row.auditResult"
          />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[7].label"
        :prop="columns[7].prop"
        v-if="columns[7].visible"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.candidate_type"
            :value="scope.row.candidateType"
          />
        </template>
      </el-table-column>
      <!-- 
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[8].label"
        :prop="columns[8].prop"
        v-if="columns[8].visible"
      >
        <template slot-scope="scope">
          <span v-html="scope.row.registerStatus == 1 ? '是' : '否'"></span>
        </template>
      </el-table-column> -->

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="openForm(scope.row)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getAuditList } from "@/api/enterprise";

import listMixin from "@/mixin/listMixin";

export default {
  dicts: ["audit_status", "audit_result", "candidate_type"],
  mixins: [listMixin],
  data() {
    return {
      // 列表数据接口
      listAPI: getAuditList,
      tab: 0,
      type: [undefined, 0, 1],

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      resetIgnore: ["type"],
      // 隐藏显示列数据
      columns: [
        { key: 0, label: `姓名`, prop: "name", visible: true },
        { key: 1, label: `身份证号`, prop: "idCard", visible: true },
        { key: 2, label: `手机号`, prop: "phoneNumber", visible: true },
        { key: 3, label: `提交时间`, prop: "updateTime", visible: true },
        {
          key: 4,
          label: `提交次数`,
          prop: "auditList[0].number",
          visible: true,
        },
        {
          key: 5,
          label: `审核状态`,
          prop: "auditList[0].auditStatus",
          visible: true,
        },
        {
          key: 6,
          label: `企业验证结果`,
          prop: "auditList[0].auditResult",
          visible: true,
        },
        {
          key: 7,
          label: `报考资质类别`,
          prop: "candidateType",
          visible: true,
        },
        {
          key: 8,
          label: `是否禁用注册`,
          prop: "registerStatus",
          visible: true,
        },
      ],
      candidate: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      this.listAPI({ ...this.queryParams, type: this.type[this.tab] })
        .then((res) => {
          console.log(res.rows);
          this.list = res.rows;
          this.total = res.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    openForm(row) {
      if (row.registerStatus == 1) {
        this.$message({
          showClose: true,
          message: "审核时间已结束，如有异议请联系主管部门！",
          type: "error",
        });
      } else {
        this.$router.push({
          name: "EnterpriseForm",
          query: {
            id: row.auditList[0]?.id,
            examineeId: row.id,
            detail: row.auditList[0]?.auditStatus != 0,
            isDetail: true,
          },
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.operation {
  position: relative;
  margin-bottom: -15px;
  .right {
    position: absolute;
    right: 0;
    top: 0;
  }
}
</style>
