<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

import { getData } from '@/api/enterprise'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      getData({ corpCode: this.$store.getters.name }).then((res) => {
        const { sum, yes, no } = res.data
        const data = [
          { value: yes, name: '已审核' },
          { value: no, name: '未审核' }
        ]
        this.chart = echarts.init(this.$el, 'macarons')
        this.chart.setOption({
          title: {
            text: `总考生人数：${sum}人`,
            textStyle: {
              color: '#000'
            }
          },
          color: ['#5470c6', '#91cc75', '#fac858'],
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            bottom: '0',
            left: 'center',
            itemGap: 10,
            itemWidth: 20,
            itemHeight: 12,
            textStyle: {
              rich: {
                title: {
                  fontSize: 16,
                  fontWeight: 600,
                  padding: [0, 10, 0, 10],
                  width: 100
                },
                value: {
                  fontSize: 16,
                  fontWeight: 600,
                  padding: [0, 10, 0, 10]
                }
              }
            },
            formatter(val) {
              let value = null
              data.forEach((item) => {
                if (item.name == val) {
                  value = item.value
                }
              })
              return `{title|${val}}{value|${value}}`
            }
          },
          series: [
            {
              name: '考生数据统计',
              type: 'pie',
              data,
              radius: '60%',
              center: ['50%', '45%'],
              animationEasing: 'cubicInOut',
              animationDuration: 2600
            }
          ]
        })
      })
    }
  }
}
</script>
