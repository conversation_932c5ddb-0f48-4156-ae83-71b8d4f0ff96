<template>
  <div>
    <!-- 表格 -->
    <el-table v-loading="loading" :data="list" :show-header="false">
      <el-table-column
        align="center"
        show-overflow-tooltip
        prop="name"
      ></el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        prop="idCard"
      ></el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :pagerCount="5"
      layout="total,prev,pager,next"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getPassData } from '@/api/enterprise'
import listMixin from '@/mixin/listMixin'
export default {
  mixins: [listMixin],
  data() {
    return {
      // 列表数据接口
      listAPI: getPassData,
      // 查询参数
      queryParams: {
        type: 0,
        pageNum: 1,
        pageSize: 5
      }
    }
  },
}
</script>
