<template>
  <div>
    <PageTitle title="考生基本信息" />
    <table
      width="100%"
      border="0"
      cellspacing="1"
      cellpadding="4"
      bgcolor="#e9e9e9"
      class="table"
    >
      <tr>
        <td>姓名</td>
        <td>{{ examineeInfo.name }}</td>
        <td>性别</td>
        <td>
          {{ examineeInfo.sex == 1 ? "男" : "女" }}
        </td>
      </tr>
      <tr>
        <td>身份证号</td>
        <td>{{ examineeInfo.idCard }}</td>
        <td>出生日期</td>
        <td>{{ examineeInfo.birthday }}</td>
      </tr>
      <tr>
        <td>民族</td>
        <td>
          <dict-tag :options="dict.type.nation" :value="examineeInfo.nation" />
        </td>
        <td>政治面貌</td>
        <td>
          <dict-tag
            :options="dict.type.political_outlook"
            :value="examineeInfo.politicalOutlook"
          />
        </td>
      </tr>
      <tr>
        <td>国籍</td>
        <td>{{ examineeInfo.nationality }}</td>
        <td>学历</td>
        <td>
          <dict-tag
            :options="dict.type.education"
            :value="examineeInfo.education"
          />
        </td>
      </tr>
      <tr>
        <td>手机号</td>
        <td>{{ examineeInfo.phoneNumber }}</td>
        <td>所学专业</td>
        <td>
          {{ examineeInfo.specializedSubject }}
          <!-- <dict-tag
            :options="dict.type.specialized_subject"
            :value="examineeInfo.specializedSubject"
          /> -->
        </td>
      </tr>
      <tr>
        <td>毕业院校</td>
        <td>{{ examineeInfo.school }}</td>
        <td>毕业时间</td>
        <td>{{ examineeInfo.graduationTime }}</td>
      </tr>
      <tr>
        <td>专业工作年限</td>
        <td>{{ examineeInfo.professionalWorkTime }}</td>
        <td>通讯地址</td>
        <td>{{ examineeInfo.address }}</td>
      </tr>
      <tr>
        <td>报考所在工作单位</td>
        <td>{{ examineeInfo.oldWork }}</td>
        <td>工作单位</td>
        <td
          :style="
            examineeInfo.oldWork == examineeInfo.work ? {} : { color: 'red' }
          "
        >
          {{ examineeInfo.work }}
        </td>
      </tr>
      <tr>
        <td>报考资质类别</td>
        <td>
          <dict-tag
            :options="dict.type.candidate_type"
            :value="examineeInfo.candidateType"
          />
        </td>
        <td>是否是老考生</td>
        <td>{{ examineeInfo.isoldexaminee }}</td>
      </tr>
      <tr style="background-color: blanchedalmond">
        <td v-if="userId != '02'">备注</td>
        <td v-if="userId != '02'" style="color: red">
          {{ examineeInfo.remark }}
        </td>
        <td>是否完成自动核查</td>
        <td style="color: red">{{ examineeInfo.isAutomaticCheck }}</td>
        <td v-if="userId == '02'"></td>
        <td v-if="userId == '02'"></td>
        <!-- <td></td>
        <td></td> -->
      </tr>
    </table>
    <p class="titleFont">
      <span>*</span>
      以上信息由报考系统同步，除工作单位、手机号外考生无需修改，不影响审核结果。
    </p>
    <PageTitle title="报考信息" />
    <table
      width="100%"
      border="0"
      cellspacing="1"
      cellpadding="4"
      bgcolor="#e9e9e9"
      class="table"
    >
      <tr>
        <td>报考地市</td>
        <td>{{ examineeInfo.cityName }}</td>
        <td>报考专业</td>
        <td>
          {{ examineeInfo.professional }}
          <!-- {{
            selectDictLabel(dict.type.exam_subject, examineeInfo.examSubject) ||
            examineeInfo.examSubject
          }} -->
        </td>
      </tr>
      <tr>
        <td>报考类型</td>
        <td style="color: red">{{ examineeInfo.type }}</td>
        <td>档案号</td>
        <td>{{ examineeInfo.archivesNo }}</td>
      </tr>
    </table>
    <el-table :data="tableData" style="width: 100%" border>
      <el-table-column
        prop="examSubject"
        label="科目"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="examCode"
        label="考号"
        align="center"
      ></el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getInfoById } from "@/api/examinee";
import { getDicts } from "@/api/system/dict/data.js";
export default {
  dicts: [
    "political_outlook",
    "education",
    "nation",
    "specialized_subject",
    "exam_subject",
    "candidate_type",
  ],
  data() {
    return {
      examineeInfo: { idCard: "" },
      tableData: [],
      state: [],
      userId: "",
    };
  },
  created() {
    this.getExamineeInfo();
    this.userId = sessionStorage.getItem("user");
  },
  mounted() {
    this.type();
  },
  methods: {
    getExamineeInfo() {
      getInfoById({
        Id: this.$route.query.examineeId,
      }).then((res) => {
        this.examineeInfo = res.data;
        this.tableData = res.data.examInfoList;
        this.$emit(
          "clickType",
          this.examineeInfo.candidateType,
          this.examineeInfo.idCard
        );
        this.state.forEach((item) => {
          if (item.dictValue == res.data?.auditStatus) {
            this.$emit("clickState", item.dictLabel);
          }
        });
      });
    },
    type() {
      getDicts("audit_status").then((res) => {
        this.state.push(...res.data);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table tr td {
  padding: 10px;
  &:nth-child(odd) {
    width: 150px;
    text-align: right;
    background-color: #f9f9f9;
  }
  &:nth-child(even) {
    width: calc(50% - 150px);
    background-color: #fff;
  }
}
.titleFont {
  font-size: 14px;
  color: red;
}
</style>
