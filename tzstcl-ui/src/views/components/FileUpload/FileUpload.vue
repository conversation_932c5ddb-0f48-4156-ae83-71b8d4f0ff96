<template>
  <el-upload
    ref="upload"
    class="fileUpload"
    accept=".jpg,.jpeg,.png,.pdf"
    :action="uploadUrl"
    :headers="headers"
    :limit="1"
    :file-list="fileList"
    :on-change="handleChange"
    :on-success="handleUploadSuccess"
    :on-error="handleUploadError"
    :on-exceed="handleExceed"
    :on-remove="removeFile"
    :on-preview="showImg"
    :disabled="disabled"
  >
    <!-- :before-upload="beforeUpload" -->
    <el-button v-if="!value && !noUpload" slot="trigger" size="small">
      点击上传
    </el-button>
    <el-image
      v-show="false"
      ref="elImage"
      :preview-src-list="srcList"
    ></el-image>
  </el-upload>
</template>

<script>
import { getToken } from "@/utils/auth";
import { delUp } from "@/api/examinee";
import { downloadFile } from "@/api/file";

export default {
  props: ["value", "disabled", "noUpload"],
  data() {
    return {
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      uploadUrl: process.env.VUE_APP_BASE_API + "/uploadFile/up",
      srcList: [],
      pdfUrl: "",
      dialogVisible: false,
    };
  },
  computed: {
    fileList() {
      return this.value ? [{ name: this.value }] : [];
    },
  },
  methods: {
    showImg(file) {
      console.log(file);
      const fileType = file.name.split(".").pop();
      const flag = ["png", "jpg", "jpeg"].includes(fileType);
      if (flag) {
        downloadFile({ path: file.name }).then((res) => {
          const blob = new Blob([res], {
            type: "application/octet-stream;charset=UTF-8",
          });
          const imageUrl = URL.createObjectURL(blob);
          console.log("URL.createObjectURL(blob)", URL.createObjectURL(blob));
          this.imgUrl = imageUrl;
          this.srcList.push(imageUrl);
          this.$refs.elImage.showViewer = true;
        });
      } else {
        this.dialogVisible = true;
        downloadFile({ path: file.name }).then((res) => {
          var blob = new Blob([res], { type: "application/pdf" });
          var url = URL.createObjectURL(blob);
          this.pdfUrl = url;
          window.open(url);
        });
      }
    },
    // 上传之前 判断文件类型 加水印
    beforeUpload(file) {
      console.log(file);
      if (file.type.includes("image")) {
        const _this = this;
        let dialogImageUrl = window.URL
          ? window.URL.createObjectURL(file)
          : window.webkitURL.createObjectURL(file);
        console.log(dialogImageUrl);
        const img = new Image();
        console.log(img);
        img.src = dialogImageUrl;
        return new Promise((resolve) => {
          img.onload = function () {
            resolve(_this.addWaterMark(img, file.name));
          };
        });
      }
    },
    // 组件的文件列表发生改变
    handleChange(file) {
      if ("response" in file && file.response.code === 200) {
        this.$emit("input", file.response.msg);
      }
    },
    // 删除文件
    removeFile(file) {
      console.log(file);
      this.$emit("input", null);
      delUp({ url: file.name });
    },
    // 上传文件超出限制
    handleExceed() {
      this.$message.error("只能上传1个文件");
    },
    // 收到接口返回数据
    handleUploadSuccess(res, file, fileList) {
      if (res.code !== 200) {
        this.$message({
          message: "上传失败!",
          type: "error",
          duration: 1000,
        });
        fileList.pop();
      } else {
        this.$message({
          message: "上传成功!",
          type: "success",
          duration: 1000,
        });
      }
    },
    // 上传失败提示
    handleUploadError() {
      this.$message({
        message: "上传失败!",
        type: "error",
        duration: 2000,
      });
    },
    addWaterMark(img, fileName) {
      const canvas = document.createElement("canvas");
      const imgWidth = img.width;
      const imgHeight = img.height;
      canvas.width = imgWidth;
      canvas.height = imgHeight;
      const ctx = canvas.getContext("2d");
      ctx.drawImage(img, 0, 0);

      // 平铺水印
      const canvasWater = document.createElement("canvas");
      const waterMarkSize = 300; // 水印大小
      canvasWater.width = waterMarkSize;
      canvasWater.height = waterMarkSize;
      const ctxWater = canvasWater.getContext("2d");
      ctxWater.textAlign = "left";
      ctxWater.textBaseline = "top";
      ctxWater.font = "24px Microsoft Yahei";
      ctxWater.fillStyle = "rgba(235, 235, 235, 0.3)";
      ctxWater.rotate((-20 * Math.PI) / 180);
      ctxWater.fillText("仅用于河南省", 60, 80);
      ctxWater.fillText("建设类职业资格考试审核", 10, 100);
      ctx.fillStyle = ctx.createPattern(canvasWater, "repeat");
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      const base64 = canvas.toDataURL("image/jpeg", 0.8);
      return this.dataURLtoBlob(base64, fileName);
    },
    // base64转文件对象
    dataURLtoBlob(dataurl, name) {
      const arr = dataurl.split(",");
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], name, {
        type: mime,
      });
    },
  },
};
</script>

<style scoped>
.fileUpload /deep/ .el-upload-list {
  margin-top: -40px;
  width: 300px;
}
</style>
