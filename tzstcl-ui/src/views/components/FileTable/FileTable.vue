<template>
  <div>
    <div class="title-btn">
      <PageTitle title="附件" />
    </div>
    <el-table :data="list" border stripe style="width: 100%">
      <el-table-column
        prop="label"
        label="附件类型"
        width="200"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="label"
        label="文件信息"
        show-overflow-tooltip
        header-align="center"
      >
        <template slot-scope="scope">
          <div class="uploadBox" v-if="!isDetail">
            <FileUpload v-model="scope.row.path" />
            <el-button
              type="primary"
              icon="el-icon-download"
              size="mini"
              @click="download(scope.row)"
            >
              下载
            </el-button>
          </div>
          <div v-else>
            <i class="el-icon-paperclip"></i>
            <span
              class="ml10 mr20"
              style="color: #409eff; cursor: pointer"
              @click="showImg(scope.row)"
            >
              {{ scope.row.label }}
            </span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="btnCen" v-if="!isDetail">
      <el-button
        @click="upBtn"
        size="small"
        type="primary"
        style="margin-left: 20px"
      >
        提交修改
      </el-button>
    </div>
    <el-image
      v-show="false"
      ref="elImage"
      :src="imgUrl"
      :preview-src-list="srcList"
    ></el-image>
    <el-dialog :visible.sync="dialogVisible" width="100%">
      <iframe class="boxIframe" :src="pdfUrl"></iframe>
    </el-dialog>
  </div>
</template>

<script>
import { getInfoById } from "@/api/examinee";
import { downloadFile, updateData } from "@/api/file";
import FileUpload from "@/views/components/FileUpload/FileUpload";
export default {
  components: { FileUpload },
  props: ["idCard", "candidateType"],
  data() {
    return {
      isDetail: null,
      list: [],
      labelList: [
        "身份证复印件",
        "学历/学位证书",
        "学历/学位在线验证/认证报告",
        "工作证明（单位盖章）",
        "其他",
      ],

      labelList1: [
        "身份证复印件",
        "学历/学位证书",
        "学历/学位在线验证/认证报告",
        "工作证明（单位盖章）",
        "其它可证明工作年限的材料",
        "其他",
      ],
      labelList2: [
        "身份证复印件",
        "学历/学位证书",
        "学历/学位在线验证/认证报告",
        "工作证明（单位盖章）",
        "其它可证明工作年限的材料",
      ],
      labelListSocial: [
        "身份证复印件",
        "学历/学位证书",
        "学历/学位在线验证/认证报告",
        "工作证明（单位盖章）",
        "其他",
      ],
      labelListSocial1: [
        "身份证复印件",
        "学历/学位证书",
        "学历/学位在线验证/认证报告",
        "工作证明（单位盖章）",
        "其它可证明工作年限的材料",
        "其他",
      ],
      labelListSocial2: [
        "身份证复印件",
        "学历/学位证书",
        "学历/学位在线验证/认证报告",
        "工作证明（单位盖章）",
        "其它可证明工作年限的材料",
      ],
      srcList: [],
      imgUrl: "",
      AId: "",
      pdfUrl: "",
      dialogVisible: false,
    };
  },
  mounted() {
    this.isDetail = this.$route.query.isDetail;
    getInfoById({ Id: this.$route.query.examineeId }).then((res) => {
      const hand = res.data.hand;
      const listLength = res.data.personDataList.length;
      this.$emit("getAuditStatus", res.data.auditList[0]?.auditStatus);
      switch (listLength) {
        // 六条数据
        case 6:
          if (res.data.candidateType == 1) {
            this.list = res.data.personDataList.map((item) => {
              return { ...item, label: this.labelListSocial1[item.type] };
            });
          } else {
            this.list = res.data.personDataList.map((item) => {
              return { ...item, label: this.labelList1[item.type] };
            });
          }
          break;
        default:
          // 没有社保证明
          if (hand == 0) {
            if (res.data.candidateType == 1) {
              this.list = res.data.personDataList.map((item) => {
                return { ...item, label: this.labelListSocial[item.type] };
              });
            } else {
              this.list = res.data.personDataList.map((item) => {
                return { ...item, label: this.labelList[item.type] };
              });
            }
          }
          //有社保证明
          else {
            if (res.data.candidateType == 1) {
              this.list = res.data.personDataList.map((item) => {
                return { ...item, label: this.labelListSocial2[item.type] };
              });
            } else {
              this.list = res.data.personDataList.map((item) => {
                return { ...item, label: this.labelList2[item.type] };
              });
              console.log("this.list", this.list);
            }
          }
          break;
      }
    });
  },
  methods: {
    upBtn() {
      let obj = {
        array: [],
        idCard: this.idCard,
        candidateType: this.candidateType,
      };
      this.list.forEach((item) => {
        obj.array.push(item.path);
      });
      updateData(obj).then((res) => {
        console.log(res);
        if (res.code == 200) {
          this.$message({
            message: "提交成功",
            type: "success",
          });
        }
      });
    },
    download(row) {
      downloadFile(row).then((res) => {
        //location.href = res
        if (res) {
          const blob = new Blob([res], {
            type: "application/octet-stream;charset=UTF-8",
          });
          // 如果后端返回文件名
          if ("download" in document.createElement("a")) {
            // 非IE下载
            const link = document.createElement("a");
            link.download = row.path;
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            document.body.appendChild(link);
            link.click();
            URL.revokeObjectURL(link.href); // 释放URL 对象
            document.body.removeChild(link);
          }
        }
      });
    },
    showImg(row) {
      const fileType = row.path.split(".").pop();
      const flag = ["png", "jpg", "jpeg"].includes(fileType);
      if (flag) {
        downloadFile(row).then((res) => {
          const blob = new Blob([res], {
            type: "application/octet-stream;charset=UTF-8",
          });
          const imageUrl = URL.createObjectURL(blob);
          this.imgUrl = imageUrl;
          this.srcList.push(imageUrl);
          this.$refs.elImage.showViewer = true;
        });
      } else {
        // this.dialogVisible = true;
        downloadFile(row).then((res) => {
          var blob = new Blob([res], { type: "application/pdf" });
          var url = URL.createObjectURL(blob);
          // this.pdfUrl = url;
          window.open(url);
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.boxIframe {
  width: 100%;
  height: 89vh;
}
.el-dialog:not(.is-fullscreen) {
  margin-top: 0vh !important;
}
.uploadBox {
  display: flex;
  align-items: center;
}
.title-btn {
  display: flex;
  align-items: center;
}
.btnCen {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.fileUpload {
  display: flex;
  width: 400px;
  ::v-deep .el-upload-list {
    margin-top: 0;
    .el-upload-list__item:first-child {
      margin-top: 0;
      padding-top: 0;
    }
  }
}
</style>
