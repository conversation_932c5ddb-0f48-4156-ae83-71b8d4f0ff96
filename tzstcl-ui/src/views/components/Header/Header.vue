<template>
  <div class="header-title">
    <p>
      河南省建设类职业资格考试审核
      <el-button
        type="text"
        icon="el-icon-circle-close"
        class="logout"
        @click="logout"
      >
        退出
      </el-button>
    </p>
  </div>
</template>

<script>
export default {
  methods: {
    async logout() {
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.dispatch("LogOut").then(() => {
            window.sessionStorage.removeItem("message");
            location.href = "/index";
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.header-title {
  height: 70px;
  background-color: #409eff;
  padding: 15px;
  p {
    margin: 0;
    font-size: 30px;
    color: #fff;
    font-weight: bold;
  }
  .logout {
    float: right;
    color: #fff;
    font-size: 20px;
    font-weight: bold;
    &:hover {
      font-size: 25px;
    }
  }
}
</style>
