<template>
  <div class="chart" :style="{ minHeight: '500px', width: '100%' }" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  data() {
    return {
      chart: null
    }
  },
  // mounted() {
  //   this.$nextTick(() => {
  //     this.initChart()
  //   })
  // },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart(chartData) {
      const { cityName, sum, noAudit, yesAudit, noAdd } = chartData
      this.chart = echarts.init(this.$el, 'macarons')
      const option = {
        color: ['#6595f7', '#66daac', '#657697', '#f4be38'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          bottom: '0%'
        },
        grid: {
          top: '5%',
          left: '0',
          right: '0',
          bottom: '10%',
          containLabel: true
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid'
            }
          }
        },
        xAxis: {
          data: cityName
        },
        series: [
          {
            name: '全部',
            type: 'bar',
            barWidth: 12,
            barGap: '0',
            data: sum
          },
          {
            name: '待审核',
            type: 'bar',
            barWidth: 12,
            barGap: '0',
            data: noAudit
          },
          {
            name: '已审核',
            type: 'bar',
            barWidth: 12,
            barGap: '0',
            data: yesAudit
          },
          {
            name: '未提交审核',
            type: 'bar',
            barWidth: 12,
            barGap: '0',
            data: noAdd
          }
        ]
      }

      this.chart.setOption(option)
    }
  }
}
</script>
