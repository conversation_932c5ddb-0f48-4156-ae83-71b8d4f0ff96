<template>
  <div class="dialogTitle">{{ title }}</div>
</template>

<script>
export default {
  name: 'PageTitle',
  props: {
    title: {
      type: String,
      default: '页面标题'
    }
  }
}
</script>

<style lang='scss'>
.dialogTitle {
  line-height: 37px;
  padding-left: 10px;
  margin-bottom: 10px;
  position: relative;
  font-weight: bold;
  &::after {
    content: " ";
    display: block;
    background-color: #1890ff;
    width: 4px;
    height: 50%;
    position: absolute;
    left: 0;
    top: 25%;
  }
}
</style>