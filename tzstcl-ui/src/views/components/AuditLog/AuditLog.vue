<template>
  <div>
    <el-tabs
      v-if="tabsShow"
      v-model="type"
      type="card"
      style="margin-bottom: -15px"
    >
      <el-tab-pane
        v-for="(item, i) in auditData"
        :key="item.id"
        :label="item.number"
        :name="i.toString()"
      ></el-tab-pane>
    </el-tabs>
    <el-table :data="auditData[type].auditRecord">
      <el-table-column type="index" width="50" label="序号" align="center" />
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[0].label"
        :prop="columns[0].prop"
        v-if="columns[0].visible"
      ></el-table-column>
      <el-table-column align="center" show-overflow-tooltip label="公司名称">
        <template slot-scope="scope">
          <span :style="scope.row.isSame ? {} : { color: 'red' }">
            {{ scope.row.corpName }}
          </span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[4].label"
        :prop="columns[4].prop"
        v-if="columns[4].visible"
      ></el-table-column> -->
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[1].label"
        :prop="columns[1].prop"
        v-if="columns[1].visible && userType != '02'"
      ></el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[2].label"
        :prop="columns[2].prop"
        v-if="columns[2].visible"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.audit_result"
            :value="scope.row.auditResult"
          />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        show-overflow-tooltip
        :label="columns[3].label"
        :prop="columns[3].prop"
        v-if="columns[3].visible"
      />

      <el-table-column label="附件" show-overflow-tooltip header-align="center">
        <template slot-scope="scope">
          <div class="uploadBox">
            <FileUpload v-model="scope.row.cityFile" :disabled="true" />
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getInfoById } from "@/api/examinee";
import FileUpload from "@/views/components/FileUpload/FileUpload";
export default {
  components: { FileUpload },
  dicts: ["audit_result"],
  data() {
    return {
      tabsShow: false,
      type: "0",
      auditData: [{ auditRecord: [] }],
      columns: [
        { key: 0, label: `提交时间`, prop: "time", visible: true },
        { key: 1, label: `审核人`, prop: "auditPerson", visible: true },
        { key: 2, label: `审核结果`, prop: "auditResult", visible: true },
        { key: 3, label: `审核意见`, prop: "opinion", visible: true },
        { key: 4, label: `公司名称`, prop: "corpName", visible: true },
      ],
      work: "",
      userType: "",
    };
  },
  created() {
    this.userType = sessionStorage.getItem("user");
    getInfoById({ Id: this.$route.query.examineeId }).then((res) => {
      this.work = res.data.work;
      if (!this.work) {
        this.work = "--";
      }
      this.$emit("getAuditId", res.data.auditList[0]?.id);
      if (res.data?.auditList.length > 0) {
        this.auditData = res.data.auditList;
        console.log(1, this.auditData);
        this.tabsShow = true;
      }
    });
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
::v-deep .el-upload-list__item:first-child {
  margin-top: 20px;
}
</style>
