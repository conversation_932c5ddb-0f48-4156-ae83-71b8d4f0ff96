<template>
  <!-- 选择企业对话框 -->
  <el-dialog
    :visible.sync="dialogVisible"
    width="650px"
    append-to-body
    custom-class="enterPriseDialog"
  >
    <template slot="title">
      <el-row>
        <el-col :span="6">
          <div class="dialogTitle">选择企业</div>
        </el-col>
        <el-col :span="18">
          <el-form
            :model="queryParams"
            ref="queryForm"
            :inline="true"
            class="queryForm"
          >
            <el-form-item style="margin-bottom: 0">
              <el-input
                v-model="queryParams.corpName"
                placeholder="请输入企业名称"
                clearable
                size="small"
              />
            </el-form-item>
            <el-form-item style="margin-bottom: 0">
              <el-button
                type="primary"
                icon="el-icon-search"
                size="small"
                @click="handleQuery"
              >
                搜索
              </el-button>
              <el-button
                icon="el-icon-refresh"
                size="small"
                @click="resetQuery"
              >
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </template>
    <!-- 企业列表 -->
    <el-table
      ref="eltable"
      :data="list"
      :header-cell-class-name="cellClass"
      highlight-current-row
      @select="(selected, row) => rowClick(row)"
      @row-click="rowClick"
    >
      <el-table-column align="center" type="selection" width="55" fixed />
      <el-table-column align="center" property="corpName" label="企业名称" />
      <el-table-column
        align="center"
        property="corpCode"
        label="统一社会信用代码"
      />
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :pagerCount="3"
      @pagination="getList"
      class="mb20"
    />

    <!-- <div class="item_show" @click.self="showForm = !showForm">
      <span @click="showForm = !showForm">
        列表中找不到对应的企业信息？请点击此处手动录入
      </span>
      <el-button
        :icon="`el-icon-arrow-${showForm ? 'down' : 'right'}`"
        circle
        style="color: #fff"
        @click="showForm = !showForm"
      ></el-button>
    </div> -->
    <div style="display: flex; justify-content: center">
      <div class="manual">
        列表中找不到对应的企业信息,可点击此处进行
        <span style="color: #409eff; cursor: pointer" @click="clickShow">
          手动录入
        </span>
      </div>
    </div>
    <el-form
      v-show="showForm"
      :model="formData"
      ref="elForm"
      label-width="135px"
      style="margin-top: 18px"
    >
      <el-form-item
        label="企业名称"
        prop="corpName"
        :rules="{
          required: true,
          message: '企业名称不能为空',
          trigger: 'blur',
        }"
      >
        <el-input
          v-model="formData.corpName"
          placeholder="'请输入企业名称' "
          style="width: 300px"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="统一社会信用代码"
        prop="corpCode"
        :rules="{
          validator: CheckSocialCreditCode,
          trigger: 'blur',
        }"
      >
        <el-input
          v-model="formData.corpCode"
          placeholder="请输入企业统一社会信用代码"
          style="width: 300px"
        ></el-input>
      </el-form-item>
      <p>手动录入后,请资料上传页面上传符合报考工作年限的社保缴纳记录等证明</p>
    </el-form>

    <!-- 手动 -->
    <!-- <div class="manual">
      列表中找不到对应的企业信息,可点击此处进行
      <span style="color: #409eff" @click="isShow = !isShow">手动录入</span>
    </div> -->
    <!-- <div class="enterprise" v-show="isShow">
      <el-form label-width="150px" style="margin-top: 10px">
        <el-form-item label="企业名称">
          <el-input placeholder="请输入企业名称" style="width: 300px" />
        </el-form-item>
        <el-form-item label="统一社会信用代码">
          <el-input
            placeholder="请输入企业统一社会信用代码"
            style="width: 300px"
          />
        </el-form-item>
      </el-form>
      <p>手动录入后,请资料上传页面上传符合报考工作年限的社保缴纳记录等证明</p>
    </div> -->
    <!-- 操作按钮 -->
    <div slot="footer" class="center">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getCompanyInfoList } from "@/api/examinee";

import listMixin from "@/mixin/listMixin";

import { CheckSocialCreditCode } from "@/utils/validate";
export default {
  props: {
    show: {
      require: true,
      type: Boolean,
    },
  },
  mixins: [listMixin],
  data() {
    return {
      isShow: false,
      dialogVisible: false,
      listAPI: getCompanyInfoList,
      queryParams: {
        corpName: null,
      },
      showForm: false,
      formData: { hand: 1 },
      CheckSocialCreditCode,
      selected: null,
    };
  },
  watch: {
    // 观察父级组件的show，并将showFlag的最新值设置给dialogVisible
    show(newVal, oldVal) {
      this.dialogVisible = newVal;
    },
    // 子组件关闭dialog时，通知父组件同步
    dialogVisible(newVal, oldVal) {
      if (!newVal) this.$emit("update:show", false);
    },
    list() {
      this.selected = null;
    },
  },
  methods: {
    // 给全选column 添加 class
    cellClass(row) {
      if (row.columnIndex === 0) {
        return "DisableSelection";
      }
    },
    rowClick(val) {
      this.showForm = false;
      this.$refs.eltable.clearSelection();
      this.$refs.eltable.toggleRowSelection(val);
      this.selected = val;
    },

    confirm() {
      if (this.showForm) {
        this.$refs["elForm"].validate((valid) => {
          if (valid) {
            this.dialogVisible = false;
            this.$emit("confirm", this.formData);
          }
        });
      } else {
        this.dialogVisible = false;
        this.selected = { ...this.selected, hand: 0 };
        this.$emit("confirm", this.selected);
      }
    },
    cancel() {
      this.dialogVisible = false;
    },
    clickShow() {
      this.showForm = !this.showForm;
      this.$refs.eltable.clearSelection();
    },
  },
};
</script>

<style lang="scss" scope>
.enterPriseDialog {
  .dialogTitle {
    line-height: 37px;
    padding-left: 10px;
    position: relative;
    &::after {
      content: " ";
      display: block;
      background-color: #1890ff;
      width: 4px;
      height: 50%;
      position: absolute;
      left: 0;
      top: 25%;
    }
  }
  .queryForm {
    display: flex;
    justify-content: flex-end;
    padding-right: 20px;
  }
  .el-dialog__body {
    padding: 0 20px;
  }
  .center {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-top: 20px;
  }
}
// 隐藏全选框
.DisableSelection > .cell {
  display: none !important;
}
.item_show {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  width: 100%;
  margin-bottom: 10px;
  background-color: #1890ff;
  color: #fff;
  padding-left: 20px;
  .el-button {
    background-color: transparent;
  }
}
.manual {
  margin-top: 40px;
}
</style>
