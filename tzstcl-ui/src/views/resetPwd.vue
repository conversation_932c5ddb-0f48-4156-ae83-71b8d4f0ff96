<template>
  <div class="register">
    <el-form
      ref="registerForm"
      :model="registerForm"
      :rules="registerRules"
      class="register-form"
      label-width="auto"
    >
      <h3 class="title">忘记密码</h3>
      <el-form-item label="姓名" prop="nickName">
        <el-input
          v-model="registerForm.nickName"
          type="text"
          auto-complete="off"
          placeholder="姓名"
        ></el-input>
      </el-form-item>
      <el-form-item label="身份证号" prop="userName">
        <el-input
          v-model="registerForm.userName"
          type="text"
          auto-complete="off"
          placeholder="身份证号"
        ></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="phoneNumber">
        <el-input
          v-model="registerForm.phoneNumber"
          type="text"
          auto-complete="off"
          placeholder="手机号"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input
          v-model="registerForm.password"
          type="password"
          auto-complete="off"
          placeholder="密码"
          clearable
          @keyup.enter.native="handleRegister"
        ></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model="registerForm.confirmPassword"
          type="password"
          auto-complete="off"
          placeholder="确认密码"
          clearable
          @keyup.enter.native="handleRegister"
        ></el-input>
      </el-form-item>
      <el-form-item style="width: 100%" label-width="0">
        <el-button
          :disabled="disabled"
          size="medium"
          type="primary"
          style="width: 100%"
          @click.native.prevent="handleRegister"
        >
          <span>重置密码</span>
        </el-button>
        <div style="float: right">
          <router-link class="link-type" :to="'/login'">
            使用已有账户登录
          </router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-register-footer">
      <span>Copyright © 天筑科技股份有限公司 All Rights Reserved.</span>
    </div>
  </div>
</template>

<script>
import { resetPwd } from "@/api/login";
import { idCardValid } from "@/utils/validate";

export default {
  name: "Register",
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.registerForm.password !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      registerForm: {
        nickName: "",
        userName: "",
        phoneNumber: "",
        password: "",
        confirmPassword: "",
      },
      registerRules: {
        nickName: [
          { required: true, trigger: "blur", message: "请输入您的姓名" },
        ],
        userName: [
          { required: true, message: "请输入您的身份证号" },
          { validator: idCardValid },
        ],
        phoneNumber: [
          { required: true, message: "请输入您的手机号" },
          { pattern: /^1\d{10}$/, message: "手机号格式不正确" },
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" },

          {
            pattern:
              /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&.])[A-Za-z\d@$!%*?&.]{8,20}$/,
            message:
              "密码必须包含（大小写字母，数字，特殊字符@$!%*?&.），长度在 8 到 20 个字符之间",
            trigger: "blur",
          },
        ],
        confirmPassword: [
          { required: true, trigger: "blur", message: "请再次输入您的密码" },
          { required: true, validator: equalToPassword, trigger: "blur" },
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }],
      },
      disabled: false,
    };
  },
  methods: {
    handleRegister() {
      this.$refs.registerForm.validate((valid, result) => {
        if (valid) {
          this.disabled = true;
          resetPwd(this.registerForm)
            .then((res) => {
              this.$alert(
                "<font color='red'>" + res.msg + "</font>",
                "系统提示",
                {
                  dangerouslyUseHTMLString: true,
                  type: "success",
                }
              )
                .then(() => {
                  this.$router.push("/login");
                })
                .catch(() => {});
            })
            .catch(() => {
              this.disabled = false;
            });
        }
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.register {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.png");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.register-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.register-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.register-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-register-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  // color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.register-code-img {
  height: 38px;
}
.el-form-item {
  margin-bottom: 30px;
}
</style>
