package com.tzstcl.common.utils;

/**
 * 判断是否是弱密码
 *
 * <AUTHOR> on 2023/11/21
 */
public class PasswordUtils {
    public static boolean isWeakPassword(String password){
        // 检查密码长度
        if (password.length() < 8) {
            return true;
        }

        // 检查是否包含数字
        boolean hasDigit = false;
        // 检查是否包含字母
        boolean hasLetter = false;
        // 检查是否包含特殊字符
        boolean hasSpecialChar = false;

        for (char c : password.toCharArray()) {
            if (Character.isDigit(c)) {
                hasDigit = true;
            } else if (Character.isLetter(c)) {
                hasLetter = true;
            } else {
                hasSpecialChar = true;
            }
        }

        // 检查是否同时包含数字、字母和特殊字符
        if (hasDigit && hasLetter && hasSpecialChar) {
            return false;
        } else {
            return true;
        }
    }

    public static void main(String[] args) {
        System.out.println(SecurityUtils.encryptPassword("123456789"));
    }

}
