package com.tzstcl.common.utils;

import com.github.pagehelper.PageHelper;
import com.tzstcl.common.constant.HttpStatus;
import com.tzstcl.common.core.page.PageDomain;
import com.tzstcl.common.core.page.TableDataInfo;
import com.tzstcl.common.core.page.TableSupport;
import com.tzstcl.common.utils.sql.SqlUtil;

import java.util.List;

/**
 * 分页工具类
 * 
 * <AUTHOR>
 */
public class PageUtils extends PageHelper
{
    /**
     * 设置请求分页数据
     */
    public static void startPage()
    {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        Boolean reasonable = pageDomain.getReasonable();
        PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
    }

    /**
     * 手动分页
     * @param l 待处理列表
     * @param <T>
     * @return 分页处理后列表
     */
    public static <T> TableDataInfo projectPage(List<T> l) {
        TableDataInfo rspData = new TableDataInfo();
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        int count;
        int fromIndex = 0;
        int toIndex = 0;
        if (l != null && l.size() > 0) {
            count = l.size();
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = pageNum * pageSize;
            if (toIndex > count) {
                toIndex = count;
            }
        }
        List pageList = l.subList(fromIndex, toIndex);

        if (pageNum != null && pageSize != null) {
            rspData.setRows(pageList);
        }else {
            rspData.setRows(l);
        }
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setTotal(l.size());
        return rspData;
    }

    /**
     * 清理分页的线程变量
     */
    public static void clearPage()
    {
        PageHelper.clearPage();
    }
}
