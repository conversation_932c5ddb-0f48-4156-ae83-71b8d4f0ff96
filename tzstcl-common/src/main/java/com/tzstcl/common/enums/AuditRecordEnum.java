package com.tzstcl.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Desc:
 * @Author: chajing<PERSON>
 * @Date:2023/2/16
 * @since 1.8
 **/
@AllArgsConstructor
public enum AuditRecordEnum {

    NO_ADD_AUDIT(0, "未参加审核"),

    NO_AUDIT(1, "未审核"),

    AUDITED(2, "已审核"),

    OTHER(3,"其他")

    ;

    @Getter
    private final Integer code;

    @Getter
    private final String name;
}
