package com.tzstcl.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Desc:
 * @Author: chajing<PERSON>
 * @Date:2023/2/16
 * @since 1.8
 **/
@AllArgsConstructor
public enum AuditStatusEnum {

    NO_ADD_AUDIT(0, "未参加审核"),

    NO_AUDIT(1, "未审核"),

    AUDITED(2, "已审核"),

    OTHER(3,"其他"),
    AUDIT_NOT_PASSED(4,"审核不通过"),
    WAIT_CORP_AUDIT(5,"待企业审核"),

    OTADD_AUDITHER(6,"已参加审核"),

    NEED_TO_SUBMIT_INFORMATION(7,"需要补交资料")

    ;

    @Getter
    private final Integer code;

    @Getter
    private final String name;
}
