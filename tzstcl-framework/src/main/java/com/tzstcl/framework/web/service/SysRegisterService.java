package com.tzstcl.framework.web.service;

import com.tzstcl.common.utils.PasswordUtils;
import com.tzstcl.person.domain.PersonInfo;
import com.tzstcl.person.service.IPersonInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.tzstcl.common.constant.CacheConstants;
import com.tzstcl.common.constant.Constants;
import com.tzstcl.common.constant.UserConstants;
import com.tzstcl.common.core.domain.entity.SysUser;
import com.tzstcl.common.core.redis.RedisCache;
import com.tzstcl.common.exception.user.CaptchaException;
import com.tzstcl.common.exception.user.CaptchaExpireException;
import com.tzstcl.common.utils.MessageUtils;
import com.tzstcl.common.utils.SecurityUtils;
import com.tzstcl.common.utils.StringUtils;
import com.tzstcl.framework.manager.AsyncManager;
import com.tzstcl.framework.manager.factory.AsyncFactory;
import com.tzstcl.system.service.ISysConfigService;
import com.tzstcl.system.service.ISysUserService;

/**
 * 注册校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysRegisterService
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;
    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private IPersonInfoService personInfoService;

    /**
     * 注册
     */
    public String register(SysUser registerBody)
    {
        String msg = "", username = registerBody.getUserName(), password = registerBody.getPassword();

        if (StringUtils.isEmpty(username))
        {
            msg = "用户名不能为空";
        }
        else if (StringUtils.isEmpty(password))
        {
            msg = "用户密码不能为空";
        }
        else if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            msg = "账户长度必须在2到20个字符之间";
        }
        else if (PasswordUtils.isWeakPassword(password)){
            //判断是否是弱密码
            msg = "注册失败'" + username + "失败，密码强度太弱，密码必须包含大写字母、小写字母、特殊符号和数字，长度在 8 到 16 个字符之间";
        }
        else if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(registerBody)))
        {
            msg = "保存用户'" + username + "'失败，注册账号已存在";
        }
        else
        {
            Long [] roleIds = {4L};
            registerBody.setPassword(SecurityUtils.encryptPassword(registerBody.getPassword()));
            registerBody.setRoleIds(roleIds);
            registerBody.setUserType("03");
            boolean regFlag = userService.registerUser(registerBody);
            sysUserService.insertUserAuth(registerBody.getUserId(),roleIds);
            //更新手机号到考生信息
            PersonInfo info = personInfoService.checkRegister(registerBody.getUserName());
            if(info!=null){
                info.setPhoneNumber(registerBody.getPhonenumber());
                personInfoService.updatePersonInfo(info);
            }
            if (!regFlag)
            {
                msg = "注册失败,请联系系统管理人员";
            }
            else
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.REGISTER, MessageUtils.message("user.register.success")));
            }
        }

        return msg;
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid)
    {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null)
        {
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha))
        {
            throw new CaptchaException();
        }
    }
}
