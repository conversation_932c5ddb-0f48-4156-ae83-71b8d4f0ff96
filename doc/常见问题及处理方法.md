#### 常见问题

1. **考生账号开启关闭：**

   查询需要关闭的考生批次，去服务器navicat中找到停用账号的查**询，将该批次账号状态改为1（status->1），然后将该批次中未参加审核的人的注册状态改为禁止注册（registerStatus->1 where auditstatus = 9），如果存在第14批和第15批同时在审核，在关闭第14批的账号后，需要再次执行第15批的启用sql，因为第15批中可能存在第14批的账号用户存在。**

2. **主管部门要用不同的账号审核不同批次或类型的考生：**

   代码中//todo 有标注，找到标注（创建审核账号需修改）的代码段，selectauditList方法是考后审核菜单的查询列表，**（注意usertype在页面上创建用户的时候默认为01，根据需求要从数据库修改）**，还有generateAuditStatusIndex方法是页面首页模块中的数量统计，此方法不区分usertype。

3. **分配考生：**

   默认根据该用户部门中的人数，用雪花算法生成的id的倒数第二位对人数取余，根据余数分配给用户。

   ```  
   //根据考生id分配给不同的用户   先查询出所有的用户id，然后根据id%num添加到返回集合中
   ```

   在selectauditList方法中，目前只有地市部门，即usetype=04的主管部门账号进行考生分配，分配方法allotPersonByDept。

   如果有需求某个用户要查询所有考生，不进行分配用户，则在allotPersonByDept方法调用之前设置

   ```
   personInfo.setAuditTotal(1);
   personInfo.setIndexTotal(0);
   ```

   如果该用户也在需要分配的主管部门用户部门下，那么其他用户需要重新设置

   ```
   //-1 是减去 不需要分配的哪个用户
   personInfo.setAuditTotal(total-1);
   // n应该是从0开始到 total-1 分别设置
   personInfo.setIndexTotal(n);
   ```

 4. **审核流程：**

    考生信息excel导入->考生注册->考生提交->根据考生考区各地市主管部门进行审核

    以上是默认流程，但省厅由于某些地方有中介参与的原因，考生人数少的时候会直接让创建省厅的账号直接由省厅审核，不交由地市。**（创建省厅的账号也可以设置usertype=04的地市角色-每一个审核的账号都设置这个角色）**

 5. **考生信息导入：**

    考生信息由省厅提供excel，根据excel的表头，/person/info/importData接口方法，根据不同的考试类型走不同的导入方法，字段对应使用实体类中的@Excel注解，***（工作单位、手机号不导入），最终要给批次inputType+1***

 6. **旧考生角色问题：**

    有一部分考生在早期注册过账号，后续再次参加审核的时候启用了旧帐号~~，但是user_role表中丢失了角色，需要在服务器的navicat中找到关于角色问题的查询，执行sql给这些考生手动添加角色~~

 7. **企业无法登录问题：**

    可能是四库或者资格后审系统创建了两条用户信息。排查这两个系统直接删除一条即可